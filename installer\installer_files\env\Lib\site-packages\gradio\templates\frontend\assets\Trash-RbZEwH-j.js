const{SvelteComponent:x,append:n,attr:t,detach:f,init:u,insert:v,noop:g,safe_not_equal:m,svg_element:l,text:V}=window.__gradio__svelte__internal;function y(p){let e,c,h,_,s,a,o,i,r;return{c(){e=l("svg"),c=l("defs"),h=l("style"),_=V(`.cls-1 {
				fill: none;
			}`),s=l("rect"),a=l("rect"),o=l("path"),i=l("rect"),r=l("rect"),t(s,"x","12"),t(s,"y","12"),t(s,"width","2"),t(s,"height","12"),t(a,"x","18"),t(a,"y","12"),t(a,"width","2"),t(a,"height","12"),t(o,"d","M4,6V8H6V28a2,2,0,0,0,2,2H24a2,2,0,0,0,2-2V8h2V6ZM8,28V8H24V28Z"),t(i,"x","12"),t(i,"y","2"),t(i,"width","8"),t(i,"height","2"),t(r,"id","_Transparent_Rectangle_"),t(r,"data-name","<Transparent Rectangle>"),t(r,"class","cls-1"),t(r,"width","32"),t(r,"height","32"),t(e,"id","icon"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"viewBox","0 0 32 32"),t(e,"fill","currentColor"),t(e,"width","100%"),t(e,"height","100%")},m(d,w){v(d,e,w),n(e,c),n(c,h),n(h,_),n(e,s),n(e,a),n(e,o),n(e,i),n(e,r)},p:g,i:g,o:g,d(d){d&&f(e)}}}class T extends x{constructor(e){super(),u(this,e,null,y,m,{})}}export{T};
//# sourceMappingURL=Trash-RbZEwH-j.js.map
