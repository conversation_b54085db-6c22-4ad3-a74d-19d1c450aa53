const __vite__fileDeps=["./vega-embed.module-p0L_PgLp.js","./index-BkoKOheB.js","./index-D_bOYnap.css","./dsv-DB8NKgIY.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{_ as Ve}from"./index-BkoKOheB.js";import{B as je}from"./Block-CB3nIXHA.js";import{B as Ie}from"./BlockTitle-IUerRYnJ.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{E as Be}from"./Empty-B_fwEKaS.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{L as De}from"./LineChart-CKh1Fdep.js";import{S as Ne}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./svelte/svelte.js";import"./Info-QztxykAE.js";import"./MarkdownCode-DYQlapxH.js";import"./prism-python-B8dcvKZU.js";import"./IconButton-B3BI2i6T.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:qe,append:Me,assign:Re,attr:we,binding_callbacks:He,check_outros:be,create_component:q,destroy_component:M,detach:W,element:Fe,empty:Pe,flush:_,get_spread_object:Ue,get_spread_update:Xe,group_outros:ye,init:Ye,insert:L,mount_component:R,noop:xe,safe_not_equal:Ge,set_data:ze,space:se,text:Se,transition_in:w,transition_out:z}=window.__gradio__svelte__internal,{onMount:Je}=window.__gradio__svelte__internal;function ve(o){let e,l;const i=[{autoscroll:o[3].autoscroll},{i18n:o[3].i18n},o[11]];let n={};for(let s=0;s<i.length;s+=1)n=Re(n,i[s]);return e=new Ne({props:n}),e.$on("clear_status",o[40]),{c(){q(e.$$.fragment)},m(s,u){R(e,s,u),l=!0},p(s,u){const d=u[0]&2056?Xe(i,[u[0]&8&&{autoscroll:s[3].autoscroll},u[0]&8&&{i18n:s[3].i18n},u[0]&2048&&Ue(s[11])]):{};e.$set(d)},i(s){l||(w(e.$$.fragment,s),l=!0)},o(s){z(e.$$.fragment,s),l=!1},d(s){M(e,s)}}}function Ke(o){let e;return{c(){e=Se(o[4])},m(l,i){L(l,e,i)},p(l,i){i[0]&16&&ze(e,l[4])},d(l){l&&W(e)}}}function Qe(o){let e,l;return e=new Be({props:{unpadded_box:!0,$$slots:{default:[pe]},$$scope:{ctx:o}}}),{c(){q(e.$$.fragment)},m(i,n){R(e,i,n),l=!0},p(i,n){const s={};n[1]&33554432&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){l||(w(e.$$.fragment,i),l=!0)},o(i){z(e.$$.fragment,i),l=!1},d(i){M(e,i)}}}function Ze(o){let e,l,i,n=o[2]&&ke(o);return{c(){e=Fe("div"),l=se(),n&&n.c(),i=Pe(),we(e,"class","svelte-10k9m4v")},m(s,u){L(s,e,u),o[41](e),L(s,l,u),n&&n.m(s,u),L(s,i,u)},p(s,u){s[2]?n?n.p(s,u):(n=ke(s),n.c(),n.m(i.parentNode,i)):n&&(n.d(1),n=null)},i:xe,o:xe,d(s){s&&(W(e),W(l),W(i)),o[41](null),n&&n.d(s)}}}function pe(o){let e,l;return e=new De({}),{c(){q(e.$$.fragment)},m(i,n){R(e,i,n),l=!0},i(i){l||(w(e.$$.fragment,i),l=!0)},o(i){z(e.$$.fragment,i),l=!1},d(i){M(e,i)}}}function ke(o){let e,l;return{c(){e=Fe("p"),l=Se(o[2]),we(e,"class","caption svelte-10k9m4v")},m(i,n){L(i,e,n),Me(e,l)},p(i,n){n[0]&4&&ze(l,i[2])},d(i){i&&W(e)}}}function $e(o){let e,l,i,n,s,u,d,r=o[11]&&ve(o);l=new Ie({props:{root:o[1],show_label:o[8],info:void 0,$$slots:{default:[Ke]},$$scope:{ctx:o}}});const g=[Ze,Qe],c=[];function f(a,m){return a[0]&&a[14]?0:1}return n=f(o),s=c[n]=g[n](o),{c(){r&&r.c(),e=se(),q(l.$$.fragment),i=se(),s.c(),u=Pe()},m(a,m){r&&r.m(a,m),L(a,e,m),R(l,a,m),L(a,i,m),c[n].m(a,m),L(a,u,m),d=!0},p(a,m){a[11]?r?(r.p(a,m),m[0]&2048&&w(r,1)):(r=ve(a),r.c(),w(r,1),r.m(e.parentNode,e)):r&&(ye(),z(r,1,1,()=>{r=null}),be());const F={};m[0]&2&&(F.root=a[1]),m[0]&256&&(F.show_label=a[8]),m[0]&16|m[1]&33554432&&(F.$$scope={dirty:m,ctx:a}),l.$set(F);let S=n;n=f(a),n===S?c[n].p(a,m):(ye(),z(c[S],1,1,()=>{c[S]=null}),be(),s=c[n],s?s.p(a,m):(s=c[n]=g[n](a),s.c()),w(s,1),s.m(u.parentNode,u))},i(a){d||(w(r),w(l.$$.fragment,a),w(s),d=!0)},o(a){z(r),z(l.$$.fragment,a),z(s),d=!1},d(a){a&&(W(e),W(i),W(u)),r&&r.d(a),M(l,a),c[n].d(a)}}}function et(o){let e,l;return e=new je({props:{visible:o[7],elem_id:o[5],elem_classes:o[6],scale:o[9],min_width:o[10],allow_overflow:!1,padding:!0,height:o[12],$$slots:{default:[$e]},$$scope:{ctx:o}}}),{c(){q(e.$$.fragment)},m(i,n){R(e,i,n),l=!0},p(i,n){const s={};n[0]&128&&(s.visible=i[7]),n[0]&32&&(s.elem_id=i[5]),n[0]&64&&(s.elem_classes=i[6]),n[0]&512&&(s.scale=i[9]),n[0]&1024&&(s.min_width=i[10]),n[0]&4096&&(s.height=i[12]),n[0]&10527|n[1]&33554432&&(s.$$scope={dirty:n,ctx:i}),e.$set(s)},i(i){l||(w(e.$$.fragment,i),l=!0)},o(i){z(e.$$.fragment,i),l=!1},d(i){M(e,i)}}}function tt(o,e,l){let i,n,s,u,d,{value:r}=e,{x:g}=e,{y:c}=e,{color:f=null}=e,{root:a}=e,{title:m=null}=e,{x_title:F=null}=e,{y_title:S=null}=e,{color_title:$=null}=e,{x_bin:P=null}=e,{y_aggregate:B=void 0}=e,{color_map:H=null}=e,{x_lim:V=null}=e,{y_lim:U=null}=e,{x_label_angle:X=null}=e,{y_label_angle:Y=null}=e,{x_axis_labels_visible:G=!0}=e,{caption:oe=null}=e,{sort:ee=null}=e,{tooltip:A="axis"}=e;function Ae(t){if(t==="x")return"ascending";if(t==="-x")return"descending";if(t==="y")return{field:c,order:"ascending"};if(t==="-y")return{field:c,order:"descending"};if(t===null)return;if(Array.isArray(t))return t}let{_selectable:J=!1}=e,K,{gradio:j}=e,E,te=!1;const Ee={s:1,m:60,h:60*60,d:24*60*60};let D,I;function Ce(t){if(A=="all"||Array.isArray(A))return t.data.map(v=>{const h={};return t.columns.forEach((p,N)=>{h[p]=v[N]}),h});let y=t.columns.indexOf(g),O=t.columns.indexOf(c),x=f?t.columns.indexOf(f):null;return t.data.map(v=>{const h={[g]:v[y],[c]:v[O]};return f&&x!==null&&(h[f]=v[x]),h})}const Oe=typeof window<"u";let b,C,le=!1,re,Q,ie;async function ae(){if(C&&C.finalize(),!r||!b)return;re=b.offsetWidth;const t=We();t&&(Q=new ResizeObserver(y=>{!y[0].target||!(y[0].target instanceof HTMLElement)||(re===0&&b.offsetWidth!==0&&r.datatypes[g]==="nominal"?ae():C.signal("width",y[0].target.offsetWidth).run())}),ie||(ie=(await Ve(()=>import("./vega-embed.module-p0L_PgLp.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)).default),ie(b,t,{actions:!1}).then(function(y){C=y.view,Q.observe(b);var O;C.addEventListener("dblclick",()=>{j.dispatch("double_click")}),b.addEventListener("mousedown",function(x){x.detail>1&&x.preventDefault()},!1),J&&C.addSignalListener("brush",function(x,v){if(Object.keys(v).length===0)return;clearTimeout(O);let h=v[Object.keys(v)[0]];s&&(h=[h[0]/1e3,h[1]/1e3]),te?Z=()=>{j.dispatch("select",{value:h,index:h,selected:!0})}:O=setTimeout(function(){j.dispatch("select",{value:h,index:h,selected:!0})},250)})}))}let Z=null;Je(()=>(l(37,le=!0),b.addEventListener("mousedown",()=>{te=!0}),b.addEventListener("mouseup",()=>{te=!1,Z&&(Z(),Z=null)}),()=>{l(37,le=!1),C&&C.finalize(),Q&&Q.disconnect()}));function We(){if(!r||!d)return null;let t=d.getPropertyValue("--color-accent"),y=d.getPropertyValue("--body-text-color"),O=d.getPropertyValue("--border-color-primary"),x=d.fontFamily,v=d.getPropertyValue("--block-title-text-weight");const h=T=>T.endsWith("px")?parseFloat(T.slice(0,-2)):12;let p=h(d.getPropertyValue("--text-md")),N=h(d.getPropertyValue("--text-sm"));return{$schema:"https://vega.github.io/schema/vega-lite/v5.17.0.json",background:"transparent",config:{autosize:{type:"fit",contains:"padding"},axis:{labelFont:x,labelColor:y,titleFont:x,titleColor:y,titlePadding:8,tickColor:O,labelFontSize:N,gridColor:O,titleFontWeight:"normal",titleFontSize:N,labelFontWeight:"normal",domain:!1,labelAngle:0},legend:{labelColor:y,labelFont:x,titleColor:y,titleFont:x,titleFontWeight:"normal",titleFontSize:N,labelFontWeight:"normal",offset:2},title:{color:y,font:x,fontSize:p,fontWeight:v,anchor:"middle"},view:{stroke:O},mark:{stroke:r.mark!=="bar"?t:void 0,fill:r.mark==="bar"?t:void 0,cursor:"crosshair"}},data:{name:"data"},datasets:{data:K},layer:["plot",...r.mark==="line"?["hover"]:[]].map(T=>({encoding:{size:r.mark==="line"?T=="plot"?{condition:{empty:!1,param:"hoverPlot",value:3},value:2}:{condition:{empty:!1,param:"hover",value:100},value:0}:void 0,opacity:T==="plot"?void 0:{condition:{empty:!1,param:"hover",value:1},value:0},x:{axis:{...X!==null&&{labelAngle:X},labels:G,ticks:G},field:g,title:F||g,type:r.datatypes[g],scale:u?{domain:u}:void 0,bin:E?{step:E}:void 0,sort:n},y:{axis:Y?{labelAngle:Y}:{},field:c,title:S||c,type:r.datatypes[c],scale:U?{domain:U}:void 0,aggregate:I?D:void 0},color:f?{field:f,legend:{orient:"bottom",title:$},scale:r.datatypes[f]==="nominal"?{domain:i,range:H?i.map(k=>H[k]):void 0}:{range:[100,200,300,400,500,600,700,800,900].map(k=>d.getPropertyValue("--primary-"+k)),interpolate:"hsl"},type:r.datatypes[f]}:void 0,tooltip:A=="none"?void 0:[{field:c,type:r.datatypes[c],aggregate:I?D:void 0,title:S||c},{field:g,type:r.datatypes[g],title:F||g,format:s?"%Y-%m-%d %H:%M:%S":void 0,bin:E?{step:E}:void 0},...f?[{field:f,type:r.datatypes[f]}]:[],...A==="axis"?[]:r?.columns.filter(k=>k!==g&&k!==c&&k!==f&&(A==="all"||A.includes(k))).map(k=>({field:k,type:r.datatypes[k]}))]},strokeDash:{},mark:{clip:!0,type:T==="hover"?"point":r.mark},name:T})),params:[...r.mark==="line"?[{name:"hoverPlot",select:{clear:"mouseout",fields:f?[f]:[],nearest:!0,on:"mouseover",type:"point"},views:["hover"]},{name:"hover",select:{clear:"mouseout",nearest:!0,on:"mouseover",type:"point"},views:["hover"]}]:[],...J?[{name:"brush",select:{encodings:["x"],mark:{fill:"gray",fillOpacity:.3,stroke:"none"},type:"interval"},views:["plot"]}]:[]],width:b.offsetWidth,title:m||void 0}}let{label:_e="Textbox"}=e,{elem_id:ue=""}=e,{elem_classes:ce=[]}=e,{visible:fe=!0}=e,{show_label:me}=e,{scale:de=null}=e,{min_width:ge=void 0}=e,{loading_status:ne=void 0}=e,{height:he=void 0}=e;const Le=()=>j.dispatch("clear_status",ne);function Te(t){He[t?"unshift":"push"](()=>{b=t,l(13,b)})}return o.$$set=t=>{"value"in t&&l(0,r=t.value),"x"in t&&l(15,g=t.x),"y"in t&&l(16,c=t.y),"color"in t&&l(17,f=t.color),"root"in t&&l(1,a=t.root),"title"in t&&l(18,m=t.title),"x_title"in t&&l(19,F=t.x_title),"y_title"in t&&l(20,S=t.y_title),"color_title"in t&&l(21,$=t.color_title),"x_bin"in t&&l(22,P=t.x_bin),"y_aggregate"in t&&l(23,B=t.y_aggregate),"color_map"in t&&l(24,H=t.color_map),"x_lim"in t&&l(25,V=t.x_lim),"y_lim"in t&&l(26,U=t.y_lim),"x_label_angle"in t&&l(27,X=t.x_label_angle),"y_label_angle"in t&&l(28,Y=t.y_label_angle),"x_axis_labels_visible"in t&&l(29,G=t.x_axis_labels_visible),"caption"in t&&l(2,oe=t.caption),"sort"in t&&l(30,ee=t.sort),"tooltip"in t&&l(31,A=t.tooltip),"_selectable"in t&&l(32,J=t._selectable),"gradio"in t&&l(3,j=t.gradio),"label"in t&&l(4,_e=t.label),"elem_id"in t&&l(5,ue=t.elem_id),"elem_classes"in t&&l(6,ce=t.elem_classes),"visible"in t&&l(7,fe=t.visible),"show_label"in t&&l(8,me=t.show_label),"scale"in t&&l(9,de=t.scale),"min_width"in t&&l(10,ge=t.min_width),"loading_status"in t&&l(11,ne=t.loading_status),"height"in t&&l(12,he=t.height)},o.$$.update=()=>{o.$$.dirty[0]&1&&l(33,K=r?Ce(r):[]),o.$$.dirty[0]&131073|o.$$.dirty[1]&4&&(i=f&&r&&r.datatypes[f]==="nominal"?Array.from(new Set(K.map(t=>t[f]))):[]),o.$$.dirty[0]&1073741824&&(n=Ae(ee)),o.$$.dirty[0]&32769&&l(38,s=r&&r.datatypes[g]==="temporal"),o.$$.dirty[0]&33554432|o.$$.dirty[1]&128&&(u=V&&s?[V[0]*1e3,V[1]*1e3]:V),o.$$.dirty[0]&4194304&&l(34,E=P?typeof P=="string"?1e3*parseInt(P.substring(0,P.length-1))*Ee[P[P.length-1]]:P:void 0),o.$$.dirty[0]&8421377|o.$$.dirty[1]&40&&r&&(r.mark==="point"?(l(36,I=E!==void 0),l(35,D=B||I?"sum":void 0)):(l(36,I=E!==void 0||r.datatypes[g]==="nominal"),l(35,D=B||"sum"))),o.$$.dirty[0]&8192&&l(39,d=b?window.getComputedStyle(b):null),o.$$.dirty[0]&1199546373|o.$$.dirty[1]&336&&d&&requestAnimationFrame(ae)},[r,a,oe,j,_e,ue,ce,fe,me,de,ge,ne,he,b,Oe,g,c,f,m,F,S,$,P,B,H,V,U,X,Y,G,ee,A,J,K,E,D,I,le,s,d,Le,Te]}class bt extends qe{constructor(e){super(),Ye(this,e,tt,et,Ge,{value:0,x:15,y:16,color:17,root:1,title:18,x_title:19,y_title:20,color_title:21,x_bin:22,y_aggregate:23,color_map:24,x_lim:25,y_lim:26,x_label_angle:27,y_label_angle:28,x_axis_labels_visible:29,caption:2,sort:30,tooltip:31,_selectable:32,gradio:3,label:4,elem_id:5,elem_classes:6,visible:7,show_label:8,scale:9,min_width:10,loading_status:11,height:12},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),_()}get x(){return this.$$.ctx[15]}set x(e){this.$$set({x:e}),_()}get y(){return this.$$.ctx[16]}set y(e){this.$$set({y:e}),_()}get color(){return this.$$.ctx[17]}set color(e){this.$$set({color:e}),_()}get root(){return this.$$.ctx[1]}set root(e){this.$$set({root:e}),_()}get title(){return this.$$.ctx[18]}set title(e){this.$$set({title:e}),_()}get x_title(){return this.$$.ctx[19]}set x_title(e){this.$$set({x_title:e}),_()}get y_title(){return this.$$.ctx[20]}set y_title(e){this.$$set({y_title:e}),_()}get color_title(){return this.$$.ctx[21]}set color_title(e){this.$$set({color_title:e}),_()}get x_bin(){return this.$$.ctx[22]}set x_bin(e){this.$$set({x_bin:e}),_()}get y_aggregate(){return this.$$.ctx[23]}set y_aggregate(e){this.$$set({y_aggregate:e}),_()}get color_map(){return this.$$.ctx[24]}set color_map(e){this.$$set({color_map:e}),_()}get x_lim(){return this.$$.ctx[25]}set x_lim(e){this.$$set({x_lim:e}),_()}get y_lim(){return this.$$.ctx[26]}set y_lim(e){this.$$set({y_lim:e}),_()}get x_label_angle(){return this.$$.ctx[27]}set x_label_angle(e){this.$$set({x_label_angle:e}),_()}get y_label_angle(){return this.$$.ctx[28]}set y_label_angle(e){this.$$set({y_label_angle:e}),_()}get x_axis_labels_visible(){return this.$$.ctx[29]}set x_axis_labels_visible(e){this.$$set({x_axis_labels_visible:e}),_()}get caption(){return this.$$.ctx[2]}set caption(e){this.$$set({caption:e}),_()}get sort(){return this.$$.ctx[30]}set sort(e){this.$$set({sort:e}),_()}get tooltip(){return this.$$.ctx[31]}set tooltip(e){this.$$set({tooltip:e}),_()}get _selectable(){return this.$$.ctx[32]}set _selectable(e){this.$$set({_selectable:e}),_()}get gradio(){return this.$$.ctx[3]}set gradio(e){this.$$set({gradio:e}),_()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),_()}get elem_id(){return this.$$.ctx[5]}set elem_id(e){this.$$set({elem_id:e}),_()}get elem_classes(){return this.$$.ctx[6]}set elem_classes(e){this.$$set({elem_classes:e}),_()}get visible(){return this.$$.ctx[7]}set visible(e){this.$$set({visible:e}),_()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),_()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),_()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),_()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),_()}get height(){return this.$$.ctx[12]}set height(e){this.$$set({height:e}),_()}}export{bt as default};
//# sourceMappingURL=Index-C_CL_I2H.js.map
