{"version": 3, "file": "IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js", "sources": ["../../../../js/theme/src/colors.ts"], "sourcesContent": ["// import tw_colors from \"tailwindcss/colors\";\n\nexport const ordered_colors = [\n\t\"red\",\n\t\"green\",\n\t\"blue\",\n\t\"yellow\",\n\t\"purple\",\n\t\"teal\",\n\t\"orange\",\n\t\"cyan\",\n\t\"lime\",\n\t\"pink\"\n] as const;\ninterface ColorPair {\n\tprimary: string;\n\tsecondary: string;\n}\n\ninterface Colors {\n\tred: ColorPair;\n\tgreen: ColorPair;\n\tblue: ColorPair;\n\tyellow: ColorPair;\n\tpurple: ColorPair;\n\tteal: ColorPair;\n\torange: ColorPair;\n\tcyan: ColorPair;\n\tlime: ColorPair;\n\tpink: ColorPair;\n}\n\n// https://play.tailwindcss.com/ZubQYya0aN\nexport const color_values = [\n\t{ color: \"red\", primary: 600, secondary: 100 },\n\t{ color: \"green\", primary: 600, secondary: 100 },\n\t{ color: \"blue\", primary: 600, secondary: 100 },\n\t{ color: \"yellow\", primary: 500, secondary: 100 },\n\t{ color: \"purple\", primary: 600, secondary: 100 },\n\t{ color: \"teal\", primary: 600, secondary: 100 },\n\t{ color: \"orange\", primary: 600, secondary: 100 },\n\t{ color: \"cyan\", primary: 600, secondary: 100 },\n\t{ color: \"lime\", primary: 500, secondary: 100 },\n\t{ color: \"pink\", primary: 600, secondary: 100 }\n] as const;\n\nconst tw_colors = {\n\tinherit: \"inherit\",\n\tcurrent: \"currentColor\",\n\ttransparent: \"transparent\",\n\tblack: \"#000\",\n\twhite: \"#fff\",\n\tslate: {\n\t\t50: \"#f8fafc\",\n\t\t100: \"#f1f5f9\",\n\t\t200: \"#e2e8f0\",\n\t\t300: \"#cbd5e1\",\n\t\t400: \"#94a3b8\",\n\t\t500: \"#64748b\",\n\t\t600: \"#475569\",\n\t\t700: \"#334155\",\n\t\t800: \"#1e293b\",\n\t\t900: \"#0f172a\",\n\t\t950: \"#020617\"\n\t},\n\tgray: {\n\t\t50: \"#f9fafb\",\n\t\t100: \"#f3f4f6\",\n\t\t200: \"#e5e7eb\",\n\t\t300: \"#d1d5db\",\n\t\t400: \"#9ca3af\",\n\t\t500: \"#6b7280\",\n\t\t600: \"#4b5563\",\n\t\t700: \"#374151\",\n\t\t800: \"#1f2937\",\n\t\t900: \"#111827\",\n\t\t950: \"#030712\"\n\t},\n\tzinc: {\n\t\t50: \"#fafafa\",\n\t\t100: \"#f4f4f5\",\n\t\t200: \"#e4e4e7\",\n\t\t300: \"#d4d4d8\",\n\t\t400: \"#a1a1aa\",\n\t\t500: \"#71717a\",\n\t\t600: \"#52525b\",\n\t\t700: \"#3f3f46\",\n\t\t800: \"#27272a\",\n\t\t900: \"#18181b\",\n\t\t950: \"#09090b\"\n\t},\n\tneutral: {\n\t\t50: \"#fafafa\",\n\t\t100: \"#f5f5f5\",\n\t\t200: \"#e5e5e5\",\n\t\t300: \"#d4d4d4\",\n\t\t400: \"#a3a3a3\",\n\t\t500: \"#737373\",\n\t\t600: \"#525252\",\n\t\t700: \"#404040\",\n\t\t800: \"#262626\",\n\t\t900: \"#171717\",\n\t\t950: \"#0a0a0a\"\n\t},\n\tstone: {\n\t\t50: \"#fafaf9\",\n\t\t100: \"#f5f5f4\",\n\t\t200: \"#e7e5e4\",\n\t\t300: \"#d6d3d1\",\n\t\t400: \"#a8a29e\",\n\t\t500: \"#78716c\",\n\t\t600: \"#57534e\",\n\t\t700: \"#44403c\",\n\t\t800: \"#292524\",\n\t\t900: \"#1c1917\",\n\t\t950: \"#0c0a09\"\n\t},\n\tred: {\n\t\t50: \"#fef2f2\",\n\t\t100: \"#fee2e2\",\n\t\t200: \"#fecaca\",\n\t\t300: \"#fca5a5\",\n\t\t400: \"#f87171\",\n\t\t500: \"#ef4444\",\n\t\t600: \"#dc2626\",\n\t\t700: \"#b91c1c\",\n\t\t800: \"#991b1b\",\n\t\t900: \"#7f1d1d\",\n\t\t950: \"#450a0a\"\n\t},\n\torange: {\n\t\t50: \"#fff7ed\",\n\t\t100: \"#ffedd5\",\n\t\t200: \"#fed7aa\",\n\t\t300: \"#fdba74\",\n\t\t400: \"#fb923c\",\n\t\t500: \"#f97316\",\n\t\t600: \"#ea580c\",\n\t\t700: \"#c2410c\",\n\t\t800: \"#9a3412\",\n\t\t900: \"#7c2d12\",\n\t\t950: \"#431407\"\n\t},\n\tamber: {\n\t\t50: \"#fffbeb\",\n\t\t100: \"#fef3c7\",\n\t\t200: \"#fde68a\",\n\t\t300: \"#fcd34d\",\n\t\t400: \"#fbbf24\",\n\t\t500: \"#f59e0b\",\n\t\t600: \"#d97706\",\n\t\t700: \"#b45309\",\n\t\t800: \"#92400e\",\n\t\t900: \"#78350f\",\n\t\t950: \"#451a03\"\n\t},\n\tyellow: {\n\t\t50: \"#fefce8\",\n\t\t100: \"#fef9c3\",\n\t\t200: \"#fef08a\",\n\t\t300: \"#fde047\",\n\t\t400: \"#facc15\",\n\t\t500: \"#eab308\",\n\t\t600: \"#ca8a04\",\n\t\t700: \"#a16207\",\n\t\t800: \"#854d0e\",\n\t\t900: \"#713f12\",\n\t\t950: \"#422006\"\n\t},\n\tlime: {\n\t\t50: \"#f7fee7\",\n\t\t100: \"#ecfccb\",\n\t\t200: \"#d9f99d\",\n\t\t300: \"#bef264\",\n\t\t400: \"#a3e635\",\n\t\t500: \"#84cc16\",\n\t\t600: \"#65a30d\",\n\t\t700: \"#4d7c0f\",\n\t\t800: \"#3f6212\",\n\t\t900: \"#365314\",\n\t\t950: \"#1a2e05\"\n\t},\n\tgreen: {\n\t\t50: \"#f0fdf4\",\n\t\t100: \"#dcfce7\",\n\t\t200: \"#bbf7d0\",\n\t\t300: \"#86efac\",\n\t\t400: \"#4ade80\",\n\t\t500: \"#22c55e\",\n\t\t600: \"#16a34a\",\n\t\t700: \"#15803d\",\n\t\t800: \"#166534\",\n\t\t900: \"#14532d\",\n\t\t950: \"#052e16\"\n\t},\n\temerald: {\n\t\t50: \"#ecfdf5\",\n\t\t100: \"#d1fae5\",\n\t\t200: \"#a7f3d0\",\n\t\t300: \"#6ee7b7\",\n\t\t400: \"#34d399\",\n\t\t500: \"#10b981\",\n\t\t600: \"#059669\",\n\t\t700: \"#047857\",\n\t\t800: \"#065f46\",\n\t\t900: \"#064e3b\",\n\t\t950: \"#022c22\"\n\t},\n\tteal: {\n\t\t50: \"#f0fdfa\",\n\t\t100: \"#ccfbf1\",\n\t\t200: \"#99f6e4\",\n\t\t300: \"#5eead4\",\n\t\t400: \"#2dd4bf\",\n\t\t500: \"#14b8a6\",\n\t\t600: \"#0d9488\",\n\t\t700: \"#0f766e\",\n\t\t800: \"#115e59\",\n\t\t900: \"#134e4a\",\n\t\t950: \"#042f2e\"\n\t},\n\tcyan: {\n\t\t50: \"#ecfeff\",\n\t\t100: \"#cffafe\",\n\t\t200: \"#a5f3fc\",\n\t\t300: \"#67e8f9\",\n\t\t400: \"#22d3ee\",\n\t\t500: \"#06b6d4\",\n\t\t600: \"#0891b2\",\n\t\t700: \"#0e7490\",\n\t\t800: \"#155e75\",\n\t\t900: \"#164e63\",\n\t\t950: \"#083344\"\n\t},\n\tsky: {\n\t\t50: \"#f0f9ff\",\n\t\t100: \"#e0f2fe\",\n\t\t200: \"#bae6fd\",\n\t\t300: \"#7dd3fc\",\n\t\t400: \"#38bdf8\",\n\t\t500: \"#0ea5e9\",\n\t\t600: \"#0284c7\",\n\t\t700: \"#0369a1\",\n\t\t800: \"#075985\",\n\t\t900: \"#0c4a6e\",\n\t\t950: \"#082f49\"\n\t},\n\tblue: {\n\t\t50: \"#eff6ff\",\n\t\t100: \"#dbeafe\",\n\t\t200: \"#bfdbfe\",\n\t\t300: \"#93c5fd\",\n\t\t400: \"#60a5fa\",\n\t\t500: \"#3b82f6\",\n\t\t600: \"#2563eb\",\n\t\t700: \"#1d4ed8\",\n\t\t800: \"#1e40af\",\n\t\t900: \"#1e3a8a\",\n\t\t950: \"#172554\"\n\t},\n\tindigo: {\n\t\t50: \"#eef2ff\",\n\t\t100: \"#e0e7ff\",\n\t\t200: \"#c7d2fe\",\n\t\t300: \"#a5b4fc\",\n\t\t400: \"#818cf8\",\n\t\t500: \"#6366f1\",\n\t\t600: \"#4f46e5\",\n\t\t700: \"#4338ca\",\n\t\t800: \"#3730a3\",\n\t\t900: \"#312e81\",\n\t\t950: \"#1e1b4b\"\n\t},\n\tviolet: {\n\t\t50: \"#f5f3ff\",\n\t\t100: \"#ede9fe\",\n\t\t200: \"#ddd6fe\",\n\t\t300: \"#c4b5fd\",\n\t\t400: \"#a78bfa\",\n\t\t500: \"#8b5cf6\",\n\t\t600: \"#7c3aed\",\n\t\t700: \"#6d28d9\",\n\t\t800: \"#5b21b6\",\n\t\t900: \"#4c1d95\",\n\t\t950: \"#2e1065\"\n\t},\n\tpurple: {\n\t\t50: \"#faf5ff\",\n\t\t100: \"#f3e8ff\",\n\t\t200: \"#e9d5ff\",\n\t\t300: \"#d8b4fe\",\n\t\t400: \"#c084fc\",\n\t\t500: \"#a855f7\",\n\t\t600: \"#9333ea\",\n\t\t700: \"#7e22ce\",\n\t\t800: \"#6b21a8\",\n\t\t900: \"#581c87\",\n\t\t950: \"#3b0764\"\n\t},\n\tfuchsia: {\n\t\t50: \"#fdf4ff\",\n\t\t100: \"#fae8ff\",\n\t\t200: \"#f5d0fe\",\n\t\t300: \"#f0abfc\",\n\t\t400: \"#e879f9\",\n\t\t500: \"#d946ef\",\n\t\t600: \"#c026d3\",\n\t\t700: \"#a21caf\",\n\t\t800: \"#86198f\",\n\t\t900: \"#701a75\",\n\t\t950: \"#4a044e\"\n\t},\n\tpink: {\n\t\t50: \"#fdf2f8\",\n\t\t100: \"#fce7f3\",\n\t\t200: \"#fbcfe8\",\n\t\t300: \"#f9a8d4\",\n\t\t400: \"#f472b6\",\n\t\t500: \"#ec4899\",\n\t\t600: \"#db2777\",\n\t\t700: \"#be185d\",\n\t\t800: \"#9d174d\",\n\t\t900: \"#831843\",\n\t\t950: \"#500724\"\n\t},\n\trose: {\n\t\t50: \"#fff1f2\",\n\t\t100: \"#ffe4e6\",\n\t\t200: \"#fecdd3\",\n\t\t300: \"#fda4af\",\n\t\t400: \"#fb7185\",\n\t\t500: \"#f43f5e\",\n\t\t600: \"#e11d48\",\n\t\t700: \"#be123c\",\n\t\t800: \"#9f1239\",\n\t\t900: \"#881337\",\n\t\t950: \"#4c0519\"\n\t}\n};\n\nexport const colors = color_values.reduce(\n\t(acc, { color, primary, secondary }) => ({\n\t\t...acc,\n\t\t[color]: {\n\t\t\tprimary: tw_colors[color][primary],\n\t\t\tsecondary: tw_colors[color][secondary]\n\t\t}\n\t}),\n\t{} as Colors\n);\n"], "names": ["ordered_colors", "color_values", "tw_colors", "colors", "acc", "color", "primary", "secondary"], "mappings": "AAEO,MAAMA,EAAiB,CAC7B,MACA,QACA,OACA,SACA,SACA,OACA,SACA,OACA,OACA,MACD,EAoBaC,EAAe,CAC3B,CAAE,MAAO,MAAO,QAAS,IAAK,UAAW,GAAI,EAC7C,CAAE,MAAO,QAAS,QAAS,IAAK,UAAW,GAAI,EAC/C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,SAAU,QAAS,IAAK,UAAW,GAAI,EAChD,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,EAC9C,CAAE,MAAO,OAAQ,QAAS,IAAK,UAAW,GAAI,CAC/C,EAEMC,EAAY,CACjB,QAAS,UACT,QAAS,eACT,YAAa,cACb,MAAO,OACP,MAAO,OACP,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,IAAK,CACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,MAAO,CACN,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,IAAK,CACJ,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,OAAQ,CACP,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,QAAS,CACR,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,EACA,KAAM,CACL,GAAI,UACJ,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,UACL,IAAK,SACN,CACD,EAEaC,EAASF,EAAa,OAClC,CAACG,EAAK,CAAE,MAAAC,EAAO,QAAAC,EAAS,UAAAC,MAAiB,CACxC,GAAGH,EACH,CAACC,CAAK,EAAG,CACR,QAASH,EAAUG,CAAK,EAAEC,CAAO,EACjC,UAAWJ,EAAUG,CAAK,EAAEE,CAAS,CACtC,CAAA,GAED,CAAC,CACF"}