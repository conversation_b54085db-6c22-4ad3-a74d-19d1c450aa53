import{B as te}from"./Block-CB3nIXHA.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{U as ie}from"./UploadText-BVcQ46sk.js";import le from"./Gallery-CqKzMhy6.js";import{S as ne}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{B as se}from"./FileUpload-DUiF5Qgq.js";/* empty css                                              */import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./Upload-DXgDHKDd.js";import"./BlockLabel-CCoHIDM7.js";import"./IconButton-B3BI2i6T.js";import"./Empty-B_fwEKaS.js";import"./ShareButton-HkxSYutN.js";import"./Community-Dw1micSV.js";import"./utils-BsGrhMNe.js";import"./Clear-By3xiIwg.js";import"./Download-DVtk-Jv3.js";import"./FullscreenButton-BWloBRD_.js";import"./Play-B0Q0U1Qz.js";import"./IconButtonWrapper-BMUxHqmv.js";/* empty css                                             */import"./ModifyUpload-89NSLc0b.js";import"./Undo-DCjBnnSO.js";import"./DownloadLink-IzUam-rM.js";import"./context-TgWPFwN2.js";import"./file-url-DgijyRSD.js";/* empty css                                                   */import"./Image-BRVH1yXn.js";/* empty css                                                   */import"./Video-DiLYgEjl.js";import"./hls-CnVhpNcu.js";import"./File-BQ_9P3Ye.js";import"./Upload-CRdEJrCZ.js";const{SvelteComponent:oe,add_flush_callback:p,assign:re,bind:H,binding_callbacks:J,check_outros:_e,create_component:j,destroy_component:B,detach:K,empty:ae,flush:f,get_spread_object:ue,get_spread_update:fe,group_outros:he,init:ce,insert:L,mount_component:z,safe_not_equal:me,space:ge,transition_in:d,transition_out:b}=window.__gradio__svelte__internal,{createEventDispatcher:we}=window.__gradio__svelte__internal;function de(l){let e,s,i,n;function r(o){l[31](o)}function w(o){l[32](o)}let g={label:l[4],show_label:l[3],columns:l[13],rows:l[14],height:l[15],preview:l[16],object_fit:l[18],interactive:l[20],allow_preview:l[17],show_share_button:l[19],show_download_button:l[21],i18n:l[22].i18n,_fetch:l[30],show_fullscreen_button:l[23]};return l[1]!==void 0&&(g.selected_index=l[1]),l[0]!==void 0&&(g.value=l[0]),e=new le({props:g}),J.push(()=>H(e,"selected_index",r)),J.push(()=>H(e,"value",w)),e.$on("change",l[33]),e.$on("select",l[34]),e.$on("share",l[35]),e.$on("error",l[36]),{c(){j(e.$$.fragment)},m(o,u){z(e,o,u),n=!0},p(o,u){const a={};u[0]&16&&(a.label=o[4]),u[0]&8&&(a.show_label=o[3]),u[0]&8192&&(a.columns=o[13]),u[0]&16384&&(a.rows=o[14]),u[0]&32768&&(a.height=o[15]),u[0]&65536&&(a.preview=o[16]),u[0]&262144&&(a.object_fit=o[18]),u[0]&1048576&&(a.interactive=o[20]),u[0]&131072&&(a.allow_preview=o[17]),u[0]&524288&&(a.show_share_button=o[19]),u[0]&2097152&&(a.show_download_button=o[21]),u[0]&4194304&&(a.i18n=o[22].i18n),u[0]&4194304&&(a._fetch=o[30]),u[0]&8388608&&(a.show_fullscreen_button=o[23]),!s&&u[0]&2&&(s=!0,a.selected_index=o[1],p(()=>s=!1)),!i&&u[0]&1&&(i=!0,a.value=o[0],p(()=>i=!1)),e.$set(a)},i(o){n||(d(e.$$.fragment,o),n=!0)},o(o){b(e.$$.fragment,o),n=!1},d(o){B(e,o)}}}function be(l){let e,s;return e=new se({props:{value:null,root:l[5],label:l[4],max_file_size:l[22].max_file_size,file_count:"multiple",file_types:l[9],i18n:l[22].i18n,upload:l[26],stream_handler:l[27],$$slots:{default:[ve]},$$scope:{ctx:l}}}),e.$on("upload",l[28]),e.$on("error",l[29]),{c(){j(e.$$.fragment)},m(i,n){z(e,i,n),s=!0},p(i,n){const r={};n[0]&32&&(r.root=i[5]),n[0]&16&&(r.label=i[4]),n[0]&4194304&&(r.max_file_size=i[22].max_file_size),n[0]&512&&(r.file_types=i[9]),n[0]&4194304&&(r.i18n=i[22].i18n),n[0]&4194304&&(r.upload=i[26]),n[0]&4194304&&(r.stream_handler=i[27]),n[0]&4194304|n[1]&128&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){s||(d(e.$$.fragment,i),s=!0)},o(i){b(e.$$.fragment,i),s=!1},d(i){B(e,i)}}}function ve(l){let e,s;return e=new ie({props:{i18n:l[22].i18n,type:"gallery"}}),{c(){j(e.$$.fragment)},m(i,n){z(e,i,n),s=!0},p(i,n){const r={};n[0]&4194304&&(r.i18n=i[22].i18n),e.$set(r)},i(i){s||(d(e.$$.fragment,i),s=!0)},o(i){b(e.$$.fragment,i),s=!1},d(i){B(e,i)}}}function ke(l){let e,s,i,n,r,w;const g=[{autoscroll:l[22].autoscroll},{i18n:l[22].i18n},l[2]];let o={};for(let _=0;_<g.length;_+=1)o=re(o,g[_]);e=new ne({props:o}),e.$on("clear_status",l[25]);const u=[be,de],a=[];function c(_,h){return _[20]&&_[24]?0:1}return i=c(l),n=a[i]=u[i](l),{c(){j(e.$$.fragment),s=ge(),n.c(),r=ae()},m(_,h){z(e,_,h),L(_,s,h),a[i].m(_,h),L(_,r,h),w=!0},p(_,h){const S=h[0]&4194308?fe(g,[h[0]&4194304&&{autoscroll:_[22].autoscroll},h[0]&4194304&&{i18n:_[22].i18n},h[0]&4&&ue(_[2])]):{};e.$set(S);let v=i;i=c(_),i===v?a[i].p(_,h):(he(),b(a[v],1,1,()=>{a[v]=null}),_e(),n=a[i],n?n.p(_,h):(n=a[i]=u[i](_),n.c()),d(n,1),n.m(r.parentNode,r))},i(_){w||(d(e.$$.fragment,_),d(n),w=!0)},o(_){b(e.$$.fragment,_),b(n),w=!1},d(_){_&&(K(s),K(r)),B(e,_),a[i].d(_)}}}function je(l){let e,s;return e=new te({props:{visible:l[8],variant:"solid",padding:!1,elem_id:l[6],elem_classes:l[7],container:l[10],scale:l[11],min_width:l[12],allow_overflow:!1,height:typeof l[15]=="number"?l[15]:void 0,$$slots:{default:[ke]},$$scope:{ctx:l}}}),{c(){j(e.$$.fragment)},m(i,n){z(e,i,n),s=!0},p(i,n){const r={};n[0]&256&&(r.visible=i[8]),n[0]&64&&(r.elem_id=i[6]),n[0]&128&&(r.elem_classes=i[7]),n[0]&1024&&(r.container=i[10]),n[0]&2048&&(r.scale=i[11]),n[0]&4096&&(r.min_width=i[12]),n[0]&32768&&(r.height=typeof i[15]=="number"?i[15]:void 0),n[0]&33546815|n[1]&128&&(r.$$scope={dirty:n,ctx:i}),e.$set(r)},i(i){s||(d(e.$$.fragment,i),s=!0)},o(i){b(e.$$.fragment,i),s=!1},d(i){B(e,i)}}}function Be(l,e,s){let i,{loading_status:n}=e,{show_label:r}=e,{label:w}=e,{root:g}=e,{elem_id:o=""}=e,{elem_classes:u=[]}=e,{visible:a=!0}=e,{value:c=null}=e,{file_types:_=["image","video"]}=e,{container:h=!0}=e,{scale:S=null}=e,{min_width:v=void 0}=e,{columns:A=[2]}=e,{rows:G=void 0}=e,{height:q="auto"}=e,{preview:C}=e,{allow_preview:D=!0}=e,{selected_index:k=null}=e,{object_fit:E="cover"}=e,{show_share_button:F=!1}=e,{interactive:I}=e,{show_download_button:N=!1}=e,{gradio:m}=e,{show_fullscreen_button:T=!0}=e;const M=we(),O=()=>m.dispatch("clear_status",n),P=(...t)=>m.client.upload(...t),Q=(...t)=>m.client.stream(...t),R=t=>{const ee=Array.isArray(t.detail)?t.detail:[t.detail];s(0,c=ee.map(U=>U.mime_type?.includes("video")?{video:U,caption:null}:{image:U,caption:null})),m.dispatch("upload",c)},V=({detail:t})=>{s(2,n=n||{}),s(2,n.status="error",n),m.dispatch("error",t)},W=(...t)=>m.client.fetch(...t);function X(t){k=t,s(1,k)}function Y(t){c=t,s(0,c)}const Z=()=>m.dispatch("change",c),y=t=>m.dispatch("select",t.detail),x=t=>m.dispatch("share",t.detail),$=t=>m.dispatch("error",t.detail);return l.$$set=t=>{"loading_status"in t&&s(2,n=t.loading_status),"show_label"in t&&s(3,r=t.show_label),"label"in t&&s(4,w=t.label),"root"in t&&s(5,g=t.root),"elem_id"in t&&s(6,o=t.elem_id),"elem_classes"in t&&s(7,u=t.elem_classes),"visible"in t&&s(8,a=t.visible),"value"in t&&s(0,c=t.value),"file_types"in t&&s(9,_=t.file_types),"container"in t&&s(10,h=t.container),"scale"in t&&s(11,S=t.scale),"min_width"in t&&s(12,v=t.min_width),"columns"in t&&s(13,A=t.columns),"rows"in t&&s(14,G=t.rows),"height"in t&&s(15,q=t.height),"preview"in t&&s(16,C=t.preview),"allow_preview"in t&&s(17,D=t.allow_preview),"selected_index"in t&&s(1,k=t.selected_index),"object_fit"in t&&s(18,E=t.object_fit),"show_share_button"in t&&s(19,F=t.show_share_button),"interactive"in t&&s(20,I=t.interactive),"show_download_button"in t&&s(21,N=t.show_download_button),"gradio"in t&&s(22,m=t.gradio),"show_fullscreen_button"in t&&s(23,T=t.show_fullscreen_button)},l.$$.update=()=>{l.$$.dirty[0]&1&&s(24,i=c===null?!0:c.length===0),l.$$.dirty[0]&2&&M("prop_change",{selected_index:k})},[c,k,n,r,w,g,o,u,a,_,h,S,v,A,G,q,C,D,E,F,I,N,m,T,i,O,P,Q,R,V,W,X,Y,Z,y,x,$]}class ot extends oe{constructor(e){super(),ce(this,e,Be,je,me,{loading_status:2,show_label:3,label:4,root:5,elem_id:6,elem_classes:7,visible:8,value:0,file_types:9,container:10,scale:11,min_width:12,columns:13,rows:14,height:15,preview:16,allow_preview:17,selected_index:1,object_fit:18,show_share_button:19,interactive:20,show_download_button:21,gradio:22,show_fullscreen_button:23},null,[-1,-1])}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_label(){return this.$$.ctx[3]}set show_label(e){this.$$set({show_label:e}),f()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),f()}get root(){return this.$$.ctx[5]}set root(e){this.$$set({root:e}),f()}get elem_id(){return this.$$.ctx[6]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[7]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[8]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get file_types(){return this.$$.ctx[9]}set file_types(e){this.$$set({file_types:e}),f()}get container(){return this.$$.ctx[10]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[11]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[12]}set min_width(e){this.$$set({min_width:e}),f()}get columns(){return this.$$.ctx[13]}set columns(e){this.$$set({columns:e}),f()}get rows(){return this.$$.ctx[14]}set rows(e){this.$$set({rows:e}),f()}get height(){return this.$$.ctx[15]}set height(e){this.$$set({height:e}),f()}get preview(){return this.$$.ctx[16]}set preview(e){this.$$set({preview:e}),f()}get allow_preview(){return this.$$.ctx[17]}set allow_preview(e){this.$$set({allow_preview:e}),f()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),f()}get object_fit(){return this.$$.ctx[18]}set object_fit(e){this.$$set({object_fit:e}),f()}get show_share_button(){return this.$$.ctx[19]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get interactive(){return this.$$.ctx[20]}set interactive(e){this.$$set({interactive:e}),f()}get show_download_button(){return this.$$.ctx[21]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get gradio(){return this.$$.ctx[22]}set gradio(e){this.$$set({gradio:e}),f()}get show_fullscreen_button(){return this.$$.ctx[23]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}}export{le as BaseGallery,ot as default};
//# sourceMappingURL=Index-C0FeVypH.js.map
