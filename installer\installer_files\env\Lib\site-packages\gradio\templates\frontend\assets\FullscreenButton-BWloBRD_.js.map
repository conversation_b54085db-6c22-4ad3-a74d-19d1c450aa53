{"version": 3, "file": "FullscreenButton-BWloBRD_.js", "sources": ["../../../../js/icons/src/Maximize.svelte", "../../../../js/icons/src/Image.svelte", "../../../../js/icons/src/Minimize.svelte", "../../../../js/atoms/src/FullscreenButton.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-maximize\"\n\twidth=\"100%\"\n\theight=\"100%\"\n>\n\t<path\n\t\td=\"M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3\"\n\t>\n\t</path>\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"1.5\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-image\"\n>\n\t<rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" ry=\"2\" />\n\t<circle cx=\"8.5\" cy=\"8.5\" r=\"1.5\" />\n\t<polyline points=\"21 15 16 10 5 21\" />\n</svg>\n", "<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\tstroke=\"currentColor\"\n\tstroke-width=\"2\"\n\tstroke-linecap=\"round\"\n\tstroke-linejoin=\"round\"\n\tclass=\"feather feather-minimize\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\t><path\n\t\td=\"M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3\"\n\t></path></svg\n>\n", "<script lang=\"ts\">\n\timport { onMount, createEventDispatcher } from \"svelte\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport { Maximize, Minimize } from \"@gradio/icons\";\n\n\texport let container: HTMLElement | undefined = undefined;\n\tconst dispatch = createEventDispatcher<{\n\t\tfullscreenchange: boolean;\n\t}>();\n\n\tlet is_full_screen = false;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t\tdispatch(\"fullscreenchange\", is_full_screen);\n\t\t});\n\t});\n\n\tconst toggle_full_screen = async (): Promise<void> => {\n\t\tif (!container) return;\n\n\t\tif (!is_full_screen) {\n\t\t\tawait container.requestFullscreen();\n\t\t} else {\n\t\t\tawait document.exitFullscreen();\n\t\t\tis_full_screen = !is_full_screen;\n\t\t}\n\t};\n</script>\n\n{#if !is_full_screen}\n\t<IconButton\n\t\tIcon={Maximize}\n\t\tlabel=\"View in full screen\"\n\t\ton:click={toggle_full_screen}\n\t/>\n{/if}\n\n{#if is_full_screen}\n\t<IconButton\n\t\tIcon={Minimize}\n\t\tlabel=\"Exit full screen\"\n\t\ton:click={toggle_full_screen}\n\t/>\n{/if}\n"], "names": ["insert", "target", "svg", "anchor", "append", "path", "rect", "circle", "polyline", "onMount", "createEventDispatcher", "Maximize", "ctx", "Minimize", "create_if_block_1", "create_if_block", "container", "$$props", "dispatch", "is_full_screen", "$$invalidate", "toggle_full_screen"], "mappings": "kwBAAAA,EAgBKC,EAAAC,EAAAC,CAAA,EAJJC,EAGMF,EAAAG,CAAA,+wBCfPL,EAeKC,EAAAC,EAAAC,CAAA,EAHJC,EAAwDF,EAAAI,CAAA,EACxDF,EAAmCF,EAAAK,CAAA,EACnCH,EAAqCF,EAAAM,CAAA,6qBCdtCR,EAcAC,EAAAC,EAAAC,CAAA,EAHEC,EAEOF,EAAAG,CAAA,+VCZC,CAAA,QAAAI,GAAA,sBAAAC,IAAsC,OAAA,6EAgCxCC,+CAEIC,EAAkB,CAAA,CAAA,oLAMtBC,4CAEID,EAAkB,CAAA,CAAA,8JAZxBA,EAAc,CAAA,GAAAE,EAAAF,CAAA,IAQfA,EAAc,CAAA,GAAAG,EAAAH,CAAA,8GARbA,EAAc,CAAA,wGAQfA,EAAc,CAAA,yNAlCP,UAAAI,EAAqC,MAAA,EAAAC,QAC1CC,EAAWR,SAIbS,EAAiB,GAErBV,GAAA,IAAA,CACC,SAAS,iBAAiB,mBAAA,IAAA,CACzBW,EAAA,EAAAD,EAAA,CAAA,CAAmB,SAAS,iBAAA,EAC5BD,EAAS,mBAAoBC,CAAc,MAIvC,MAAAE,EAAA,SAAA,CACAL,IAEAG,SAGE,SAAS,qBACfA,EAAkB,CAAAA,CAAA,SAHZH,EAAU"}