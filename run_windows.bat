@echo off
REM Simple Windows startup script for <PERSON><PERSON> Floyd
REM This script checks for Python and runs the application

echo Starting Roop Floyd...

REM Check if Python is available
py --version >nul 2>&1
if %errorlevel% neq 0 (
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo <PERSON> is not installed or not in PATH.
        echo Please install Python 3.9+ and try again.
        pause
        exit /b 1
    )
    set PYTHON_CMD=python
) else (
    set PYTHON_CMD=py
)

REM Check if we're in the correct directory
if not exist "run.py" (
    echo Error: run.py not found. Please run this script from the roop-floyd directory.
    pause
    exit /b 1
)

REM Check if requirements are installed
%PYTHON_CMD% -c "import torch, cv2, gradio" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing requirements...
    %PYTHON_CMD% -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo Failed to install requirements.
        pause
        exit /b 1
    )
)

REM Run the application
echo Launching Roop Floyd...
%PYTHON_CMD% run.py %*

if %errorlevel% neq 0 (
    echo Application exited with error code %errorlevel%
    pause
)
