const{SvelteComponent:o,flush:v,init:_,safe_not_equal:d}=window.__gradio__svelte__internal,{onDestroy:f}=window.__gradio__svelte__internal;function g(r,t,e){let{gradio:s}=t,{value:n=1}=t,{active:l=!0}=t,c,u,i;return f(()=>{i&&clearInterval(i)}),r.$$set=a=>{"gradio"in a&&e(0,s=a.gradio),"value"in a&&e(1,n=a.value),"active"in a&&e(2,l=a.active)},r.$$.update=()=>{r.$$.dirty&63&&(c!==n||l!==u)&&(i&&clearInterval(i),l&&e(5,i=setInterval(()=>{document.visibilityState==="visible"&&s.dispatch("tick")},n*1e3)),e(3,c=n),e(4,u=l))},[s,n,l,c,u,i]}class h extends o{constructor(t){super(),_(this,t,g,null,d,{gradio:0,value:1,active:2})}get gradio(){return this.$$.ctx[0]}set gradio(t){this.$$set({gradio:t}),v()}get value(){return this.$$.ctx[1]}set value(t){this.$$set({value:t}),v()}get active(){return this.$$.ctx[2]}set active(t){this.$$set({active:t}),v()}}export{h as default};
//# sourceMappingURL=Index-BMLc4VxK.js.map
