#!/usr/bin/env python3
"""
Test script to verify Windows fixes for Roop <PERSON>
This script tests the main components that were causing ASGI issues
"""

import sys
import os
import traceback

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import gradio as gr
        print("✓ Gradio imported successfully")
    except Exception as e:
        print(f"✗ Gradio import failed: {e}")
        return False
    
    try:
        import roop.globals
        print("✓ Roop globals imported successfully")
    except Exception as e:
        print(f"✗ Roop globals import failed: {e}")
        return False
    
    try:
        from settings import Settings
        print("✓ Settings imported successfully")
    except Exception as e:
        print(f"✗ Settings import failed: {e}")
        return False
    
    return True

def test_config_loading():
    """Test if config can be loaded without errors"""
    print("\nTesting config loading...")
    
    try:
        from settings import Settings
        cfg = Settings('config.yaml')
        print("✓ Config loaded successfully")
        print(f"  - Theme: {cfg.selected_theme}")
        print(f"  - Server port: {cfg.server_port}")
        print(f"  - Output format: {cfg.output_image_format}")
        return True
    except Exception as e:
        print(f"✗ Config loading failed: {e}")
        traceback.print_exc()
        return False

def test_gradio_components():
    """Test if Gradio components can be created without schema errors"""
    print("\nTesting Gradio components...")
    
    try:
        import gradio as gr
        
        # Test the previously problematic ImageEditor component
        brush = gr.Brush(color_mode="fixed", colors=["rgba(255, 255, 255, 1)"])
        print("✓ Brush component created successfully")
        
        # Test basic components
        with gr.Blocks() as demo:
            gr.Textbox(label="Test")
            gr.Button("Test")
            gr.Slider(0, 100, value=50)
            gr.Dropdown(["option1", "option2"], value="option1")
            gr.Checkbox(label="Test checkbox", value=False)
        
        print("✓ Basic Gradio components created successfully")
        
        # Test if we can get API info without errors
        try:
            api_info = demo.get_api_info()
            print("✓ API info generated successfully")
        except Exception as e:
            print(f"✗ API info generation failed: {e}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Gradio components test failed: {e}")
        traceback.print_exc()
        return False

def test_ui_tabs():
    """Test if UI tabs can be imported without errors"""
    print("\nTesting UI tabs...")
    
    try:
        # Set up minimal globals first
        import roop.globals
        from settings import Settings
        roop.globals.CFG = Settings('config.yaml')
        
        # Test importing UI tabs
        from ui.tabs.faceswap_tab import get_gradio_output_format
        print("✓ Faceswap tab imported successfully")
        
        # Test the function that was causing issues
        format_result = get_gradio_output_format()
        print(f"✓ get_gradio_output_format() returned: {format_result}")
        
        return True
    except Exception as e:
        print(f"✗ UI tabs test failed: {e}")
        traceback.print_exc()
        return False

def test_environment_setup():
    """Test Windows-specific environment setup"""
    print("\nTesting environment setup...")
    
    try:
        from ui.main import prepare_environment
        prepare_environment()
        print("✓ Environment prepared successfully")
        
        # Check if Windows-specific env vars are set
        if os.name == 'nt':
            expected_vars = ['GRADIO_SERVER_NAME', 'UVICORN_HOST', 'GRADIO_ANALYTICS_ENABLED']
            for var in expected_vars:
                if var in os.environ:
                    print(f"✓ {var} = {os.environ[var]}")
                else:
                    print(f"⚠ {var} not set")
        
        return True
    except Exception as e:
        print(f"✗ Environment setup failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Roop Floyd Windows Fix Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_loading,
        test_gradio_components,
        test_ui_tabs,
        test_environment_setup
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test crashed: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The Windows fixes should work.")
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
