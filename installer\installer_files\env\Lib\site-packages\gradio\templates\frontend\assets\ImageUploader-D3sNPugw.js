import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{B as _t}from"./BlockLabel-CCoHIDM7.js";import{I as ft}from"./IconButton-B3BI2i6T.js";import{C as mt}from"./Clear-By3xiIwg.js";import{I as dt,F as gt}from"./FullscreenButton-BWloBRD_.js";import{W as ht,a as bt,S as pt}from"./SelectSource-cjMqi4BC.js";import{I as wt}from"./IconButtonWrapper-BMUxHqmv.js";import{g as vt}from"./utils-Gtzs_Zla.js";import{D as it}from"./DropdownArrow-CRmyeEUc.js";import{S as kt}from"./Square-oAGqOwsh.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{f as yt}from"./index-C30MtwUc.js";import{S as It}from"./StreamingBar-CCUnT5tf.js";import{d as St}from"./index-BkoKOheB.js";import{U as Ct}from"./Upload-CRdEJrCZ.js";/* empty css                                                   */import{I as Dt}from"./Image-BRVH1yXn.js";const{SvelteComponent:qt,append:Oe,attr:A,detach:Et,init:Wt,insert:Bt,noop:Me,safe_not_equal:$t,svg_element:je}=window.__gradio__svelte__internal;function zt(l){let e,t,n;return{c(){e=je("svg"),t=je("path"),n=je("circle"),A(t,"d","M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"),A(n,"cx","12"),A(n,"cy","13"),A(n,"r","4"),A(e,"xmlns","http://www.w3.org/2000/svg"),A(e,"width","100%"),A(e,"height","100%"),A(e,"viewBox","0 0 24 24"),A(e,"fill","none"),A(e,"stroke","currentColor"),A(e,"stroke-width","1.5"),A(e,"stroke-linecap","round"),A(e,"stroke-linejoin","round"),A(e,"class","feather feather-camera")},m(i,s){Bt(i,e,s),Oe(e,t),Oe(e,n)},p:Me,i:Me,o:Me,d(i){i&&Et(e)}}}class Mt extends qt{constructor(e){super(),Wt(this,e,null,zt,$t,{})}}const{SvelteComponent:jt,append:Nt,attr:P,detach:Rt,init:Tt,insert:Ut,noop:Ne,safe_not_equal:Lt,svg_element:Pe}=window.__gradio__svelte__internal;function Ft(l){let e,t;return{c(){e=Pe("svg"),t=Pe("circle"),P(t,"cx","12"),P(t,"cy","12"),P(t,"r","10"),P(e,"xmlns","http://www.w3.org/2000/svg"),P(e,"width","100%"),P(e,"height","100%"),P(e,"viewBox","0 0 24 24"),P(e,"stroke-width","1.5"),P(e,"stroke-linecap","round"),P(e,"stroke-linejoin","round"),P(e,"class","feather feather-circle")},m(n,i){Ut(n,e,i),Nt(e,t)},p:Ne,i:Ne,o:Ne,d(n){n&&Rt(e)}}}class At extends jt{constructor(e){super(),Tt(this,e,null,Ft,Lt,{})}}const{SvelteComponent:Ht,append:ve,attr:Re,create_component:Ot,destroy_component:Pt,detach:Vt,element:Te,init:Gt,insert:Jt,listen:Kt,mount_component:Qt,noop:Xt,safe_not_equal:Yt,set_style:Zt,space:xt,text:en,transition_in:tn,transition_out:nn}=window.__gradio__svelte__internal,{createEventDispatcher:ln}=window.__gradio__svelte__internal;function rn(l){let e,t,n,i,s,r="Click to Access Webcam",a,o,u,f;return i=new ht({}),{c(){e=Te("button"),t=Te("div"),n=Te("span"),Ot(i.$$.fragment),s=xt(),a=en(r),Re(n,"class","icon-wrap svelte-qbrfs"),Re(t,"class","wrap svelte-qbrfs"),Re(e,"class","svelte-qbrfs"),Zt(e,"height","100%")},m(_,w){Jt(_,e,w),ve(e,t),ve(t,n),Qt(i,n,null),ve(t,s),ve(t,a),o=!0,u||(f=Kt(e,"click",l[1]),u=!0)},p:Xt,i(_){o||(tn(i.$$.fragment,_),o=!0)},o(_){nn(i.$$.fragment,_),o=!1},d(_){_&&Vt(e),Pt(i),u=!1,f()}}}function sn(l){const e=ln();return[e,()=>e("click")]}class on extends Ht{constructor(e){super(),Gt(this,e,sn,rn,Yt,{})}}function an(){return navigator.mediaDevices.enumerateDevices()}function cn(l,e){e.srcObject=l,e.muted=!0,e.play()}async function Ve(l,e,t,n){const i={video:n?{deviceId:{exact:n},...t?.video}:t?.video||{width:{ideal:1920},height:{ideal:1440}},audio:l&&(t?.audio??!0)};return navigator.mediaDevices.getUserMedia(i).then(s=>(cn(s,e),s))}function un(l){return l.filter(t=>t.kind==="videoinput")}const{SvelteComponent:_n,action_destroyer:fn,add_render_callback:mn,append:D,attr:y,binding_callbacks:dn,check_outros:fe,create_component:x,create_in_transition:gn,destroy_component:ee,destroy_each:hn,detach:R,element:B,empty:Fe,ensure_array_like:Ge,flush:V,group_outros:me,init:bn,insert:T,listen:Se,mount_component:te,noop:Ae,run_all:pn,safe_not_equal:wn,set_data:pe,set_input_value:Ue,set_style:lt,space:K,src_url_equal:Je,stop_propagation:vn,text:we,toggle_class:re,transition_in:q,transition_out:W}=window.__gradio__svelte__internal,{createEventDispatcher:kn,onMount:yn}=window.__gradio__svelte__internal;function Ke(l,e,t){const n=l.slice();return n[38]=e[t],n}function In(l){let e,t,n,i,s,r,a,o,u,f,_;const w=[Dn,Cn],I=[];function j(b,k){return b[2]==="video"||b[1]?0:1}n=j(l),i=I[n]=w[n](l);let d=!l[11]&&Qe(l),h=l[13]&&l[8]&&Xe(l);return{c(){e=B("div"),t=B("button"),i.c(),r=K(),d&&d.c(),a=K(),h&&h.c(),o=Fe(),y(t,"aria-label",s=l[2]==="image"?"capture photo":"start recording"),y(t,"class","svelte-s8feoe"),y(e,"class","button-wrap svelte-s8feoe")},m(b,k){T(b,e,k),D(e,t),I[n].m(t,null),D(e,r),d&&d.m(e,null),T(b,a,k),h&&h.m(b,k),T(b,o,k),u=!0,f||(_=Se(t,"click",l[16]),f=!0)},p(b,k){let U=n;n=j(b),n===U?I[n].p(b,k):(me(),W(I[U],1,1,()=>{I[U]=null}),fe(),i=I[n],i?i.p(b,k):(i=I[n]=w[n](b),i.c()),q(i,1),i.m(t,null)),(!u||k[0]&4&&s!==(s=b[2]==="image"?"capture photo":"start recording"))&&y(t,"aria-label",s),b[11]?d&&(me(),W(d,1,1,()=>{d=null}),fe()):d?(d.p(b,k),k[0]&2048&&q(d,1)):(d=Qe(b),d.c(),q(d,1),d.m(e,null)),b[13]&&b[8]?h?(h.p(b,k),k[0]&8448&&q(h,1)):(h=Xe(b),h.c(),q(h,1),h.m(o.parentNode,o)):h&&(me(),W(h,1,1,()=>{h=null}),fe())},i(b){u||(q(i),q(d),q(h),u=!0)},o(b){W(i),W(d),W(h),u=!1},d(b){b&&(R(e),R(a),R(o)),I[n].d(),d&&d.d(),h&&h.d(b),f=!1,_()}}}function Sn(l){let e,t,n,i;return t=new on({}),t.$on("click",l[27]),{c(){e=B("div"),x(t.$$.fragment),y(e,"title","grant webcam access"),lt(e,"height","100%")},m(s,r){T(s,e,r),te(t,e,null),i=!0},p:Ae,i(s){i||(q(t.$$.fragment,s),s&&(n||mn(()=>{n=gn(e,yt,{delay:100,duration:200}),n.start()})),i=!0)},o(s){W(t.$$.fragment,s),i=!1},d(s){s&&R(e),ee(t)}}}function Cn(l){let e,t,n;return t=new Mt({}),{c(){e=B("div"),x(t.$$.fragment),y(e,"class","icon svelte-s8feoe"),y(e,"title","capture photo")},m(i,s){T(i,e,s),te(t,e,null),n=!0},p:Ae,i(i){n||(q(t.$$.fragment,i),n=!0)},o(i){W(t.$$.fragment,i),n=!1},d(i){i&&R(e),ee(t)}}}function Dn(l){let e,t,n,i;const s=[Wn,En,qn],r=[];function a(o,u){return o[1]&&o[10]==="waiting"?0:o[1]&&o[10]==="open"||!o[1]&&o[11]?1:2}return e=a(l),t=r[e]=s[e](l),{c(){t.c(),n=Fe()},m(o,u){r[e].m(o,u),T(o,n,u),i=!0},p(o,u){let f=e;e=a(o),e===f?r[e].p(o,u):(me(),W(r[f],1,1,()=>{r[f]=null}),fe(),t=r[e],t?t.p(o,u):(t=r[e]=s[e](o),t.c()),q(t,1),t.m(n.parentNode,n))},i(o){i||(q(t),i=!0)},o(o){W(t),i=!1},d(o){o&&R(n),r[e].d(o)}}}function qn(l){let e,t,n,i,s=l[4]("audio.record")+"",r,a;return n=new At({}),{c(){e=B("div"),t=B("div"),x(n.$$.fragment),i=K(),r=we(s),y(t,"class","icon color-primary svelte-s8feoe"),y(t,"title","start recording"),y(e,"class","icon-with-text svelte-s8feoe")},m(o,u){T(o,e,u),D(e,t),te(n,t,null),D(e,i),D(e,r),a=!0},p(o,u){(!a||u[0]&16)&&s!==(s=o[4]("audio.record")+"")&&pe(r,s)},i(o){a||(q(n.$$.fragment,o),a=!0)},o(o){W(n.$$.fragment,o),a=!1},d(o){o&&R(e),ee(n)}}}function En(l){let e,t,n,i,s=l[4]("audio.stop")+"",r,a;return n=new kt({}),{c(){e=B("div"),t=B("div"),x(n.$$.fragment),i=K(),r=we(s),y(t,"class","icon color-primary svelte-s8feoe"),y(t,"title","stop recording"),y(e,"class","icon-with-text svelte-s8feoe")},m(o,u){T(o,e,u),D(e,t),te(n,t,null),D(e,i),D(e,r),a=!0},p(o,u){(!a||u[0]&16)&&s!==(s=o[4]("audio.stop")+"")&&pe(r,s)},i(o){a||(q(n.$$.fragment,o),a=!0)},o(o){W(n.$$.fragment,o),a=!1},d(o){o&&R(e),ee(n)}}}function Wn(l){let e,t,n,i,s=l[4]("audio.waiting")+"",r,a;return n=new bt({}),{c(){e=B("div"),t=B("div"),x(n.$$.fragment),i=K(),r=we(s),y(t,"class","icon color-primary svelte-s8feoe"),y(t,"title","spinner"),y(e,"class","icon-with-text svelte-s8feoe"),lt(e,"width","var(--size-24)")},m(o,u){T(o,e,u),D(e,t),te(n,t,null),D(e,i),D(e,r),a=!0},p(o,u){(!a||u[0]&16)&&s!==(s=o[4]("audio.waiting")+"")&&pe(r,s)},i(o){a||(q(n.$$.fragment,o),a=!0)},o(o){W(n.$$.fragment,o),a=!1},d(o){o&&R(e),ee(n)}}}function Qe(l){let e,t,n,i,s;return t=new it({}),{c(){e=B("button"),x(t.$$.fragment),y(e,"class","icon svelte-s8feoe"),y(e,"aria-label","select input source")},m(r,a){T(r,e,a),te(t,e,null),n=!0,i||(s=Se(e,"click",l[28]),i=!0)},p:Ae,i(r){n||(q(t.$$.fragment,r),n=!0)},o(r){W(t.$$.fragment,r),n=!1},d(r){r&&R(e),ee(t),i=!1,s()}}}function Xe(l){let e,t,n,i,s,r,a;n=new it({});function o(_,w){return _[7].length===0?$n:Bn}let u=o(l),f=u(l);return{c(){e=B("select"),t=B("button"),x(n.$$.fragment),i=K(),f.c(),y(t,"class","inset-icon svelte-s8feoe"),y(e,"class","select-wrap svelte-s8feoe"),y(e,"aria-label","select source")},m(_,w){T(_,e,w),D(e,t),te(n,t,null),D(t,i),f.m(e,null),s=!0,r||(a=[Se(t,"click",vn(l[29])),fn(He.call(null,e,l[17])),Se(e,"change",l[14])],r=!0)},p(_,w){u===(u=o(_))&&f?f.p(_,w):(f.d(1),f=u(_),f&&(f.c(),f.m(e,null)))},i(_){s||(q(n.$$.fragment,_),s=!0)},o(_){W(n.$$.fragment,_),s=!1},d(_){_&&R(e),ee(n),f.d(),r=!1,pn(a)}}}function Bn(l){let e,t=Ge(l[7]),n=[];for(let i=0;i<t.length;i+=1)n[i]=Ye(Ke(l,t,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();e=Fe()},m(i,s){for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(i,s);T(i,e,s)},p(i,s){if(s[0]&384){t=Ge(i[7]);let r;for(r=0;r<t.length;r+=1){const a=Ke(i,t,r);n[r]?n[r].p(a,s):(n[r]=Ye(a),n[r].c(),n[r].m(e.parentNode,e))}for(;r<n.length;r+=1)n[r].d(1);n.length=t.length}},d(i){i&&R(e),hn(n,i)}}}function $n(l){let e,t=l[4]("common.no_devices")+"",n;return{c(){e=B("option"),n=we(t),e.__value="",Ue(e,e.__value),y(e,"class","svelte-s8feoe")},m(i,s){T(i,e,s),D(e,n)},p(i,s){s[0]&16&&t!==(t=i[4]("common.no_devices")+"")&&pe(n,t)},d(i){i&&R(e)}}}function Ye(l){let e,t=l[38].label+"",n,i,s,r;return{c(){e=B("option"),n=we(t),i=K(),e.__value=s=l[38].deviceId,Ue(e,e.__value),e.selected=r=l[8].deviceId===l[38].deviceId,y(e,"class","svelte-s8feoe")},m(a,o){T(a,e,o),D(e,n),D(e,i)},p(a,o){o[0]&128&&t!==(t=a[38].label+"")&&pe(n,t),o[0]&128&&s!==(s=a[38].deviceId)&&(e.__value=s,Ue(e,e.__value)),o[0]&384&&r!==(r=a[8].deviceId===a[38].deviceId)&&(e.selected=r)},d(a){a&&R(e)}}}function zn(l){let e,t,n,i,s,r,a,o,u,f,_;t=new It({props:{time_limit:l[9]}});const w=[Sn,In],I=[];function j(d,h){return d[12]?1:0}return u=j(l),f=I[u]=w[u](l),{c(){e=B("div"),x(t.$$.fragment),n=K(),i=B("video"),s=K(),r=B("img"),o=K(),f.c(),y(i,"class","svelte-s8feoe"),re(i,"flip",l[3]),re(i,"hide",!l[12]||l[12]&&!!l[0]),Je(r.src,a=l[0]?.url)||y(r,"src",a),y(r,"class","svelte-s8feoe"),re(r,"hide",!l[12]||l[12]&&!l[0]),y(e,"class","wrap svelte-s8feoe")},m(d,h){T(d,e,h),te(t,e,null),D(e,n),D(e,i),l[26](i),D(e,s),D(e,r),D(e,o),I[u].m(e,null),_=!0},p(d,h){const b={};h[0]&512&&(b.time_limit=d[9]),t.$set(b),(!_||h[0]&8)&&re(i,"flip",d[3]),(!_||h[0]&4097)&&re(i,"hide",!d[12]||d[12]&&!!d[0]),(!_||h[0]&1&&!Je(r.src,a=d[0]?.url))&&y(r,"src",a),(!_||h[0]&4097)&&re(r,"hide",!d[12]||d[12]&&!d[0]);let k=u;u=j(d),u===k?I[u].p(d,h):(me(),W(I[k],1,1,()=>{I[k]=null}),fe(),f=I[u],f?f.p(d,h):(f=I[u]=w[u](d),f.c()),q(f,1),f.m(e,null))},i(d){_||(q(t.$$.fragment,d),q(f),_=!0)},o(d){W(t.$$.fragment,d),W(f),_=!1},d(d){d&&R(e),ee(t),l[26](null),I[u].d()}}}function He(l,e){const t=n=>{l&&!l.contains(n.target)&&!n.defaultPrevented&&e(n)};return document.addEventListener("click",t,!0),{destroy(){document.removeEventListener("click",t,!0)}}}function Mn(l,e,t){let n,i=[],s=null,r=null,a="closed";const o=g=>{g==="closed"?(t(9,r=null),t(10,a="closed"),t(0,H=null)):g==="waiting"?t(10,a="waiting"):t(10,a="open")},u=g=>{N&&t(9,r=g)};let f,{streaming:_=!1}=e,{pending:w=!1}=e,{root:I=""}=e,{stream_every:j=1}=e,{mode:d="image"}=e,{mirror_webcam:h}=e,{include_audio:b}=e,{webcam_constraints:k=null}=e,{i18n:U}=e,{upload:$}=e,{value:H=null}=e;const v=kn();yn(()=>{f=document.createElement("canvas"),_&&d==="image"&&window.setInterval(()=>{n&&!w&&L()},j*1e3)});const m=async g=>{const J=g.target.value;await Ve(b,n,k,J).then(async ce=>{O=ce,t(8,s=i.find(ue=>ue.deviceId===J)||null),t(13,Q=!1)})};async function p(){try{Ve(b,n,k).then(async g=>{t(12,ae=!0),t(7,i=await an()),O=g}).then(()=>un(i)).then(g=>{t(7,i=g);const M=O.getTracks().map(J=>J.getSettings()?.deviceId)[0];t(8,s=M&&g.find(J=>J.deviceId===M)||i[0])}),(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)&&v("error",U("image.no_webcam_support"))}catch(g){if(g instanceof DOMException&&g.name=="NotAllowedError")v("error",U("image.allow_webcam_access"));else throw g}}function L(){var g=f.getContext("2d");if((!_||_&&N)&&n.videoWidth&&n.videoHeight){if(f.width=n.videoWidth,f.height=n.videoHeight,g.drawImage(n,0,0,n.videoWidth,n.videoHeight),h&&(g.scale(-1,1),g.drawImage(n,-n.videoWidth,0)),_&&(!N||a==="waiting"))return;if(_){const M=f.toDataURL("image/jpeg");v("stream",M);return}f.toBlob(M=>{v(_?"stream":"capture",M)},`image/${_?"jpeg":"png"}`,.8)}}let N=!1,z=[],O,G,F;function ne(){if(N){F.stop();let g=new Blob(z,{type:G}),M=new FileReader;M.onload=async function(J){if(J.target){let ce=new File([g],"sample."+G.substring(6));const ue=await St([ce]);let ze=(await $(ue,I))?.filter(Boolean)[0];v("capture",ze),v("stop_recording")}},M.readAsDataURL(g)}else{v("start_recording"),z=[];let g=["video/webm","video/mp4"];for(let M of g)if(MediaRecorder.isTypeSupported(M)){G=M;break}if(G===null){console.error("No supported MediaRecorder mimeType");return}F=new MediaRecorder(O,{mimeType:G}),F.addEventListener("dataavailable",function(M){z.push(M.data)}),F.start(200)}t(11,N=!N)}let ae=!1;function De(){d==="image"&&_&&t(11,N=!N),d==="image"?L():ne(),!N&&O&&(v("close_stream"),O.getTracks().forEach(g=>g.stop()),t(6,n.srcObject=null,n),t(12,ae=!1),window.setTimeout(()=>{t(0,H=null)},500),t(0,H=null))}let Q=!1;function qe(g){g.preventDefault(),g.stopPropagation(),t(13,Q=!1)}function Ee(g){dn[g?"unshift":"push"](()=>{n=g,t(6,n)})}const We=async()=>p(),Be=()=>t(13,Q=!0),$e=()=>t(13,Q=!1);return l.$$set=g=>{"streaming"in g&&t(1,_=g.streaming),"pending"in g&&t(20,w=g.pending),"root"in g&&t(21,I=g.root),"stream_every"in g&&t(22,j=g.stream_every),"mode"in g&&t(2,d=g.mode),"mirror_webcam"in g&&t(3,h=g.mirror_webcam),"include_audio"in g&&t(23,b=g.include_audio),"webcam_constraints"in g&&t(24,k=g.webcam_constraints),"i18n"in g&&t(4,U=g.i18n),"upload"in g&&t(25,$=g.upload),"value"in g&&t(0,H=g.value)},[H,_,d,h,U,He,n,i,s,r,a,N,ae,Q,m,p,De,qe,o,u,w,I,j,b,k,$,Ee,We,Be,$e]}class jn extends _n{constructor(e){super(),bn(this,e,Mn,zn,wn,{modify_stream:18,set_time_limit:19,streaming:1,pending:20,root:21,stream_every:22,mode:2,mirror_webcam:3,include_audio:23,webcam_constraints:24,i18n:4,upload:25,value:0,click_outside:5},null,[-1,-1])}get modify_stream(){return this.$$.ctx[18]}get set_time_limit(){return this.$$.ctx[19]}get streaming(){return this.$$.ctx[1]}set streaming(e){this.$$set({streaming:e}),V()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),V()}get root(){return this.$$.ctx[21]}set root(e){this.$$set({root:e}),V()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),V()}get mode(){return this.$$.ctx[2]}set mode(e){this.$$set({mode:e}),V()}get mirror_webcam(){return this.$$.ctx[3]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),V()}get include_audio(){return this.$$.ctx[23]}set include_audio(e){this.$$set({include_audio:e}),V()}get webcam_constraints(){return this.$$.ctx[24]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),V()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),V()}get upload(){return this.$$.ctx[25]}set upload(e){this.$$set({upload:e}),V()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),V()}get click_outside(){return He}}const Nn=jn,{SvelteComponent:Rn,add_flush_callback:de,append:ke,attr:Ie,bind:ge,binding_callbacks:ie,bubble:ye,check_outros:he,create_component:X,create_slot:Tn,destroy_component:Y,detach:se,element:Le,empty:rt,flush:C,get_all_dirty_from_scope:Un,get_slot_changes:Ln,group_outros:be,init:Fn,insert:oe,listen:An,mount_component:Z,safe_not_equal:Hn,set_style:Ze,space:_e,toggle_class:Ce,transition_in:S,transition_out:E,update_slot_base:On}=window.__gradio__svelte__internal,{createEventDispatcher:Pn,tick:Vn}=window.__gradio__svelte__internal;function xe(l){let e,t,n,i=l[18]&&et(l);return t=new ft({props:{Icon:mt,label:"Remove Image"}}),t.$on("click",l[31]),{c(){i&&i.c(),e=_e(),X(t.$$.fragment)},m(s,r){i&&i.m(s,r),oe(s,e,r),Z(t,s,r),n=!0},p(s,r){s[18]?i?(i.p(s,r),r[0]&262144&&S(i,1)):(i=et(s),i.c(),S(i,1),i.m(e.parentNode,e)):i&&(be(),E(i,1,1,()=>{i=null}),he())},i(s){n||(S(i),S(t.$$.fragment,s),n=!0)},o(s){E(i),E(t.$$.fragment,s),n=!1},d(s){s&&se(e),i&&i.d(s),Y(t,s)}}}function et(l){let e,t;return e=new gt({props:{container:l[22]}}),{c(){X(e.$$.fragment)},m(n,i){Z(e,n,i),t=!0},p(n,i){const s={};i[0]&4194304&&(s.container=n[22]),e.$set(s)},i(n){t||(S(e.$$.fragment,n),t=!0)},o(n){E(e.$$.fragment,n),t=!1},d(n){Y(e,n)}}}function Gn(l){let e,t,n=l[3]?.url&&!l[20]&&xe(l);return{c(){n&&n.c(),e=rt()},m(i,s){n&&n.m(i,s),oe(i,e,s),t=!0},p(i,s){i[3]?.url&&!i[20]?n?(n.p(i,s),s[0]&1048584&&S(n,1)):(n=xe(i),n.c(),S(n,1),n.m(e.parentNode,e)):n&&(be(),E(n,1,1,()=>{n=null}),he())},i(i){t||(S(n),t=!0)},o(i){E(n),t=!1},d(i){i&&se(e),n&&n.d(i)}}}function tt(l){let e;const t=l[30].default,n=Tn(t,l,l[46],null);return{c(){n&&n.c()},m(i,s){n&&n.m(i,s),e=!0},p(i,s){n&&n.p&&(!e||s[1]&32768)&&On(n,t,i,i[46],e?Ln(t,i[46],s,null):Un(i[46]),null)},i(i){e||(S(n,i),e=!0)},o(i){E(n,i),e=!1},d(i){n&&n.d(i)}}}function Jn(l){let e,t,n=l[3]===null&&tt(l);return{c(){n&&n.c(),e=rt()},m(i,s){n&&n.m(i,s),oe(i,e,s),t=!0},p(i,s){i[3]===null?n?(n.p(i,s),s[0]&8&&S(n,1)):(n=tt(i),n.c(),S(n,1),n.m(e.parentNode,e)):n&&(be(),E(n,1,1,()=>{n=null}),he())},i(i){t||(S(n),t=!0)},o(i){E(n),t=!1},d(i){i&&se(e),n&&n.d(i)}}}function Kn(l){let e,t,n,i,s;return t=new Dt({props:{src:l[3].url,alt:l[3].alt_text}}),{c(){e=Le("div"),X(t.$$.fragment),Ie(e,"class","image-frame svelte-1ti4ehe"),Ce(e,"selectable",l[11])},m(r,a){oe(r,e,a),Z(t,e,null),n=!0,i||(s=An(e,"click",l[27]),i=!0)},p(r,a){const o={};a[0]&8&&(o.src=r[3].url),a[0]&8&&(o.alt=r[3].alt_text),t.$set(o),(!n||a[0]&2048)&&Ce(e,"selectable",r[11])},i(r){n||(S(t.$$.fragment,r),n=!0)},o(r){E(t.$$.fragment,r),n=!1},d(r){r&&se(e),Y(t),i=!1,s()}}}function Qn(l){let e,t,n,i;function s(o){l[36](o)}function r(o){l[37](o)}let a={root:l[12],value:l[3],mirror_webcam:l[10],stream_every:l[17],streaming:l[9],mode:"image",include_audio:!1,i18n:l[13],upload:l[15],webcam_constraints:l[19]};return l[4]!==void 0&&(a.modify_stream=l[4]),l[5]!==void 0&&(a.set_time_limit=l[5]),e=new Nn({props:a}),ie.push(()=>ge(e,"modify_stream",s)),ie.push(()=>ge(e,"set_time_limit",r)),e.$on("capture",l[38]),e.$on("stream",l[39]),e.$on("error",l[40]),e.$on("drag",l[41]),e.$on("upload",l[42]),e.$on("close_stream",l[43]),{c(){X(e.$$.fragment)},m(o,u){Z(e,o,u),i=!0},p(o,u){const f={};u[0]&4096&&(f.root=o[12]),u[0]&8&&(f.value=o[3]),u[0]&1024&&(f.mirror_webcam=o[10]),u[0]&131072&&(f.stream_every=o[17]),u[0]&512&&(f.streaming=o[9]),u[0]&8192&&(f.i18n=o[13]),u[0]&32768&&(f.upload=o[15]),u[0]&524288&&(f.webcam_constraints=o[19]),!t&&u[0]&16&&(t=!0,f.modify_stream=o[4],de(()=>t=!1)),!n&&u[0]&32&&(n=!0,f.set_time_limit=o[5],de(()=>n=!1)),e.$set(f)},i(o){i||(S(e.$$.fragment,o),i=!0)},o(o){E(e.$$.fragment,o),i=!1},d(o){Y(e,o)}}}function nt(l){let e,t,n;function i(r){l[44](r)}let s={sources:l[8],handle_clear:l[24],handle_select:l[28]};return l[1]!==void 0&&(s.active_source=l[1]),e=new pt({props:s}),ie.push(()=>ge(e,"active_source",i)),{c(){X(e.$$.fragment)},m(r,a){Z(e,r,a),n=!0},p(r,a){const o={};a[0]&256&&(o.sources=r[8]),!t&&a[0]&2&&(t=!0,o.active_source=r[1],de(()=>t=!1)),e.$set(o)},i(r){n||(S(e.$$.fragment,r),n=!0)},o(r){E(e.$$.fragment,r),n=!1},d(r){Y(e,r)}}}function Xn(l){let e,t,n,i,s,r,a,o,u,f,_,w,I,j=l[8].length>1||l[8].includes("clipboard"),d;e=new _t({props:{show_label:l[7],Icon:dt,label:l[6]||"Image"}}),i=new wt({props:{$$slots:{default:[Gn]},$$scope:{ctx:l}}});function h(m){l[33](m)}function b(m){l[34](m)}let k={hidden:l[3]!==null||l[1]==="webcam",filetype:l[1]==="clipboard"?"clipboard":"image/*",root:l[12],max_file_size:l[14],disable_click:!l[8].includes("upload")||l[3]!==null,upload:l[15],stream_handler:l[16],$$slots:{default:[Jn]},$$scope:{ctx:l}};l[0]!==void 0&&(k.uploading=l[0]),l[2]!==void 0&&(k.dragging=l[2]),a=new Ct({props:k}),l[32](a),ie.push(()=>ge(a,"uploading",h)),ie.push(()=>ge(a,"dragging",b)),a.$on("load",l[23]),a.$on("error",l[35]);const U=[Qn,Kn],$=[];function H(m,p){return m[1]==="webcam"&&(m[9]||!m[9]&&!m[3])?0:m[3]!==null&&!m[9]?1:-1}~(_=H(l))&&(w=$[_]=U[_](l));let v=j&&nt(l);return{c(){X(e.$$.fragment),t=_e(),n=Le("div"),X(i.$$.fragment),s=_e(),r=Le("div"),X(a.$$.fragment),f=_e(),w&&w.c(),I=_e(),v&&v.c(),Ie(r,"class","upload-container svelte-1ti4ehe"),Ce(r,"reduced-height",l[8].length>1),Ze(r,"width",l[3]?"auto":"100%"),Ie(n,"data-testid","image"),Ie(n,"class","image-container svelte-1ti4ehe")},m(m,p){Z(e,m,p),oe(m,t,p),oe(m,n,p),Z(i,n,null),ke(n,s),ke(n,r),Z(a,r,null),ke(r,f),~_&&$[_].m(r,null),ke(n,I),v&&v.m(n,null),l[45](n),d=!0},p(m,p){const L={};p[0]&128&&(L.show_label=m[7]),p[0]&64&&(L.label=m[6]||"Image"),e.$set(L);const N={};p[0]&5505032|p[1]&32768&&(N.$$scope={dirty:p,ctx:m}),i.$set(N);const z={};p[0]&10&&(z.hidden=m[3]!==null||m[1]==="webcam"),p[0]&2&&(z.filetype=m[1]==="clipboard"?"clipboard":"image/*"),p[0]&4096&&(z.root=m[12]),p[0]&16384&&(z.max_file_size=m[14]),p[0]&264&&(z.disable_click=!m[8].includes("upload")||m[3]!==null),p[0]&32768&&(z.upload=m[15]),p[0]&65536&&(z.stream_handler=m[16]),p[0]&8|p[1]&32768&&(z.$$scope={dirty:p,ctx:m}),!o&&p[0]&1&&(o=!0,z.uploading=m[0],de(()=>o=!1)),!u&&p[0]&4&&(u=!0,z.dragging=m[2],de(()=>u=!1)),a.$set(z);let O=_;_=H(m),_===O?~_&&$[_].p(m,p):(w&&(be(),E($[O],1,1,()=>{$[O]=null}),he()),~_?(w=$[_],w?w.p(m,p):(w=$[_]=U[_](m),w.c()),S(w,1),w.m(r,null)):w=null),(!d||p[0]&256)&&Ce(r,"reduced-height",m[8].length>1),p[0]&8&&Ze(r,"width",m[3]?"auto":"100%"),p[0]&256&&(j=m[8].length>1||m[8].includes("clipboard")),j?v?(v.p(m,p),p[0]&256&&S(v,1)):(v=nt(m),v.c(),S(v,1),v.m(n,null)):v&&(be(),E(v,1,1,()=>{v=null}),he())},i(m){d||(S(e.$$.fragment,m),S(i.$$.fragment,m),S(a.$$.fragment,m),S(w),S(v),d=!0)},o(m){E(e.$$.fragment,m),E(i.$$.fragment,m),E(a.$$.fragment,m),E(w),E(v),d=!1},d(m){m&&(se(t),se(n)),Y(e,m),Y(i),l[32](null),Y(a),~_&&$[_].d(),v&&v.d(),l[45](null)}}}function Yn(l,e,t){let n,{$$slots:i={},$$scope:s}=e,{value:r=null}=e,{label:a=void 0}=e,{show_label:o}=e,{sources:u=["upload","clipboard","webcam"]}=e,{streaming:f=!1}=e,{pending:_=!1}=e,{mirror_webcam:w}=e,{selectable:I=!1}=e,{root:j}=e,{i18n:d}=e,{max_file_size:h=null}=e,{upload:b}=e,{stream_handler:k}=e,{stream_every:U}=e,{modify_stream:$}=e,{set_time_limit:H}=e,{show_fullscreen_button:v=!0}=e,m,{uploading:p=!1}=e,{active_source:L=null}=e,{webcam_constraints:N=void 0}=e;function z({detail:c}){f||(t(3,r=c),F("upload"))}function O(){t(3,r=null),F("clear"),F("change",null)}async function G(c,le){if(le==="stream"){F("stream",{value:{url:c},is_value_data:!0});return}t(29,_=!0);const ut=await m.load_files([new File([c],`image/${f?"jpeg":"png"}`)]);(le==="change"||le==="upload")&&(t(3,r=ut?.[0]||null),await Vn(),F("change")),t(29,_=!1)}const F=Pn();let{dragging:ne=!1}=e;function ae(c){let le=vt(c);le&&F("select",{index:le,value:null})}async function De(c){switch(c){case"clipboard":m.paste_clipboard();break}}let Q;const qe=c=>{t(3,r=null),F("clear"),c.stopPropagation()};function Ee(c){ie[c?"unshift":"push"](()=>{m=c,t(21,m)})}function We(c){p=c,t(0,p)}function Be(c){ne=c,t(2,ne)}function $e(c){ye.call(this,l,c)}function g(c){$=c,t(4,$)}function M(c){H=c,t(5,H)}const J=c=>G(c.detail,"change"),ce=c=>G(c.detail,"stream");function ue(c){ye.call(this,l,c)}function ze(c){ye.call(this,l,c)}const st=c=>G(c.detail,"upload");function ot(c){ye.call(this,l,c)}function at(c){L=c,t(1,L),t(8,u)}function ct(c){ie[c?"unshift":"push"](()=>{Q=c,t(22,Q)})}return l.$$set=c=>{"value"in c&&t(3,r=c.value),"label"in c&&t(6,a=c.label),"show_label"in c&&t(7,o=c.show_label),"sources"in c&&t(8,u=c.sources),"streaming"in c&&t(9,f=c.streaming),"pending"in c&&t(29,_=c.pending),"mirror_webcam"in c&&t(10,w=c.mirror_webcam),"selectable"in c&&t(11,I=c.selectable),"root"in c&&t(12,j=c.root),"i18n"in c&&t(13,d=c.i18n),"max_file_size"in c&&t(14,h=c.max_file_size),"upload"in c&&t(15,b=c.upload),"stream_handler"in c&&t(16,k=c.stream_handler),"stream_every"in c&&t(17,U=c.stream_every),"modify_stream"in c&&t(4,$=c.modify_stream),"set_time_limit"in c&&t(5,H=c.set_time_limit),"show_fullscreen_button"in c&&t(18,v=c.show_fullscreen_button),"uploading"in c&&t(0,p=c.uploading),"active_source"in c&&t(1,L=c.active_source),"webcam_constraints"in c&&t(19,N=c.webcam_constraints),"dragging"in c&&t(2,ne=c.dragging),"$$scope"in c&&t(46,s=c.$$scope)},l.$$.update=()=>{l.$$.dirty[0]&258&&!L&&u&&t(1,L=u[0]),l.$$.dirty[0]&514&&t(20,n=f&&L==="webcam"),l.$$.dirty[0]&1048577&&p&&!n&&t(3,r=null),l.$$.dirty[0]&4&&F("drag",ne)},[p,L,ne,r,$,H,a,o,u,f,w,I,j,d,h,b,k,U,v,N,n,m,Q,z,O,G,F,ae,De,_,i,qe,Ee,We,Be,$e,g,M,J,ce,ue,ze,st,ot,at,ct,s]}class Zn extends Rn{constructor(e){super(),Fn(this,e,Yn,Xn,Hn,{value:3,label:6,show_label:7,sources:8,streaming:9,pending:29,mirror_webcam:10,selectable:11,root:12,i18n:13,max_file_size:14,upload:15,stream_handler:16,stream_every:17,modify_stream:4,set_time_limit:5,show_fullscreen_button:18,uploading:0,active_source:1,webcam_constraints:19,dragging:2},null,[-1,-1])}get value(){return this.$$.ctx[3]}set value(e){this.$$set({value:e}),C()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),C()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),C()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),C()}get streaming(){return this.$$.ctx[9]}set streaming(e){this.$$set({streaming:e}),C()}get pending(){return this.$$.ctx[29]}set pending(e){this.$$set({pending:e}),C()}get mirror_webcam(){return this.$$.ctx[10]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),C()}get selectable(){return this.$$.ctx[11]}set selectable(e){this.$$set({selectable:e}),C()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),C()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),C()}get max_file_size(){return this.$$.ctx[14]}set max_file_size(e){this.$$set({max_file_size:e}),C()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),C()}get stream_handler(){return this.$$.ctx[16]}set stream_handler(e){this.$$set({stream_handler:e}),C()}get stream_every(){return this.$$.ctx[17]}set stream_every(e){this.$$set({stream_every:e}),C()}get modify_stream(){return this.$$.ctx[4]}set modify_stream(e){this.$$set({modify_stream:e}),C()}get set_time_limit(){return this.$$.ctx[5]}set set_time_limit(e){this.$$set({set_time_limit:e}),C()}get show_fullscreen_button(){return this.$$.ctx[18]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),C()}get uploading(){return this.$$.ctx[0]}set uploading(e){this.$$set({uploading:e}),C()}get active_source(){return this.$$.ctx[1]}set active_source(e){this.$$set({active_source:e}),C()}get webcam_constraints(){return this.$$.ctx[19]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),C()}get dragging(){return this.$$.ctx[2]}set dragging(e){this.$$set({dragging:e}),C()}}const bi=Zn;export{bi as I,Nn as W};
//# sourceMappingURL=ImageUploader-D3sNPugw.js.map
