import{r as h}from"./file-url-DgijyRSD.js";/* empty css                                                   */const{SvelteComponent:p,assign:a,bubble:v,compute_rest_props:u,detach:b,element:I,exclude_internal_props:j,flush:q,get_spread_update:y,init:w,insert:x,listen:C,noop:m,safe_not_equal:P,set_attributes:g,src_url_equal:S,toggle_class:d}=window.__gradio__svelte__internal;function k(s){let e,r,i,_,l=[{src:r=s[0]},s[1]],n={};for(let t=0;t<l.length;t+=1)n=a(n,l[t]);return{c(){e=I("img"),g(e,n),d(e,"svelte-1pijsyv",!0)},m(t,o){x(t,e,o),i||(_=C(e,"load",s[4]),i=!0)},p(t,[o]){g(e,n=y(l,[o&1&&!S(e.src,r=t[0])&&{src:r},o&2&&t[1]])),d(e,"svelte-1pijsyv",!0)},i:m,o:m,d(t){t&&b(e),i=!1,_()}}}function z(s,e,r){const i=["src"];let _=u(e,i),{src:l=void 0}=e,n,t;function o(c){v.call(this,s,c)}return s.$$set=c=>{e=a(a({},e),j(c)),r(1,_=u(e,i)),"src"in c&&r(2,l=c.src)},s.$$.update=()=>{if(s.$$.dirty&12){r(0,n=l),r(3,t=l);const c=l;h(c).then(f=>{t===c&&r(0,n=f)})}},[n,_,l,t,o]}class A extends p{constructor(e){super(),w(this,e,z,k,P,{src:2})}get src(){return this.$$.ctx[2]}set src(e){this.$$set({src:e}),q()}}const E=A;export{E as I};
//# sourceMappingURL=Image-BRVH1yXn.js.map
