{"version": 3, "file": "Index36-BW_hssju.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index36.js"], "sourcesContent": ["import { create_ssr_component, validate_component, add_attribute, escape } from \"svelte/internal\";\nimport \"./client.js\";\nimport { createEventDispatcher } from \"svelte\";\nimport { BaseButton as Button } from \"./Index35.js\";\nconst css = {\n  code: \".button-icon.svelte-yjn27e{width:var(--text-xl);height:var(--text-xl);margin-right:var(--spacing-xl)}\",\n  map: '{\"version\":3,\"file\":\"DownloadButton.svelte\",\"sources\":[\"DownloadButton.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport {} from \\\\\"@gradio/client\\\\\";\\\\nimport { BaseButton } from \\\\\"@gradio/button\\\\\";\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let visible = true;\\\\nexport let variant = \\\\\"secondary\\\\\";\\\\nexport let size = \\\\\"lg\\\\\";\\\\nexport let value;\\\\nexport let icon;\\\\nexport let disabled = false;\\\\nexport let scale = null;\\\\nexport let min_width = void 0;\\\\nconst dispatch = createEventDispatcher();\\\\nfunction download_file() {\\\\n    dispatch(\\\\\"click\\\\\");\\\\n    if (!value?.url) {\\\\n        return;\\\\n    }\\\\n    let file_name;\\\\n    if (!value.orig_name && value.url) {\\\\n        const parts = value.url.split(\\\\\"/\\\\\");\\\\n        file_name = parts[parts.length - 1];\\\\n        file_name = file_name.split(\\\\\"?\\\\\")[0].split(\\\\\"#\\\\\")[0];\\\\n    }\\\\n    else {\\\\n        file_name = value.orig_name;\\\\n    }\\\\n    const a = document.createElement(\\\\\"a\\\\\");\\\\n    a.href = value.url;\\\\n    a.download = file_name || \\\\\"file\\\\\";\\\\n    document.body.appendChild(a);\\\\n    a.click();\\\\n    document.body.removeChild(a);\\\\n}\\\\n<\\/script>\\\\n\\\\n<BaseButton\\\\n\\\\t{size}\\\\n\\\\t{variant}\\\\n\\\\t{elem_id}\\\\n\\\\t{elem_classes}\\\\n\\\\t{visible}\\\\n\\\\ton:click={download_file}\\\\n\\\\t{scale}\\\\n\\\\t{min_width}\\\\n\\\\t{disabled}\\\\n>\\\\n\\\\t{#if icon}\\\\n\\\\t\\\\t<img class=\\\\\"button-icon\\\\\" src={icon.url} alt={`${value} icon`} />\\\\n\\\\t{/if}\\\\n\\\\t<slot />\\\\n</BaseButton>\\\\n\\\\n<style>\\\\n\\\\t.button-icon {\\\\n\\\\t\\\\twidth: var(--text-xl);\\\\n\\\\t\\\\theight: var(--text-xl);\\\\n\\\\t\\\\tmargin-right: var(--spacing-xl);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAuDC,0BAAa,CACZ,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,YAAY,CAAE,IAAI,YAAY,CAC/B\"}'\n};\nconst DownloadButton = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { variant = \"secondary\" } = $$props;\n  let { size = \"lg\" } = $$props;\n  let { value } = $$props;\n  let { icon } = $$props;\n  let { disabled = false } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  createEventDispatcher();\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.variant === void 0 && $$bindings.variant && variant !== void 0)\n    $$bindings.variant(variant);\n  if ($$props.size === void 0 && $$bindings.size && size !== void 0)\n    $$bindings.size(size);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.icon === void 0 && $$bindings.icon && icon !== void 0)\n    $$bindings.icon(icon);\n  if ($$props.disabled === void 0 && $$bindings.disabled && disabled !== void 0)\n    $$bindings.disabled(disabled);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  $$result.css.add(css);\n  return `${validate_component(Button, \"BaseButton\").$$render(\n    $$result,\n    {\n      size,\n      variant,\n      elem_id,\n      elem_classes,\n      visible,\n      scale,\n      min_width,\n      disabled\n    },\n    {},\n    {\n      default: () => {\n        return `${icon ? `<img class=\"button-icon svelte-yjn27e\"${add_attribute(\"src\", icon.url, 0)}${add_attribute(\"alt\", `${value} icon`, 0)}>` : ``} ${slots.default ? slots.default({}) : ``}`;\n      }\n    }\n  )}`;\n});\nconst DownloadButton$1 = DownloadButton;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value } = $$props;\n  let { variant = \"secondary\" } = $$props;\n  let { interactive } = $$props;\n  let { size = \"lg\" } = $$props;\n  let { scale = null } = $$props;\n  let { icon = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { label = null } = $$props;\n  let { gradio } = $$props;\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.variant === void 0 && $$bindings.variant && variant !== void 0)\n    $$bindings.variant(variant);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.size === void 0 && $$bindings.size && size !== void 0)\n    $$bindings.size(size);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.icon === void 0 && $$bindings.icon && icon !== void 0)\n    $$bindings.icon(icon);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  return `${validate_component(DownloadButton$1, \"DownloadButton\").$$render(\n    $$result,\n    {\n      value,\n      variant,\n      elem_id,\n      elem_classes,\n      size,\n      scale,\n      icon,\n      min_width,\n      visible,\n      disabled: !interactive\n    },\n    {},\n    {\n      default: () => {\n        return `${escape(label ? gradio.i18n(label) : \"\")}`;\n      }\n    }\n  )}`;\n});\nexport {\n  DownloadButton$1 as BaseButton,\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,uGAAuG;AAC/G,EAAE,GAAG,EAAE,myDAAmyD;AAC1yD,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,OAAO,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC7D,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI;AACV,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,IAAI,GAAG,CAAC,sCAAsC,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnM,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN,CAAC,CAAC,CAAC;AACE,MAAC,gBAAgB,GAAG,eAAe;AACnC,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,OAAO,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,QAAQ;AAC3E,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM,IAAI;AACV,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,QAAQ,EAAE,CAAC,WAAW;AAC5B,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN,CAAC;;;;"}