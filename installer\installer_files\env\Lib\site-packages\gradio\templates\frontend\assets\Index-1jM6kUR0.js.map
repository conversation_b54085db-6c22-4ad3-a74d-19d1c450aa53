{"version": 3, "file": "Index-1jM6kUR0.js", "sources": ["../../../../js/textbox/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as BaseTextbox } from \"./shared/Textbox.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, CopyData } from \"@gradio/utils\";\n\timport TextBox from \"./shared/Textbox.svelte\";\n\timport { Block } from \"@gradio/atoms\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\texport let gradio: Gradio<{\n\t\tchange: string;\n\t\tsubmit: never;\n\t\tblur: never;\n\t\tselect: SelectData;\n\t\tinput: never;\n\t\tfocus: never;\n\t\tstop: never;\n\t\tclear_status: LoadingStatus;\n\t\tcopy: CopyData;\n\t}>;\n\texport let label = \"Textbox\";\n\texport let info: string | undefined = undefined;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value = \"\";\n\texport let lines: number;\n\texport let placeholder = \"\";\n\texport let show_label: boolean;\n\texport let max_lines: number;\n\texport let type: \"text\" | \"password\" | \"email\" = \"text\";\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let submit_btn: string | boolean | null = null;\n\texport let stop_btn: string | boolean | null = null;\n\texport let show_copy_button = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let value_is_output = false;\n\texport let rtl = false;\n\texport let text_align: \"left\" | \"right\" | undefined = undefined;\n\texport let autofocus = false;\n\texport let autoscroll = true;\n\texport let interactive: boolean;\n\texport let root: string;\n\texport let max_length: number | undefined = undefined;\n</script>\n\n<Block\n\t{visible}\n\t{elem_id}\n\t{elem_classes}\n\t{scale}\n\t{min_width}\n\tallow_overflow={false}\n\tpadding={container}\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\n\t<TextBox\n\t\tbind:value\n\t\tbind:value_is_output\n\t\t{label}\n\t\t{info}\n\t\t{root}\n\t\t{show_label}\n\t\t{lines}\n\t\t{type}\n\t\t{rtl}\n\t\t{text_align}\n\t\tmax_lines={!max_lines ? lines + 1 : max_lines}\n\t\t{placeholder}\n\t\t{submit_btn}\n\t\t{stop_btn}\n\t\t{show_copy_button}\n\t\t{autofocus}\n\t\t{container}\n\t\t{autoscroll}\n\t\t{max_length}\n\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\ton:input={() => gradio.dispatch(\"input\")}\n\t\ton:submit={() => gradio.dispatch(\"submit\")}\n\t\ton:blur={() => gradio.dispatch(\"blur\")}\n\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\ton:focus={() => gradio.dispatch(\"focus\")}\n\t\ton:stop={() => gradio.dispatch(\"stop\")}\n\t\ton:copy={(e) => gradio.dispatch(\"copy\", e.detail)}\n\t\tdisabled={!interactive}\n\t/>\n</Block>\n"], "names": ["ctx", "dirty", "create_if_block", "gradio", "$$props", "label", "info", "elem_id", "elem_classes", "visible", "value", "lines", "placeholder", "show_label", "max_lines", "type", "container", "scale", "min_width", "submit_btn", "stop_btn", "show_copy_button", "loading_status", "value_is_output", "rtl", "text_align", "autofocus", "autoscroll", "interactive", "root", "max_length", "clear_status_handler", "change_handler"], "mappings": "quCAgEe,CAAA,WAAAA,KAAO,UAAU,EACvB,CAAA,KAAAA,KAAO,IAAI,EACbA,EAAc,EAAA,0LAFNC,EAAA,CAAA,EAAA,GAAA,CAAA,WAAAD,KAAO,UAAU,EACvBC,EAAA,CAAA,EAAA,GAAA,CAAA,KAAAD,KAAO,IAAI,kBACbA,EAAc,EAAA,CAAA,gIAJfA,EAAc,EAAA,GAAAE,EAAAF,CAAA,mKAoBNA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,sJAiBrBA,EAAW,EAAA,qaArClBA,EAAc,EAAA,mXAoBNA,EAAS,EAAA,EAAeA,EAAS,EAAA,EAArBA,KAAQ,+SAiBrBA,EAAW,EAAA,iWAxCP,WACPA,EAAS,EAAA,iRAATA,EAAS,EAAA,qKA9CP,GAAA,CAAA,OAAAG,CAAA,EAAAC,GAWA,MAAAC,EAAQ,SAAA,EAAAD,GACR,KAAAE,EAA2B,MAAA,EAAAF,GAC3B,QAAAG,EAAU,EAAA,EAAAH,EACV,CAAA,aAAAI,EAAA,EAAA,EAAAJ,GACA,QAAAK,EAAU,EAAA,EAAAL,GACV,MAAAM,EAAQ,EAAA,EAAAN,EACR,CAAA,MAAAO,CAAA,EAAAP,GACA,YAAAQ,EAAc,EAAA,EAAAR,EACd,CAAA,WAAAS,CAAA,EAAAT,EACA,CAAA,UAAAU,CAAA,EAAAV,GACA,KAAAW,EAAsC,MAAA,EAAAX,GACtC,UAAAY,EAAY,EAAA,EAAAZ,GACZ,MAAAa,EAAuB,IAAA,EAAAb,GACvB,UAAAc,EAAgC,MAAA,EAAAd,GAChC,WAAAe,EAAsC,IAAA,EAAAf,GACtC,SAAAgB,EAAoC,IAAA,EAAAhB,GACpC,iBAAAiB,EAAmB,EAAA,EAAAjB,GACnB,eAAAkB,EAA4C,MAAA,EAAAlB,GAC5C,gBAAAmB,EAAkB,EAAA,EAAAnB,GAClB,IAAAoB,EAAM,EAAA,EAAApB,GACN,WAAAqB,EAA2C,MAAA,EAAArB,GAC3C,UAAAsB,EAAY,EAAA,EAAAtB,GACZ,WAAAuB,EAAa,EAAA,EAAAvB,EACb,CAAA,YAAAwB,CAAA,EAAAxB,EACA,CAAA,KAAAyB,CAAA,EAAAzB,GACA,WAAA0B,EAAiC,MAAA,EAAA1B,EAiBnB,MAAA2B,EAAA,IAAA5B,EAAO,SAAS,eAAgBmB,CAAc,oDAwBrD,MAAAU,EAAA,IAAA7B,EAAO,SAAS,SAAUO,CAAK,QAChCP,EAAO,SAAS,OAAO,QACtBA,EAAO,SAAS,QAAQ,QAC1BA,EAAO,SAAS,MAAM,IACzB,GAAMA,EAAO,SAAS,SAAU,EAAE,MAAM,QACpCA,EAAO,SAAS,OAAO,QACxBA,EAAO,SAAS,MAAM,IAC3B,GAAMA,EAAO,SAAS,OAAQ,EAAE,MAAM"}