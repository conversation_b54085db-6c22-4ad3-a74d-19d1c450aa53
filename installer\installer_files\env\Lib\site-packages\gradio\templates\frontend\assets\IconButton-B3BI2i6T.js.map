{"version": 3, "file": "IconButton-B3BI2i6T.js", "sources": ["../../../../js/atoms/src/IconButton.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport { type ComponentType } from \"svelte\";\n\texport let Icon: ComponentType;\n\texport let label = \"\";\n\texport let show_label = false;\n\texport let pending = false;\n\texport let size: \"small\" | \"large\" | \"medium\" = \"small\";\n\texport let padded = true;\n\texport let highlight = false;\n\texport let disabled = false;\n\texport let hasPopup = false;\n\texport let color = \"var(--block-label-text-color)\";\n\texport let transparent = false;\n\texport let background = \"var(--block-background-fill)\";\n\t$: _color = highlight ? \"var(--color-accent)\" : color;\n</script>\n\n<button\n\t{disabled}\n\ton:click\n\taria-label={label}\n\taria-haspopup={hasPopup}\n\ttitle={label}\n\tclass:pending\n\tclass:padded\n\tclass:highlight\n\tclass:transparent\n\tstyle:color={!disabled && _color ? _color : \"var(--block-label-text-color)\"}\n\tstyle:--bg-color={!disabled ? background : \"auto\"}\n>\n\t{#if show_label}<span>{label}</span>{/if}\n\t<div\n\t\tclass:small={size === \"small\"}\n\t\tclass:large={size === \"large\"}\n\t\tclass:medium={size === \"medium\"}\n\t>\n\t\t<svelte:component this={Icon} />\n\t\t<slot />\n\t</div>\n</button>\n\n<style>\n\tbutton {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: 1px;\n\t\tz-index: var(--layer-2);\n\t\tborder-radius: var(--radius-xs);\n\t\tcolor: var(--block-label-text-color);\n\t\tborder: 1px solid transparent;\n\t\tpadding: var(--spacing-xxs);\n\t}\n\n\tbutton:hover {\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\tbutton[disabled] {\n\t\topacity: 0.5;\n\t\tbox-shadow: none;\n\t}\n\n\tbutton[disabled]:hover {\n\t\tcursor: not-allowed;\n\t}\n\n\t.padded {\n\t\tbackground: var(--bg-color);\n\t}\n\n\tbutton:hover,\n\tbutton.highlight {\n\t\tcursor: pointer;\n\t\tcolor: var(--color-accent);\n\t}\n\n\t.padded:hover {\n\t\tcolor: var(--block-label-text-color);\n\t}\n\n\tspan {\n\t\tpadding: 0px 1px;\n\t\tfont-size: 10px;\n\t}\n\n\tdiv {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: filter 0.2s ease-in-out;\n\t}\n\n\t.small {\n\t\twidth: 14px;\n\t\theight: 14px;\n\t}\n\n\t.medium {\n\t\twidth: 20px;\n\t\theight: 20px;\n\t}\n\n\t.large {\n\t\twidth: 22px;\n\t\theight: 22px;\n\t}\n\n\t.pending {\n\t\tanimation: flash 0.5s infinite;\n\t}\n\n\t@keyframes flash {\n\t\t0% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.5;\n\t\t}\n\t}\n\n\t.transparent {\n\t\tbackground: transparent;\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t}\n</style>\n"], "names": ["ctx", "insert", "target", "span", "anchor", "create_if_block", "toggle_class", "div", "set_style", "button", "append", "Icon", "$$props", "label", "show_label", "pending", "size", "padded", "highlight", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "color", "transparent", "background", "$$invalidate", "_color"], "mappings": "yiBA8BwBA,EAAK,CAAA,CAAA,wCAAZC,EAAoBC,EAAAC,EAAAC,CAAA,0BAAbJ,EAAK,CAAA,CAAA,sDAAvBA,EAAU,CAAA,GAAAK,EAAAL,CAAA,QAMUA,EAAI,CAAA,yMAJfM,EAAAC,EAAA,QAAAP,OAAS,OAAO,EAChBM,EAAAC,EAAA,QAAAP,OAAS,OAAO,EACfM,EAAAC,EAAA,SAAAP,OAAS,QAAQ,mCAdpBA,EAAK,CAAA,CAAA,sBACFA,EAAQ,CAAA,CAAA,cAChBA,EAAK,CAAA,CAAA,qHAKEQ,EAAAC,EAAA,QAAA,CAAAT,MAAYA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAG,+BAA+B,mBACxDA,EAAQ,CAAA,EAAgB,OAAbA,EAAU,EAAA,CAAS,UAXlDC,EAsBQC,EAAAO,EAAAL,CAAA,wBARPM,EAOKD,EAAAF,CAAA,wFARAP,EAAU,CAAA,oEAMUA,EAAI,CAAA,GAAA,iNAJfM,EAAAC,EAAA,QAAAP,OAAS,OAAO,cAChBM,EAAAC,EAAA,QAAAP,OAAS,OAAO,cACfM,EAAAC,EAAA,SAAAP,OAAS,QAAQ,6DAdpBA,EAAK,CAAA,CAAA,mCACFA,EAAQ,CAAA,CAAA,yBAChBA,EAAK,CAAA,CAAA,+IAKEQ,EAAAC,EAAA,QAAA,CAAAT,MAAYA,EAAM,EAAA,EAAGA,EAAM,EAAA,EAAG,+BAA+B,2BACxDA,EAAQ,CAAA,EAAgB,OAAbA,EAAU,EAAA,CAAS,8LA1BtC,CAAA,KAAAW,CAAA,EAAAC,GACA,MAAAC,EAAQ,EAAA,EAAAD,GACR,WAAAE,EAAa,EAAA,EAAAF,GACb,QAAAG,EAAU,EAAA,EAAAH,GACV,KAAAI,EAAqC,OAAA,EAAAJ,GACrC,OAAAK,EAAS,EAAA,EAAAL,GACT,UAAAM,EAAY,EAAA,EAAAN,GACZ,SAAAO,EAAW,EAAA,EAAAP,GACX,SAAAQ,EAAW,EAAA,EAAAR,GACX,MAAAS,EAAQ,+BAAA,EAAAT,GACR,YAAAU,EAAc,EAAA,EAAAV,GACd,WAAAW,EAAa,8BAAA,EAAAX,ggBACrBY,EAAA,GAAAC,EAASP,EAAY,sBAAwBG,CAAA"}