{"version": 3, "file": "Index-B2Iya1X_.js", "sources": ["../../../../js/image/Index.svelte"], "sourcesContent": ["<svelte:options accessors={true} />\n\n<script context=\"module\" lang=\"ts\">\n\texport { default as Webcam } from \"./shared/Webcam.svelte\";\n\texport { default as BaseImageUploader } from \"./shared/ImageUploader.svelte\";\n\texport { default as BaseStaticImage } from \"./shared/ImagePreview.svelte\";\n\texport { default as BaseExample } from \"./Example.svelte\";\n\texport { default as BaseImage } from \"./shared/Image.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, ValueData } from \"@gradio/utils\";\n\timport StaticImage from \"./shared/ImagePreview.svelte\";\n\timport ImageUploader from \"./shared/ImageUploader.svelte\";\n\timport { afterUpdate } from \"svelte\";\n\n\timport { Block, Empty, UploadText } from \"@gradio/atoms\";\n\timport { Image } from \"@gradio/icons\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport { upload, type FileData } from \"@gradio/client\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\n\ttype sources = \"upload\" | \"webcam\" | \"clipboard\" | null;\n\n\tlet stream_state = \"closed\";\n\tlet _modify_stream: (state: \"open\" | \"closed\" | \"waiting\") => void = () => {};\n\texport function modify_stream_state(\n\t\tstate: \"open\" | \"closed\" | \"waiting\"\n\t): void {\n\t\tstream_state = state;\n\t\t_modify_stream(state);\n\t}\n\texport const get_stream_state: () => void = () => stream_state;\n\texport let set_time_limit: (arg0: number) => void;\n\texport let value_is_output = false;\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: null | FileData = null;\n\tlet old_value: null | FileData = null;\n\texport let label: string;\n\texport let show_label: boolean;\n\texport let show_download_button: boolean;\n\texport let root: string;\n\n\texport let height: number | undefined;\n\texport let width: number | undefined;\n\texport let stream_every: number;\n\n\texport let _selectable = false;\n\texport let container = true;\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let loading_status: LoadingStatus;\n\texport let show_share_button = false;\n\texport let sources: (\"clipboard\" | \"webcam\" | \"upload\")[] = [\n\t\t\"upload\",\n\t\t\"clipboard\",\n\t\t\"webcam\"\n\t];\n\texport let interactive: boolean;\n\texport let streaming: boolean;\n\texport let pending: boolean;\n\texport let mirror_webcam: boolean;\n\texport let placeholder: string | undefined = undefined;\n\texport let show_fullscreen_button: boolean;\n\texport let input_ready: boolean;\n\texport let webcam_constraints: { [key: string]: any } | undefined = undefined;\n\tlet uploading = false;\n\t$: input_ready = !uploading;\n\texport let gradio: Gradio<{\n\t\tinput: never;\n\t\tchange: never;\n\t\terror: string;\n\t\tedit: never;\n\t\tstream: ValueData;\n\t\tdrag: never;\n\t\tupload: never;\n\t\tclear: never;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\tclear_status: LoadingStatus;\n\t\tclose_stream: string;\n\t}>;\n\n\t$: {\n\t\tif (JSON.stringify(value) !== JSON.stringify(old_value)) {\n\t\t\told_value = value;\n\t\t\tgradio.dispatch(\"change\");\n\t\t\tif (!value_is_output) {\n\t\t\t\tgradio.dispatch(\"input\");\n\t\t\t}\n\t\t}\n\t}\n\n\tafterUpdate(() => {\n\t\tvalue_is_output = false;\n\t});\n\n\tlet dragging: boolean;\n\tlet active_source: sources = null;\n\tlet upload_component: ImageUploader;\n\tconst handle_drag_event = (event: Event): void => {\n\t\tconst drag_event = event as DragEvent;\n\t\tdrag_event.preventDefault();\n\t\tdrag_event.stopPropagation();\n\t\tif (drag_event.type === \"dragenter\" || drag_event.type === \"dragover\") {\n\t\t\tdragging = true;\n\t\t} else if (drag_event.type === \"dragleave\") {\n\t\t\tdragging = false;\n\t\t}\n\t};\n\n\tconst handle_drop = (event: Event): void => {\n\t\tif (interactive) {\n\t\t\tconst drop_event = event as DragEvent;\n\t\t\tdrop_event.preventDefault();\n\t\t\tdrop_event.stopPropagation();\n\t\t\tdragging = false;\n\n\t\t\tif (upload_component) {\n\t\t\t\tupload_component.loadFilesFromDrop(drop_event);\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n{#if !interactive}\n\t<Block\n\t\t{visible}\n\t\tvariant={\"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t/>\n\t\t<StaticImage\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => gradio.dispatch(\"error\", detail)}\n\t\t\t{value}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{show_download_button}\n\t\t\tselectable={_selectable}\n\t\t\t{show_share_button}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{show_fullscreen_button}\n\t\t/>\n\t</Block>\n{:else}\n\t<Block\n\t\t{visible}\n\t\tvariant={value === null ? \"dashed\" : \"solid\"}\n\t\tborder_mode={dragging ? \"focus\" : \"base\"}\n\t\tpadding={false}\n\t\t{elem_id}\n\t\t{elem_classes}\n\t\theight={height || undefined}\n\t\t{width}\n\t\tallow_overflow={false}\n\t\t{container}\n\t\t{scale}\n\t\t{min_width}\n\t\ton:dragenter={handle_drag_event}\n\t\ton:dragleave={handle_drag_event}\n\t\ton:dragover={handle_drag_event}\n\t\ton:drop={handle_drop}\n\t>\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\n\t\t<ImageUploader\n\t\t\tbind:this={upload_component}\n\t\t\tbind:uploading\n\t\t\tbind:active_source\n\t\t\tbind:value\n\t\t\tbind:dragging\n\t\t\tselectable={_selectable}\n\t\t\t{root}\n\t\t\t{sources}\n\t\t\ton:edit={() => gradio.dispatch(\"edit\")}\n\t\t\ton:clear={() => {\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:stream={({ detail }) => gradio.dispatch(\"stream\", detail)}\n\t\t\ton:drag={({ detail }) => (dragging = detail)}\n\t\t\ton:upload={() => gradio.dispatch(\"upload\")}\n\t\t\ton:select={({ detail }) => gradio.dispatch(\"select\", detail)}\n\t\t\ton:share={({ detail }) => gradio.dispatch(\"share\", detail)}\n\t\t\ton:error={({ detail }) => {\n\t\t\t\tloading_status = loading_status || {};\n\t\t\t\tloading_status.status = \"error\";\n\t\t\t\tgradio.dispatch(\"error\", detail);\n\t\t\t}}\n\t\t\ton:close_stream={() => {\n\t\t\t\tgradio.dispatch(\"close_stream\", \"stream\");\n\t\t\t}}\n\t\t\t{label}\n\t\t\t{show_label}\n\t\t\t{pending}\n\t\t\t{streaming}\n\t\t\t{mirror_webcam}\n\t\t\t{stream_every}\n\t\t\tbind:modify_stream={_modify_stream}\n\t\t\tbind:set_time_limit\n\t\t\t{webcam_constraints}\n\t\t\tmax_file_size={gradio.max_file_size}\n\t\t\ti18n={gradio.i18n}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\tstream_handler={gradio.client?.stream}\n\t\t>\n\t\t\t{#if active_source === \"upload\" || !active_source}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"image\" {placeholder} />\n\t\t\t{:else if active_source === \"clipboard\"}\n\t\t\t\t<UploadText i18n={gradio.i18n} type=\"clipboard\" mode=\"short\" />\n\t\t\t{:else}\n\t\t\t\t<Empty unpadded_box={true} size=\"large\"><Image /></Empty>\n\t\t\t{/if}\n\t\t</ImageUploader>\n\t</Block>\n{/if}\n"], "names": ["ctx", "dirty", "block_changes", "uploadtext_changes", "imageuploader_changes", "staticimage_changes", "stream_state", "_modify_stream", "modify_stream_state", "state", "get_stream_state", "set_time_limit", "$$props", "value_is_output", "elem_id", "elem_classes", "visible", "value", "old_value", "label", "show_label", "show_download_button", "root", "height", "width", "stream_every", "_selectable", "container", "scale", "min_width", "loading_status", "show_share_button", "sources", "interactive", "streaming", "pending", "mirror_webcam", "placeholder", "show_fullscreen_button", "input_ready", "webcam_constraints", "uploading", "gradio", "afterUpdate", "dragging", "active_source", "upload_component", "handle_drag_event", "event", "drag_event", "handle_drop", "drop_event", "select_handler", "detail", "share_handler", "error_handler", "clear_status_handler", "func", "args", "$$value", "stream_handler", "$$invalidate", "select_handler_1", "share_handler_1"], "mappings": "06DAc6B,EAAA,OAAA,uFAsJlB,QAAAA,EAAU,CAAA,IAAA,KAAO,SAAW,oBACxBA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,OAAU,kCAEF,2GAIFA,EAAiB,EAAA,CAAA,oBACjBA,EAAiB,EAAA,CAAA,mBAClBA,EAAiB,EAAA,CAAA,eACrBA,EAAW,EAAA,CAAA,2FAdXC,EAAA,CAAA,EAAA,IAAAC,EAAA,QAAAF,EAAU,CAAA,IAAA,KAAO,SAAW,wCACxBA,EAAQ,EAAA,EAAG,QAAU,gEAI1BC,EAAA,CAAA,EAAA,OAAAC,EAAA,OAAAF,OAAU,0VAvCT,oBACIA,EAAQ,EAAA,EAAG,QAAU,eACzB,kCAGD,OAAAA,OAAU,kCAEF,iNANHA,EAAQ,EAAA,EAAG,QAAU,gEAI1BC,EAAA,CAAA,EAAA,OAAAC,EAAA,OAAAF,OAAU,gVAiGK,+SAFH,KAAAA,MAAO,qGAAPC,EAAA,CAAA,EAAA,WAAAE,EAAA,KAAAH,MAAO,+IAFP,KAAAA,MAAO,sGAAPC,EAAA,CAAA,EAAA,WAAAE,EAAA,KAAAH,MAAO,qXADrBA,EAAa,EAAA,IAAK,UAAQ,CAAKA,EAAa,EAAA,EAAA,EAEvCA,QAAkB,YAAW,0VAhD3B,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,iQAUNA,EAAW,EAAA,mJA6BR,cAAAA,MAAO,cAChB,KAAAA,MAAO,iCAEGA,EAAM,EAAA,EAAC,QAAQ,mMANXA,EAAc,EAAA,IAAA,yBAAdA,EAAc,EAAA,wlBAtCtB,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,UAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,qDAUNA,EAAW,EAAA,sSA6BRC,EAAA,CAAA,EAAA,WAAAG,EAAA,cAAAJ,MAAO,eAChBC,EAAA,CAAA,EAAA,WAAAG,EAAA,KAAAJ,MAAO,uEAEGA,EAAM,EAAA,EAAC,QAAQ,0UANXA,EAAc,EAAA,oQA5EtB,WAAAA,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,CAAA,4KAUNA,EAAW,EAAA,0BAEjB,KAAAA,MAAO,+OAdD,WAAAA,MAAO,YACbC,EAAA,CAAA,EAAA,UAAA,CAAA,KAAAD,MAAO,IAAI,aACbA,EAAc,CAAA,CAAA,0KAUNA,EAAW,EAAA,4CAEjBC,EAAA,CAAA,EAAA,WAAAI,EAAA,KAAAL,MAAO,4QA9BVA,EAAW,EAAA,IAAA,8TAvGZM,EAAe,SACfC,EAAA,IAAA,YACYC,EACfC,EAAA,CAEAH,EAAeG,EACfF,EAAeE,CAAK,QAERC,EAAqC,IAAAJ,EACvC,GAAA,CAAA,eAAAK,CAAA,EAAAC,GACA,gBAAAC,EAAkB,EAAA,EAAAD,GAClB,QAAAE,EAAU,EAAA,EAAAF,EACV,CAAA,aAAAG,EAAA,EAAA,EAAAH,GACA,QAAAI,EAAU,EAAA,EAAAJ,GACV,MAAAK,EAAyB,IAAA,EAAAL,EAChCM,EAA6B,KACtB,CAAA,MAAAC,CAAA,EAAAP,EACA,CAAA,WAAAQ,CAAA,EAAAR,EACA,CAAA,qBAAAS,CAAA,EAAAT,EACA,CAAA,KAAAU,CAAA,EAAAV,EAEA,CAAA,OAAAW,CAAA,EAAAX,EACA,CAAA,MAAAY,CAAA,EAAAZ,EACA,CAAA,aAAAa,CAAA,EAAAb,GAEA,YAAAc,EAAc,EAAA,EAAAd,GACd,UAAAe,EAAY,EAAA,EAAAf,GACZ,MAAAgB,EAAuB,IAAA,EAAAhB,GACvB,UAAAiB,EAAgC,MAAA,EAAAjB,EAChC,CAAA,eAAAkB,CAAA,EAAAlB,GACA,kBAAAmB,EAAoB,EAAA,EAAAnB,EACpB,CAAA,QAAAoB,EAAA,CACV,SACA,YACA,QAAA,CAAA,EAAApB,EAEU,CAAA,YAAAqB,CAAA,EAAArB,EACA,CAAA,UAAAsB,CAAA,EAAAtB,EACA,CAAA,QAAAuB,CAAA,EAAAvB,EACA,CAAA,cAAAwB,CAAA,EAAAxB,GACA,YAAAyB,EAAkC,MAAA,EAAAzB,EAClC,CAAA,uBAAA0B,CAAA,EAAA1B,EACA,CAAA,YAAA2B,CAAA,EAAA3B,GACA,mBAAA4B,EAAyD,MAAA,EAAA5B,EAChE6B,EAAY,GAEL,CAAA,OAAAC,CAAA,EAAA9B,EAyBX+B,GAAA,IAAA,MACC9B,EAAkB,EAAA,IAGf,IAAA+B,EACAC,EAAyB,KACzBC,QACEC,GAAqBC,GAAA,OACpBC,EAAaD,EACnBC,EAAW,eAAA,EACXA,EAAW,gBAAA,EACPA,EAAW,OAAS,aAAeA,EAAW,OAAS,gBAC1DL,EAAW,EAAA,EACDK,EAAW,OAAS,kBAC9BL,EAAW,EAAA,GAIPM,GAAeF,GAAA,CAChB,GAAAf,EAAA,OACGkB,EAAaH,EACnBG,EAAW,eAAA,EACXA,EAAW,gBAAA,OACXP,EAAW,EAAA,EAEPE,GACHA,EAAiB,kBAAkBK,CAAU,IA2BhCC,GAAA,CAAA,CAAA,OAAAC,KAAaX,EAAO,SAAS,SAAUW,CAAM,EAC9CC,GAAA,CAAA,CAAA,OAAAD,KAAaX,EAAO,SAAS,QAASW,CAAM,EAC5CE,GAAA,CAAA,CAAA,OAAAF,KAAaX,EAAO,SAAS,QAASW,CAAM,EAkClCG,GAAA,IAAAd,EAAO,SAAS,eAAgBZ,CAAc,EAwCzD2B,GAAA,IAAAC,IAAShB,EAAO,OAAO,UAAUgB,CAAI,4CApCtCZ,EAAgBa,sIA+BPpD,EAAcU,iDAvBnByB,EAAO,SAAS,MAAM,UAEpCA,EAAO,SAAS,OAAO,GAEVkB,GAAA,CAAA,CAAA,OAAAP,KAAaX,EAAO,SAAS,SAAUW,CAAM,OAC/C,OAAAA,CAAM,IAAAQ,EAAA,GAAQjB,EAAWS,CAAM,SAC1BX,EAAO,SAAS,QAAQ,EAC3BoB,GAAA,CAAA,CAAA,OAAAT,KAAaX,EAAO,SAAS,SAAUW,CAAM,EAC9CU,GAAA,CAAA,CAAA,OAAAV,KAAaX,EAAO,SAAS,QAASW,CAAM,OAC5C,OAAAA,KAAM,CAClBQ,EAAA,EAAA/B,EAAiBA,GAAc,CAAA,CAAA,MAC/BA,EAAe,OAAS,QAAOA,CAAA,EAC/BY,EAAO,SAAS,QAASW,CAAM,WAG/BX,EAAO,SAAS,eAAgB,QAAQ,0rCA9I3CmB,EAAA,GAAGtB,EAAe,CAAAE,CAAA,4CAiBb,KAAK,UAAUxB,CAAK,IAAM,KAAK,UAAUC,CAAS,SACrDA,EAAYD,CAAA,EACZyB,EAAO,SAAS,QAAQ,EACnB7B,GACJ6B,EAAO,SAAS,OAAO"}