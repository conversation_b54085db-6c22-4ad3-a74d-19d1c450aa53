import{n as Qe,w as Ue}from"./index-BkoKOheB.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{I as We}from"./IconButton-B3BI2i6T.js";import{C as xe}from"./Clear-By3xiIwg.js";const Ze=typeof window<"u";let ae=Ze?()=>window.performance.now():()=>Date.now(),Se=Ze?i=>requestAnimationFrame(i):Qe;const R=new Set;function De(i){R.forEach(t=>{t.c(i)||(R.delete(t),t.f())}),R.size!==0&&Se(De)}function $e(i){let t;return R.size===0&&Se(De),{promise:new Promise(e=>{R.add(t={c:i,f:e})}),abort(){R.delete(t)}}}function O(i){let t=["","k","M","G","T","P","E","Z"],e=0;for(;i>1e3&&e<t.length-1;)i/=1e3,e++;let l=t[e];return(Number.isInteger(i)?i:i.toFixed(1))+l}function ce(i){return Object.prototype.toString.call(i)==="[object Date]"}function ie(i,t,e,l){if(typeof e=="number"||ce(e)){const n=l-e,o=(e-t)/(i.dt||1/60),s=i.opts.stiffness*n,r=i.opts.damping*o,u=(s-r)*i.inv_mass,f=(o+u)*i.dt;return Math.abs(f)<i.opts.precision&&Math.abs(n)<i.opts.precision?l:(i.settled=!1,ce(e)?new Date(e.getTime()+f):e+f)}else{if(Array.isArray(e))return e.map((n,o)=>ie(i,t[o],e[o],l[o]));if(typeof e=="object"){const n={};for(const o in e)n[o]=ie(i,t[o],e[o],l[o]);return n}else throw new Error(`Cannot spring ${typeof e} values`)}}function de(i,t={}){const e=Ue(i),{stiffness:l=.15,damping:n=.8,precision:o=.01}=t;let s,r,u,f=i,_=i,a=1,g=0,b=!1;function k(F,L={}){_=F;const y=u={};return i==null||L.hard||Z.stiffness>=1&&Z.damping>=1?(b=!0,s=ae(),f=F,e.set(i=_),Promise.resolve()):(L.soft&&(g=1/((L.soft===!0?.5:+L.soft)*60),a=0),r||(s=ae(),b=!1,r=$e(c=>{if(b)return b=!1,r=null,!1;a=Math.min(a+g,1);const w={inv_mass:a,opts:Z,settled:!0,dt:(c-s)*60/1e3},A=ie(w,f,i,_);return s=c,f=i,e.set(i=A),w.settled&&(r=null),!w.settled})),new Promise(c=>{r.promise.then(()=>{y===u&&c()})}))}const Z={set:k,update:(F,L)=>k(F(_,i),L),subscribe:e.subscribe,stiffness:l,damping:n,precision:o};return Z}const{SvelteComponent:et,append:j,attr:h,component_subscribe:me,detach:tt,element:lt,flush:it,init:nt,insert:st,noop:pe,safe_not_equal:ot,set_style:x,svg_element:M,toggle_class:ge}=window.__gradio__svelte__internal,{onMount:rt}=window.__gradio__svelte__internal;function ft(i){let t,e,l,n,o,s,r,u,f,_,a,g;return{c(){t=lt("div"),e=M("svg"),l=M("g"),n=M("path"),o=M("path"),s=M("path"),r=M("path"),u=M("g"),f=M("path"),_=M("path"),a=M("path"),g=M("path"),h(n,"d","M255.926 0.754768L509.702 139.936V221.027L255.926 81.8465V0.754768Z"),h(n,"fill","#FF7C00"),h(n,"fill-opacity","0.4"),h(n,"class","svelte-zyxd38"),h(o,"d","M509.69 139.936L254.981 279.641V361.255L509.69 221.55V139.936Z"),h(o,"fill","#FF7C00"),h(o,"class","svelte-zyxd38"),h(s,"d","M0.250138 139.937L254.981 279.641V361.255L0.250138 221.55V139.937Z"),h(s,"fill","#FF7C00"),h(s,"fill-opacity","0.4"),h(s,"class","svelte-zyxd38"),h(r,"d","M255.923 0.232622L0.236328 139.936V221.55L255.923 81.8469V0.232622Z"),h(r,"fill","#FF7C00"),h(r,"class","svelte-zyxd38"),x(l,"transform","translate("+i[1][0]+"px, "+i[1][1]+"px)"),h(f,"d","M255.926 141.5L509.702 280.681V361.773L255.926 222.592V141.5Z"),h(f,"fill","#FF7C00"),h(f,"fill-opacity","0.4"),h(f,"class","svelte-zyxd38"),h(_,"d","M509.69 280.679L254.981 420.384V501.998L509.69 362.293V280.679Z"),h(_,"fill","#FF7C00"),h(_,"class","svelte-zyxd38"),h(a,"d","M0.250138 280.681L254.981 420.386V502L0.250138 362.295V280.681Z"),h(a,"fill","#FF7C00"),h(a,"fill-opacity","0.4"),h(a,"class","svelte-zyxd38"),h(g,"d","M255.923 140.977L0.236328 280.68V362.294L255.923 222.591V140.977Z"),h(g,"fill","#FF7C00"),h(g,"class","svelte-zyxd38"),x(u,"transform","translate("+i[2][0]+"px, "+i[2][1]+"px)"),h(e,"viewBox","-1200 -1200 3000 3000"),h(e,"fill","none"),h(e,"xmlns","http://www.w3.org/2000/svg"),h(e,"class","svelte-zyxd38"),h(t,"class","svelte-zyxd38"),ge(t,"margin",i[0])},m(b,k){st(b,t,k),j(t,e),j(e,l),j(l,n),j(l,o),j(l,s),j(l,r),j(e,u),j(u,f),j(u,_),j(u,a),j(u,g)},p(b,[k]){k&2&&x(l,"transform","translate("+b[1][0]+"px, "+b[1][1]+"px)"),k&4&&x(u,"transform","translate("+b[2][0]+"px, "+b[2][1]+"px)"),k&1&&ge(t,"margin",b[0])},i:pe,o:pe,d(b){b&&tt(t)}}}function ut(i,t,e){let l,n,{margin:o=!0}=t;const s=de([0,0]);me(i,s,g=>e(1,l=g));const r=de([0,0]);me(i,r,g=>e(2,n=g));let u;async function f(){await Promise.all([s.set([125,140]),r.set([-125,-140])]),await Promise.all([s.set([-125,140]),r.set([125,-140])]),await Promise.all([s.set([-125,0]),r.set([125,-0])]),await Promise.all([s.set([125,0]),r.set([-125,0])])}async function _(){await f(),u||_()}async function a(){await Promise.all([s.set([125,0]),r.set([-125,0])]),_()}return rt(()=>(a(),()=>u=!0)),i.$$set=g=>{"margin"in g&&e(0,o=g.margin)},[o,l,n,s,r]}class _t extends et{constructor(t){super(),nt(this,t,ut,ft,ot,{margin:0})}get margin(){return this.$$.ctx[0]}set margin(t){this.$$set({margin:t}),it()}}const{SvelteComponent:at,append:X,attr:P,binding_callbacks:be,check_outros:ne,create_component:Ae,create_slot:Ee,destroy_component:Ie,destroy_each:Be,detach:m,element:S,empty:H,ensure_array_like:ee,flush:z,get_all_dirty_from_scope:Te,get_slot_changes:Xe,group_outros:se,init:ct,insert:p,mount_component:Ye,noop:oe,safe_not_equal:dt,set_data:C,set_style:I,space:V,text:v,toggle_class:q,transition_in:N,transition_out:D,update_slot_base:Ge}=window.__gradio__svelte__internal,{tick:mt}=window.__gradio__svelte__internal,{onDestroy:pt}=window.__gradio__svelte__internal,{createEventDispatcher:gt}=window.__gradio__svelte__internal,bt=i=>({}),he=i=>({}),ht=i=>({}),we=i=>({});function ke(i,t,e){const l=i.slice();return l[40]=t[e],l[42]=e,l}function ye(i,t,e){const l=i.slice();return l[40]=t[e],l}function wt(i){let t,e,l,n,o=i[1]("common.error")+"",s,r,u;e=new We({props:{Icon:xe,label:i[1]("common.clear"),disabled:!1}}),e.$on("click",i[32]);const f=i[30].error,_=Ee(f,i,i[29],he);return{c(){t=S("div"),Ae(e.$$.fragment),l=V(),n=S("span"),s=v(o),r=V(),_&&_.c(),P(t,"class","clear-status svelte-ls20lj"),P(n,"class","error svelte-ls20lj")},m(a,g){p(a,t,g),Ye(e,t,null),p(a,l,g),p(a,n,g),X(n,s),p(a,r,g),_&&_.m(a,g),u=!0},p(a,g){const b={};g[0]&2&&(b.label=a[1]("common.clear")),e.$set(b),(!u||g[0]&2)&&o!==(o=a[1]("common.error")+"")&&C(s,o),_&&_.p&&(!u||g[0]&536870912)&&Ge(_,f,a,a[29],u?Xe(f,a[29],g,bt):Te(a[29]),he)},i(a){u||(N(e.$$.fragment,a),N(_,a),u=!0)},o(a){D(e.$$.fragment,a),D(_,a),u=!1},d(a){a&&(m(t),m(l),m(n),m(r)),Ie(e),_&&_.d(a)}}}function kt(i){let t,e,l,n,o,s,r,u,f,_=i[8]==="default"&&i[18]&&i[6]==="full"&&ve(i);function a(c,w){if(c[7])return Ft;if(c[2]!==null&&c[3]!==void 0&&c[2]>=0)return vt;if(c[2]===0)return yt}let g=a(i),b=g&&g(i),k=i[5]&&Le(i);const Z=[Vt,qt],F=[];function L(c,w){return c[15]!=null?0:c[6]==="full"?1:-1}~(o=L(i))&&(s=F[o]=Z[o](i));let y=!i[5]&&Pe(i);return{c(){_&&_.c(),t=V(),e=S("div"),b&&b.c(),l=V(),k&&k.c(),n=V(),s&&s.c(),r=V(),y&&y.c(),u=H(),P(e,"class","progress-text svelte-ls20lj"),q(e,"meta-text-center",i[8]==="center"),q(e,"meta-text",i[8]==="default")},m(c,w){_&&_.m(c,w),p(c,t,w),p(c,e,w),b&&b.m(e,null),X(e,l),k&&k.m(e,null),p(c,n,w),~o&&F[o].m(c,w),p(c,r,w),y&&y.m(c,w),p(c,u,w),f=!0},p(c,w){c[8]==="default"&&c[18]&&c[6]==="full"?_?_.p(c,w):(_=ve(c),_.c(),_.m(t.parentNode,t)):_&&(_.d(1),_=null),g===(g=a(c))&&b?b.p(c,w):(b&&b.d(1),b=g&&g(c),b&&(b.c(),b.m(e,l))),c[5]?k?k.p(c,w):(k=Le(c),k.c(),k.m(e,null)):k&&(k.d(1),k=null),(!f||w[0]&256)&&q(e,"meta-text-center",c[8]==="center"),(!f||w[0]&256)&&q(e,"meta-text",c[8]==="default");let A=o;o=L(c),o===A?~o&&F[o].p(c,w):(s&&(se(),D(F[A],1,1,()=>{F[A]=null}),ne()),~o?(s=F[o],s?s.p(c,w):(s=F[o]=Z[o](c),s.c()),N(s,1),s.m(r.parentNode,r)):s=null),c[5]?y&&(se(),D(y,1,1,()=>{y=null}),ne()):y?(y.p(c,w),w[0]&32&&N(y,1)):(y=Pe(c),y.c(),N(y,1),y.m(u.parentNode,u))},i(c){f||(N(s),N(y),f=!0)},o(c){D(s),D(y),f=!1},d(c){c&&(m(t),m(e),m(n),m(r),m(u)),_&&_.d(c),b&&b.d(),k&&k.d(),~o&&F[o].d(c),y&&y.d(c)}}}function ve(i){let t,e=`translateX(${(i[17]||0)*100-100}%)`;return{c(){t=S("div"),P(t,"class","eta-bar svelte-ls20lj"),I(t,"transform",e)},m(l,n){p(l,t,n)},p(l,n){n[0]&131072&&e!==(e=`translateX(${(l[17]||0)*100-100}%)`)&&I(t,"transform",e)},d(l){l&&m(t)}}}function yt(i){let t;return{c(){t=v("processing |")},m(e,l){p(e,t,l)},p:oe,d(e){e&&m(t)}}}function vt(i){let t,e=i[2]+1+"",l,n,o,s;return{c(){t=v("queue: "),l=v(e),n=v("/"),o=v(i[3]),s=v(" |")},m(r,u){p(r,t,u),p(r,l,u),p(r,n,u),p(r,o,u),p(r,s,u)},p(r,u){u[0]&4&&e!==(e=r[2]+1+"")&&C(l,e),u[0]&8&&C(o,r[3])},d(r){r&&(m(t),m(l),m(n),m(o),m(s))}}}function Ft(i){let t,e=ee(i[7]),l=[];for(let n=0;n<e.length;n+=1)l[n]=ze(ye(i,e,n));return{c(){for(let n=0;n<l.length;n+=1)l[n].c();t=H()},m(n,o){for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(n,o);p(n,t,o)},p(n,o){if(o[0]&128){e=ee(n[7]);let s;for(s=0;s<e.length;s+=1){const r=ye(n,e,s);l[s]?l[s].p(r,o):(l[s]=ze(r),l[s].c(),l[s].m(t.parentNode,t))}for(;s<l.length;s+=1)l[s].d(1);l.length=e.length}},d(n){n&&m(t),Be(l,n)}}}function Fe(i){let t,e=i[40].unit+"",l,n,o=" ",s;function r(_,a){return _[40].length!=null?Lt:zt}let u=r(i),f=u(i);return{c(){f.c(),t=V(),l=v(e),n=v(" | "),s=v(o)},m(_,a){f.m(_,a),p(_,t,a),p(_,l,a),p(_,n,a),p(_,s,a)},p(_,a){u===(u=r(_))&&f?f.p(_,a):(f.d(1),f=u(_),f&&(f.c(),f.m(t.parentNode,t))),a[0]&128&&e!==(e=_[40].unit+"")&&C(l,e)},d(_){_&&(m(t),m(l),m(n),m(s)),f.d(_)}}}function zt(i){let t=O(i[40].index||0)+"",e;return{c(){e=v(t)},m(l,n){p(l,e,n)},p(l,n){n[0]&128&&t!==(t=O(l[40].index||0)+"")&&C(e,t)},d(l){l&&m(e)}}}function Lt(i){let t=O(i[40].index||0)+"",e,l,n=O(i[40].length)+"",o;return{c(){e=v(t),l=v("/"),o=v(n)},m(s,r){p(s,e,r),p(s,l,r),p(s,o,r)},p(s,r){r[0]&128&&t!==(t=O(s[40].index||0)+"")&&C(e,t),r[0]&128&&n!==(n=O(s[40].length)+"")&&C(o,n)},d(s){s&&(m(e),m(l),m(o))}}}function ze(i){let t,e=i[40].index!=null&&Fe(i);return{c(){e&&e.c(),t=H()},m(l,n){e&&e.m(l,n),p(l,t,n)},p(l,n){l[40].index!=null?e?e.p(l,n):(e=Fe(l),e.c(),e.m(t.parentNode,t)):e&&(e.d(1),e=null)},d(l){l&&m(t),e&&e.d(l)}}}function Le(i){let t,e=i[0]?`/${i[19]}`:"",l,n;return{c(){t=v(i[20]),l=v(e),n=v("s")},m(o,s){p(o,t,s),p(o,l,s),p(o,n,s)},p(o,s){s[0]&1048576&&C(t,o[20]),s[0]&524289&&e!==(e=o[0]?`/${o[19]}`:"")&&C(l,e)},d(o){o&&(m(t),m(l),m(n))}}}function qt(i){let t,e;return t=new _t({props:{margin:i[8]==="default"}}),{c(){Ae(t.$$.fragment)},m(l,n){Ye(t,l,n),e=!0},p(l,n){const o={};n[0]&256&&(o.margin=l[8]==="default"),t.$set(o)},i(l){e||(N(t.$$.fragment,l),e=!0)},o(l){D(t.$$.fragment,l),e=!1},d(l){Ie(t,l)}}}function Vt(i){let t,e,l,n,o,s=`${i[15]*100}%`,r=i[7]!=null&&qe(i);return{c(){t=S("div"),e=S("div"),r&&r.c(),l=V(),n=S("div"),o=S("div"),P(e,"class","progress-level-inner svelte-ls20lj"),P(o,"class","progress-bar svelte-ls20lj"),I(o,"width",s),P(n,"class","progress-bar-wrap svelte-ls20lj"),P(t,"class","progress-level svelte-ls20lj")},m(u,f){p(u,t,f),X(t,e),r&&r.m(e,null),X(t,l),X(t,n),X(n,o),i[31](o)},p(u,f){u[7]!=null?r?r.p(u,f):(r=qe(u),r.c(),r.m(e,null)):r&&(r.d(1),r=null),f[0]&32768&&s!==(s=`${u[15]*100}%`)&&I(o,"width",s)},i:oe,o:oe,d(u){u&&m(t),r&&r.d(),i[31](null)}}}function qe(i){let t,e=ee(i[7]),l=[];for(let n=0;n<e.length;n+=1)l[n]=Ne(ke(i,e,n));return{c(){for(let n=0;n<l.length;n+=1)l[n].c();t=H()},m(n,o){for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(n,o);p(n,t,o)},p(n,o){if(o[0]&16512){e=ee(n[7]);let s;for(s=0;s<e.length;s+=1){const r=ke(n,e,s);l[s]?l[s].p(r,o):(l[s]=Ne(r),l[s].c(),l[s].m(t.parentNode,t))}for(;s<l.length;s+=1)l[s].d(1);l.length=e.length}},d(n){n&&m(t),Be(l,n)}}}function Ve(i){let t,e,l,n,o=i[42]!==0&&Ct(),s=i[40].desc!=null&&Ce(i),r=i[40].desc!=null&&i[14]&&i[14][i[42]]!=null&&je(),u=i[14]!=null&&Me(i);return{c(){o&&o.c(),t=V(),s&&s.c(),e=V(),r&&r.c(),l=V(),u&&u.c(),n=H()},m(f,_){o&&o.m(f,_),p(f,t,_),s&&s.m(f,_),p(f,e,_),r&&r.m(f,_),p(f,l,_),u&&u.m(f,_),p(f,n,_)},p(f,_){f[40].desc!=null?s?s.p(f,_):(s=Ce(f),s.c(),s.m(e.parentNode,e)):s&&(s.d(1),s=null),f[40].desc!=null&&f[14]&&f[14][f[42]]!=null?r||(r=je(),r.c(),r.m(l.parentNode,l)):r&&(r.d(1),r=null),f[14]!=null?u?u.p(f,_):(u=Me(f),u.c(),u.m(n.parentNode,n)):u&&(u.d(1),u=null)},d(f){f&&(m(t),m(e),m(l),m(n)),o&&o.d(f),s&&s.d(f),r&&r.d(f),u&&u.d(f)}}}function Ct(i){let t;return{c(){t=v(" /")},m(e,l){p(e,t,l)},d(e){e&&m(t)}}}function Ce(i){let t=i[40].desc+"",e;return{c(){e=v(t)},m(l,n){p(l,e,n)},p(l,n){n[0]&128&&t!==(t=l[40].desc+"")&&C(e,t)},d(l){l&&m(e)}}}function je(i){let t;return{c(){t=v("-")},m(e,l){p(e,t,l)},d(e){e&&m(t)}}}function Me(i){let t=(100*(i[14][i[42]]||0)).toFixed(1)+"",e,l;return{c(){e=v(t),l=v("%")},m(n,o){p(n,e,o),p(n,l,o)},p(n,o){o[0]&16384&&t!==(t=(100*(n[14][n[42]]||0)).toFixed(1)+"")&&C(e,t)},d(n){n&&(m(e),m(l))}}}function Ne(i){let t,e=(i[40].desc!=null||i[14]&&i[14][i[42]]!=null)&&Ve(i);return{c(){e&&e.c(),t=H()},m(l,n){e&&e.m(l,n),p(l,t,n)},p(l,n){l[40].desc!=null||l[14]&&l[14][l[42]]!=null?e?e.p(l,n):(e=Ve(l),e.c(),e.m(t.parentNode,t)):e&&(e.d(1),e=null)},d(l){l&&m(t),e&&e.d(l)}}}function Pe(i){let t,e,l,n;const o=i[30]["additional-loading-text"],s=Ee(o,i,i[29],we);return{c(){t=S("p"),e=v(i[9]),l=V(),s&&s.c(),P(t,"class","loading svelte-ls20lj")},m(r,u){p(r,t,u),X(t,e),p(r,l,u),s&&s.m(r,u),n=!0},p(r,u){(!n||u[0]&512)&&C(e,r[9]),s&&s.p&&(!n||u[0]&536870912)&&Ge(s,o,r,r[29],n?Xe(o,r[29],u,ht):Te(r[29]),we)},i(r){n||(N(s,r),n=!0)},o(r){D(s,r),n=!1},d(r){r&&(m(t),m(l)),s&&s.d(r)}}}function jt(i){let t,e,l,n,o;const s=[kt,wt],r=[];function u(f,_){return f[4]==="pending"?0:f[4]==="error"?1:-1}return~(e=u(i))&&(l=r[e]=s[e](i)),{c(){t=S("div"),l&&l.c(),P(t,"class",n="wrap "+i[8]+" "+i[6]+" svelte-ls20lj"),q(t,"hide",!i[4]||i[4]==="complete"||i[6]==="hidden"||i[4]=="streaming"),q(t,"translucent",i[8]==="center"&&(i[4]==="pending"||i[4]==="error")||i[11]||i[6]==="minimal"),q(t,"generating",i[4]==="generating"&&i[6]==="full"),q(t,"border",i[12]),I(t,"position",i[10]?"absolute":"static"),I(t,"padding",i[10]?"0":"var(--size-8) 0")},m(f,_){p(f,t,_),~e&&r[e].m(t,null),i[33](t),o=!0},p(f,_){let a=e;e=u(f),e===a?~e&&r[e].p(f,_):(l&&(se(),D(r[a],1,1,()=>{r[a]=null}),ne()),~e?(l=r[e],l?l.p(f,_):(l=r[e]=s[e](f),l.c()),N(l,1),l.m(t,null)):l=null),(!o||_[0]&320&&n!==(n="wrap "+f[8]+" "+f[6]+" svelte-ls20lj"))&&P(t,"class",n),(!o||_[0]&336)&&q(t,"hide",!f[4]||f[4]==="complete"||f[6]==="hidden"||f[4]=="streaming"),(!o||_[0]&2384)&&q(t,"translucent",f[8]==="center"&&(f[4]==="pending"||f[4]==="error")||f[11]||f[6]==="minimal"),(!o||_[0]&336)&&q(t,"generating",f[4]==="generating"&&f[6]==="full"),(!o||_[0]&4416)&&q(t,"border",f[12]),_[0]&1024&&I(t,"position",f[10]?"absolute":"static"),_[0]&1024&&I(t,"padding",f[10]?"0":"var(--size-8) 0")},i(f){o||(N(l),o=!0)},o(f){D(l),o=!1},d(f){f&&m(t),~e&&r[e].d(),i[33](null)}}}let $=[],le=!1;const Mt=typeof window<"u",Oe=Mt?window.requestAnimationFrame:i=>{};async function Nt(i,t=!0){if(!(window.__gradio_mode__==="website"||window.__gradio_mode__!=="app"&&t!==!0)){if($.push(i),!le)le=!0;else return;await mt(),Oe(()=>{let e=[0,0];for(let l=0;l<$.length;l++){const o=$[l].getBoundingClientRect();(l===0||o.top+window.scrollY<=e[0])&&(e[0]=o.top+window.scrollY,e[1]=l)}window.scrollTo({top:e[0]-20,behavior:"smooth"}),le=!1,$=[]})}}function Pt(i,t,e){let l,{$$slots:n={},$$scope:o}=t;const s=gt();let{i18n:r}=t,{eta:u=null}=t,{queue_position:f}=t,{queue_size:_}=t,{status:a}=t,{scroll_to_output:g=!1}=t,{timer:b=!0}=t,{show_progress:k="full"}=t,{message:Z=null}=t,{progress:F=null}=t,{variant:L="default"}=t,{loading_text:y="Loading..."}=t,{absolute:c=!0}=t,{translucent:w=!1}=t,{border:A=!1}=t,{autoscroll:te}=t,J,K=!1,U=0,B=0,Y=null,G=null,re=0,T=null,Q,E=null,fe=!0;const Re=()=>{e(0,u=e(27,Y=e(19,W=null))),e(25,U=performance.now()),e(26,B=0),K=!0,ue()};function ue(){Oe(()=>{e(26,B=(performance.now()-U)/1e3),K&&ue()})}function _e(){e(26,B=0),e(0,u=e(27,Y=e(19,W=null))),K&&(K=!1)}pt(()=>{K&&_e()});let W=null;function He(d){be[d?"unshift":"push"](()=>{E=d,e(16,E),e(7,F),e(14,T),e(15,Q)})}const Je=()=>{s("clear_status")};function Ke(d){be[d?"unshift":"push"](()=>{J=d,e(13,J)})}return i.$$set=d=>{"i18n"in d&&e(1,r=d.i18n),"eta"in d&&e(0,u=d.eta),"queue_position"in d&&e(2,f=d.queue_position),"queue_size"in d&&e(3,_=d.queue_size),"status"in d&&e(4,a=d.status),"scroll_to_output"in d&&e(22,g=d.scroll_to_output),"timer"in d&&e(5,b=d.timer),"show_progress"in d&&e(6,k=d.show_progress),"message"in d&&e(23,Z=d.message),"progress"in d&&e(7,F=d.progress),"variant"in d&&e(8,L=d.variant),"loading_text"in d&&e(9,y=d.loading_text),"absolute"in d&&e(10,c=d.absolute),"translucent"in d&&e(11,w=d.translucent),"border"in d&&e(12,A=d.border),"autoscroll"in d&&e(24,te=d.autoscroll),"$$scope"in d&&e(29,o=d.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&436207617&&(u===null&&e(0,u=Y),u!=null&&Y!==u&&(e(28,G=(performance.now()-U)/1e3+u),e(19,W=G.toFixed(1)),e(27,Y=u))),i.$$.dirty[0]&335544320&&e(17,re=G===null||G<=0||!B?null:Math.min(B/G,1)),i.$$.dirty[0]&128&&F!=null&&e(18,fe=!1),i.$$.dirty[0]&114816&&(F!=null?e(14,T=F.map(d=>{if(d.index!=null&&d.length!=null)return d.index/d.length;if(d.progress!=null)return d.progress})):e(14,T=null),T?(e(15,Q=T[T.length-1]),E&&(Q===0?e(16,E.style.transition="0",E):e(16,E.style.transition="150ms",E))):e(15,Q=void 0)),i.$$.dirty[0]&16&&(a==="pending"?Re():_e()),i.$$.dirty[0]&20979728&&J&&g&&(a==="pending"||a==="complete")&&Nt(J,te),i.$$.dirty[0]&8388624,i.$$.dirty[0]&67108864&&e(20,l=B.toFixed(1))},[u,r,f,_,a,b,k,F,L,y,c,w,A,J,T,Q,E,re,fe,W,l,s,g,Z,te,U,B,Y,G,o,n,He,Je,Ke]}class Bt extends at{constructor(t){super(),ct(this,t,Pt,jt,dt,{i18n:1,eta:0,queue_position:2,queue_size:3,status:4,scroll_to_output:22,timer:5,show_progress:6,message:23,progress:7,variant:8,loading_text:9,absolute:10,translucent:11,border:12,autoscroll:24},null,[-1,-1])}get i18n(){return this.$$.ctx[1]}set i18n(t){this.$$set({i18n:t}),z()}get eta(){return this.$$.ctx[0]}set eta(t){this.$$set({eta:t}),z()}get queue_position(){return this.$$.ctx[2]}set queue_position(t){this.$$set({queue_position:t}),z()}get queue_size(){return this.$$.ctx[3]}set queue_size(t){this.$$set({queue_size:t}),z()}get status(){return this.$$.ctx[4]}set status(t){this.$$set({status:t}),z()}get scroll_to_output(){return this.$$.ctx[22]}set scroll_to_output(t){this.$$set({scroll_to_output:t}),z()}get timer(){return this.$$.ctx[5]}set timer(t){this.$$set({timer:t}),z()}get show_progress(){return this.$$.ctx[6]}set show_progress(t){this.$$set({show_progress:t}),z()}get message(){return this.$$.ctx[23]}set message(t){this.$$set({message:t}),z()}get progress(){return this.$$.ctx[7]}set progress(t){this.$$set({progress:t}),z()}get variant(){return this.$$.ctx[8]}set variant(t){this.$$set({variant:t}),z()}get loading_text(){return this.$$.ctx[9]}set loading_text(t){this.$$set({loading_text:t}),z()}get absolute(){return this.$$.ctx[10]}set absolute(t){this.$$set({absolute:t}),z()}get translucent(){return this.$$.ctx[11]}set translucent(t){this.$$set({translucent:t}),z()}get border(){return this.$$.ctx[12]}set border(t){this.$$set({border:t}),z()}get autoscroll(){return this.$$.ctx[24]}set autoscroll(t){this.$$set({autoscroll:t}),z()}}export{_t as L,Bt as S,de as s};
//# sourceMappingURL=index-DrEzyPwM.js.map
