import{c as mt,f as Z}from"./index-C30MtwUc.js";import{h as ht}from"./index-BkoKOheB.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{p as x}from"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";const{SvelteComponent:gt,append:pt,attr:g,detach:vt,init:wt,insert:$t,noop:U,safe_not_equal:kt,svg_element:tt}=window.__gradio__svelte__internal;function bt(i){let t,e;return{c(){t=tt("svg"),e=tt("path"),g(e,"stroke-linecap","round"),g(e,"stroke-linejoin","round"),g(e,"d","M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"),g(t,"fill","none"),g(t,"stroke","currentColor"),g(t,"viewBox","0 0 24 24"),g(t,"width","100%"),g(t,"height","100%"),g(t,"xmlns","http://www.w3.org/2000/svg"),g(t,"aria-hidden","true"),g(t,"stroke-width","2"),g(t,"stroke-linecap","round"),g(t,"stroke-linejoin","round")},m(n,o){$t(n,t,o),pt(t,e)},p:U,i:U,o:U,d(n){n&&vt(t)}}}class yt extends gt{constructor(t){super(),wt(this,t,null,bt,kt,{})}}const{SvelteComponent:Ct,append:qt,attr:p,detach:Mt,init:St,insert:zt,noop:V,safe_not_equal:Tt,svg_element:et}=window.__gradio__svelte__internal;function jt(i){let t,e;return{c(){t=et("svg"),e=et("path"),p(e,"stroke-linecap","round"),p(e,"stroke-linejoin","round"),p(e,"d","M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"),p(t,"fill","none"),p(t,"stroke","currentColor"),p(t,"viewBox","0 0 24 24"),p(t,"width","100%"),p(t,"height","100%"),p(t,"xmlns","http://www.w3.org/2000/svg"),p(t,"aria-hidden","true"),p(t,"stroke-width","2"),p(t,"stroke-linecap","round"),p(t,"stroke-linejoin","round")},m(n,o){zt(n,t,o),qt(t,e)},p:V,i:V,o:V,d(n){n&&Mt(t)}}}class Ht extends Ct{constructor(t){super(),St(this,t,null,jt,Tt,{})}}const{SvelteComponent:Lt,append:At,attr:v,detach:Bt,init:Ft,insert:It,noop:W,safe_not_equal:Et,svg_element:nt}=window.__gradio__svelte__internal;function Ot(i){let t,e;return{c(){t=nt("svg"),e=nt("path"),v(e,"stroke-linecap","round"),v(e,"stroke-linejoin","round"),v(e,"d","M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"),v(t,"fill","none"),v(t,"stroke","currentColor"),v(t,"stroke-width","2"),v(t,"viewBox","0 0 24 24"),v(t,"width","100%"),v(t,"height","100%"),v(t,"xmlns","http://www.w3.org/2000/svg"),v(t,"aria-hidden","true"),v(t,"stroke-linecap","round"),v(t,"stroke-linejoin","round")},m(n,o){It(n,t,o),At(t,e)},p:W,i:W,o:W,d(n){n&&Bt(t)}}}class Rt extends Lt{constructor(t){super(),Ft(this,t,null,Ot,Et,{})}}function Dt(i,{from:t,to:e},n={}){const o=getComputedStyle(i),c=o.transform==="none"?"":o.transform,[f,l]=o.transformOrigin.split(" ").map(parseFloat),s=t.left+t.width*f/e.width-(e.left+f),r=t.top+t.height*l/e.height-(e.top+l),{delay:d=0,duration:y=w=>Math.sqrt(w)*120,easing:b=mt}=n;return{delay:d,duration:ht(y)?y(Math.sqrt(s*s+r*r)):y,easing:b,css:(w,k)=>{const S=k*s,a=k*r,q=w+k*t.width/e.width,z=w+k*t.height/e.height;return`transform: ${c} translate(${S}px, ${a}px) scale(${q}, ${z});`}}}const{SvelteComponent:Ut,add_render_callback:Vt,append:$,attr:_,bubble:it,check_outros:Wt,create_component:J,create_in_transition:Gt,create_out_transition:Jt,destroy_component:K,detach:Kt,element:C,flush:j,group_outros:Nt,init:Pt,insert:Qt,listen:G,mount_component:N,run_all:Xt,safe_not_equal:Yt,set_data:Zt,space:O,stop_propagation:st,text:xt,toggle_class:ot,transition_in:L,transition_out:A}=window.__gradio__svelte__internal,{createEventDispatcher:te,onMount:ee}=window.__gradio__svelte__internal;function ne(i){let t,e;return t=new yt({}),{c(){J(t.$$.fragment)},m(n,o){N(t,n,o),e=!0},i(n){e||(L(t.$$.fragment,n),e=!0)},o(n){A(t.$$.fragment,n),e=!1},d(n){K(t,n)}}}function ie(i){let t,e;return t=new Ht({}),{c(){J(t.$$.fragment)},m(n,o){N(t,n,o),e=!0},i(n){e||(L(t.$$.fragment,n),e=!0)},o(n){A(t.$$.fragment,n),e=!1},d(n){K(t,n)}}}function se(i){let t,e;return t=new Rt({}),{c(){J(t.$$.fragment)},m(n,o){N(t,n,o),e=!0},i(n){e||(L(t.$$.fragment,n),e=!0)},o(n){A(t.$$.fragment,n),e=!1},d(n){K(t,n)}}}function oe(i){let t,e,n,o,c,f,l,s,r,d,y,b,w,k,S,a,q,z,P,T,B,F,I,E,H,m,R,Q;const X=[se,ie,ne],M=[];function Y(u,h){return u[2]==="warning"?0:u[2]==="info"?1:u[2]==="error"?2:-1}return~(n=Y(i))&&(o=M[n]=X[n](i)),{c(){t=C("div"),e=C("div"),o&&o.c(),f=O(),l=C("div"),s=C("div"),r=xt(i[1]),y=O(),b=C("div"),S=O(),a=C("button"),q=C("span"),q.textContent="×",P=O(),T=C("div"),_(e,"class",c="toast-icon "+i[2]+" svelte-utw7m1"),_(s,"class",d="toast-title "+i[2]+" svelte-utw7m1"),_(b,"class",w="toast-text "+i[2]+" svelte-utw7m1"),_(l,"class",k="toast-details "+i[2]+" svelte-utw7m1"),_(q,"aria-hidden","true"),_(a,"class",z="toast-close "+i[2]+" svelte-utw7m1"),_(a,"type","button"),_(a,"aria-label","Close"),_(a,"data-testid","toast-close"),_(T,"class",B="timer "+i[2]+" svelte-utw7m1"),_(T,"style",F=`animation-duration: ${i[3]};`),_(t,"class",I="toast-body "+i[2]+" svelte-utw7m1"),_(t,"role","alert"),_(t,"data-testid","toast-body"),ot(t,"hidden",!i[4])},m(u,h){Qt(u,t,h),$(t,e),~n&&M[n].m(e,null),$(t,f),$(t,l),$(l,s),$(s,r),$(l,y),$(l,b),b.innerHTML=i[0],$(t,S),$(t,a),$(a,q),$(t,P),$(t,T),m=!0,R||(Q=[G(a,"click",i[5]),G(t,"click",st(i[9])),G(t,"keydown",st(i[10]))],R=!0)},p(u,[h]){let D=n;n=Y(u),n!==D&&(o&&(Nt(),A(M[D],1,1,()=>{M[D]=null}),Wt()),~n?(o=M[n],o||(o=M[n]=X[n](u),o.c()),L(o,1),o.m(e,null)):o=null),(!m||h&4&&c!==(c="toast-icon "+u[2]+" svelte-utw7m1"))&&_(e,"class",c),(!m||h&2)&&Zt(r,u[1]),(!m||h&4&&d!==(d="toast-title "+u[2]+" svelte-utw7m1"))&&_(s,"class",d),(!m||h&1)&&(b.innerHTML=u[0]),(!m||h&4&&w!==(w="toast-text "+u[2]+" svelte-utw7m1"))&&_(b,"class",w),(!m||h&4&&k!==(k="toast-details "+u[2]+" svelte-utw7m1"))&&_(l,"class",k),(!m||h&4&&z!==(z="toast-close "+u[2]+" svelte-utw7m1"))&&_(a,"class",z),(!m||h&4&&B!==(B="timer "+u[2]+" svelte-utw7m1"))&&_(T,"class",B),(!m||h&8&&F!==(F=`animation-duration: ${u[3]};`))&&_(T,"style",F),(!m||h&4&&I!==(I="toast-body "+u[2]+" svelte-utw7m1"))&&_(t,"class",I),(!m||h&20)&&ot(t,"hidden",!u[4])},i(u){m||(L(o),u&&Vt(()=>{m&&(H&&H.end(1),E=Gt(t,Z,{duration:200,delay:100}),E.start())}),m=!0)},o(u){A(o),E&&E.invalidate(),u&&(H=Jt(t,Z,{duration:200})),m=!1},d(u){u&&Kt(t),~n&&M[n].d(),u&&H&&H.end(),R=!1,Xt(Q)}}}function re(i,t,e){let n,o,{title:c=""}=t,{message:f=""}=t,{type:l}=t,{id:s}=t,{duration:r=10}=t,{visible:d=!0}=t;const y=a=>{try{return!!a&&new URL(a,location.href).origin!==location.origin}catch{return!1}};x.addHook("afterSanitizeAttributes",function(a){"target"in a&&y(a.getAttribute("href"))&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"))});const b=te();function w(){b("close",s)}ee(()=>{r!==null&&setTimeout(()=>{w()},r*1e3)});function k(a){it.call(this,i,a)}function S(a){it.call(this,i,a)}return i.$$set=a=>{"title"in a&&e(1,c=a.title),"message"in a&&e(0,f=a.message),"type"in a&&e(2,l=a.type),"id"in a&&e(7,s=a.id),"duration"in a&&e(6,r=a.duration),"visible"in a&&e(8,d=a.visible)},i.$$.update=()=>{i.$$.dirty&1&&e(0,f=x.sanitize(f)),i.$$.dirty&256&&e(4,n=d),i.$$.dirty&64&&e(6,r=r||null),i.$$.dirty&64&&e(3,o=`${r||0}s`)},[f,c,l,o,n,w,r,s,d,k,S]}class le extends Ut{constructor(t){super(),Pt(this,t,re,oe,Yt,{title:1,message:0,type:2,id:7,duration:6,visible:8})}get title(){return this.$$.ctx[1]}set title(t){this.$$set({title:t}),j()}get message(){return this.$$.ctx[0]}set message(t){this.$$set({message:t}),j()}get type(){return this.$$.ctx[2]}set type(t){this.$$set({type:t}),j()}get id(){return this.$$.ctx[7]}set id(t){this.$$set({id:t}),j()}get duration(){return this.$$.ctx[6]}set duration(t){this.$$set({duration:t}),j()}get visible(){return this.$$.ctx[8]}set visible(t){this.$$set({visible:t}),j()}}const{SvelteComponent:ae,append:ue,attr:ce,bubble:_e,check_outros:de,create_animation:fe,create_component:me,destroy_component:he,detach:ut,element:ct,ensure_array_like:rt,fix_and_outro_and_destroy_block:ge,fix_position:pe,flush:ve,group_outros:we,init:$e,insert:_t,mount_component:ke,noop:be,safe_not_equal:ye,set_style:Ce,space:qe,transition_in:dt,transition_out:ft,update_keyed_each:Me}=window.__gradio__svelte__internal;function lt(i,t,e){const n=i.slice();return n[2]=t[e].type,n[3]=t[e].title,n[4]=t[e].message,n[5]=t[e].id,n[6]=t[e].duration,n[7]=t[e].visible,n}function at(i,t){let e,n,o,c,f=be,l;return n=new le({props:{type:t[2],title:t[3],message:t[4],duration:t[6],visible:t[7],id:t[5]}}),n.$on("close",t[1]),{key:i,first:null,c(){e=ct("div"),me(n.$$.fragment),o=qe(),Ce(e,"width","100%"),this.first=e},m(s,r){_t(s,e,r),ke(n,e,null),ue(e,o),l=!0},p(s,r){t=s;const d={};r&1&&(d.type=t[2]),r&1&&(d.title=t[3]),r&1&&(d.message=t[4]),r&1&&(d.duration=t[6]),r&1&&(d.visible=t[7]),r&1&&(d.id=t[5]),n.$set(d)},r(){c=e.getBoundingClientRect()},f(){pe(e),f()},a(){f(),f=fe(e,c,Dt,{duration:300})},i(s){l||(dt(n.$$.fragment,s),l=!0)},o(s){ft(n.$$.fragment,s),l=!1},d(s){s&&ut(e),he(n)}}}function Se(i){let t,e=[],n=new Map,o,c=rt(i[0]);const f=l=>l[5];for(let l=0;l<c.length;l+=1){let s=lt(i,c,l),r=f(s);n.set(r,e[l]=at(r,s))}return{c(){t=ct("div");for(let l=0;l<e.length;l+=1)e[l].c();ce(t,"class","toast-wrap svelte-pu0yf1")},m(l,s){_t(l,t,s);for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(t,null);o=!0},p(l,[s]){if(s&1){c=rt(l[0]),we();for(let r=0;r<e.length;r+=1)e[r].r();e=Me(e,s,f,1,l,c,n,t,ge,at,null,lt);for(let r=0;r<e.length;r+=1)e[r].a();de()}},i(l){if(!o){for(let s=0;s<c.length;s+=1)dt(e[s]);o=!0}},o(l){for(let s=0;s<e.length;s+=1)ft(e[s]);o=!1},d(l){l&&ut(t);for(let s=0;s<e.length;s+=1)e[s].d()}}}function ze(i){i.length>0&&"parentIFrame"in window&&window.parentIFrame?.scrollTo(0,0)}function Te(i,t,e){let{messages:n=[]}=t;function o(c){_e.call(this,i,c)}return i.$$set=c=>{"messages"in c&&e(0,n=c.messages)},i.$$.update=()=>{i.$$.dirty&1&&ze(n)},[n,o]}class Be extends ae{constructor(t){super(),$e(this,t,Te,Se,ye,{messages:0})}get messages(){return this.$$.ctx[0]}set messages(t){this.$$set({messages:t}),ve()}}export{Be as T};
//# sourceMappingURL=Toast-CheZYOII.js.map
