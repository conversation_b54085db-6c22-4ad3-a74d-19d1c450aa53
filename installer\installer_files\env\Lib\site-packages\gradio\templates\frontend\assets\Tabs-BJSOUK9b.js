import{w as le}from"./index-BkoKOheB.js";const{SvelteComponent:Ie,append:X,attr:p,detach:ze,init:Be,insert:Se,noop:Y,safe_not_equal:Te,svg_element:P}=window.__gradio__svelte__internal;function qe(t){let e,l,n,i;return{c(){e=P("svg"),l=P("circle"),n=P("circle"),i=P("circle"),p(l,"cx","2.5"),p(l,"cy","8"),p(l,"r","1.5"),p(l,"fill","currentColor"),p(n,"cx","8"),p(n,"cy","8"),p(n,"r","1.5"),p(n,"fill","currentColor"),p(i,"cx","13.5"),p(i,"cy","8"),p(i,"r","1.5"),p(i,"fill","currentColor"),p(e,"width","16"),p(e,"height","16"),p(e,"viewBox","0 0 16 16"),p(e,"fill","none"),p(e,"xmlns","http://www.w3.org/2000/svg")},m(_,c){Se(_,e,c),X(e,l),X(e,n),X(e,i)},p:Y,i:Y,o:Y,d(_){_&&ze(e)}}}class Me extends Ie{constructor(e){super(),Be(this,e,null,qe,Te,{})}}const{SvelteComponent:je,append:I,attr:v,binding_callbacks:Z,check_outros:Ee,component_subscribe:ie,create_component:Ne,create_slot:Oe,destroy_block:ne,destroy_component:Re,destroy_each:Ae,detach:O,element:M,empty:V,ensure_array_like:G,flush:H,get_all_dirty_from_scope:De,get_slot_changes:ye,group_outros:Fe,init:Ge,insert:R,listen:K,mount_component:He,run_all:Je,safe_not_equal:Ke,set_data:$,set_store_value:Q,space:D,stop_propagation:Le,text:x,toggle_class:B,transition_in:J,transition_out:U,update_keyed_each:se,update_slot_base:Pe}=window.__gradio__svelte__internal,{setContext:Qe,createEventDispatcher:Ue,tick:Ve,onMount:We}=window.__gradio__svelte__internal;function oe(t,e,l){const n=t.slice();return n[32]=e[l],n}function _e(t,e,l){const n=t.slice();return n[32]=e[l],n[36]=l,n}function ae(t,e,l){const n=t.slice();return n[32]=e[l],n[37]=e,n[36]=l,n}function ce(t){let e,l,n=[],i=new Map,_,c,a=[],m=new Map,u,f,b,z,h,C,g,q,A,S=G(t[3]);const j=o=>o[32].id;for(let o=0;o<S.length;o+=1){let d=ae(t,S,o),r=j(d);i.set(r,n[o]=re(r,d))}let E=G(t[7]);const y=o=>o[32].id;for(let o=0;o<E.length;o+=1){let d=_e(t,E,o),r=y(d);m.set(r,a[o]=de(r,d))}z=new Me({});let N=G(t[8]),k=[];for(let o=0;o<N.length;o+=1)k[o]=be(oe(t,N,o));return{c(){e=M("div"),l=M("div");for(let o=0;o<n.length;o+=1)n[o].c();_=D(),c=M("div");for(let o=0;o<a.length;o+=1)a[o].c();u=D(),f=M("span"),b=M("button"),Ne(z.$$.fragment),h=D(),C=M("div");for(let o=0;o<k.length;o+=1)k[o].c();v(l,"class","tab-container visually-hidden svelte-1tcem6n"),v(l,"aria-hidden","true"),v(c,"class","tab-container svelte-1tcem6n"),v(c,"role","tablist"),v(b,"class","svelte-1tcem6n"),B(b,"overflow-item-selected",t[12]),v(C,"class","overflow-dropdown svelte-1tcem6n"),B(C,"hide",!t[9]),v(f,"class","overflow-menu svelte-1tcem6n"),B(f,"hide",!t[11]),v(e,"class","tab-wrapper svelte-1tcem6n")},m(o,d){R(o,e,d),I(e,l);for(let r=0;r<n.length;r+=1)n[r]&&n[r].m(l,null);I(e,_),I(e,c);for(let r=0;r<a.length;r+=1)a[r]&&a[r].m(c,null);t[26](c),I(e,u),I(e,f),I(f,b),He(z,b,null),I(f,h),I(f,C);for(let r=0;r<k.length;r+=1)k[r]&&k[r].m(C,null);t[29](f),g=!0,q||(A=K(b,"click",Le(t[27])),q=!0)},p(o,d){if(d[0]&40&&(S=G(o[3]),n=se(n,d,j,1,o,S,i,l,ne,re,null,ae)),d[0]&196800&&(E=G(o[7]),a=se(a,d,y,1,o,E,m,c,ne,de,null,_e)),(!g||d[0]&4096)&&B(b,"overflow-item-selected",o[12]),d[0]&131392){N=G(o[8]);let r;for(r=0;r<N.length;r+=1){const F=oe(o,N,r);k[r]?k[r].p(F,d):(k[r]=be(F),k[r].c(),k[r].m(C,null))}for(;r<k.length;r+=1)k[r].d(1);k.length=N.length}(!g||d[0]&512)&&B(C,"hide",!o[9]),(!g||d[0]&2048)&&B(f,"hide",!o[11])},i(o){g||(J(z.$$.fragment,o),g=!0)},o(o){U(z.$$.fragment,o),g=!1},d(o){o&&O(e);for(let d=0;d<n.length;d+=1)n[d].d();for(let d=0;d<a.length;d+=1)a[d].d();t[26](null),Re(z),Ae(k,o),t[29](null),q=!1,A()}}}function fe(t){let e,l=t[32].label+"",n,i,_=t[32];const c=()=>t[24](e,_),a=()=>t[24](null,_);return{c(){e=M("button"),n=x(l),i=D(),v(e,"class","svelte-1tcem6n")},m(m,u){R(m,e,u),I(e,n),I(e,i),c()},p(m,u){t=m,u[0]&8&&l!==(l=t[32].label+"")&&$(n,l),_!==t[32]&&(a(),_=t[32],c())},d(m){m&&O(e),a()}}}function re(t,e){let l,n,i=e[32].visible&&fe(e);return{key:t,first:null,c(){l=V(),i&&i.c(),n=V(),this.first=l},m(_,c){R(_,l,c),i&&i.m(_,c),R(_,n,c)},p(_,c){e=_,e[32].visible?i?i.p(e,c):(i=fe(e),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(_){_&&(O(l),O(n)),i&&i.d(_)}}}function ue(t){let e,l=t[32].label+"",n,i,_,c,a,m,u,f,b,z;function h(){return t[25](t[32],t[36])}return{c(){e=M("button"),n=x(l),i=D(),v(e,"role","tab"),v(e,"aria-selected",_=t[32].id===t[6]),v(e,"aria-controls",c=t[32].elem_id),e.disabled=a=!t[32].interactive,v(e,"aria-disabled",m=!t[32].interactive),v(e,"id",u=t[32].elem_id?t[32].elem_id+"-button":null),v(e,"data-tab-id",f=t[32].id),v(e,"class","svelte-1tcem6n"),B(e,"selected",t[32].id===t[6])},m(C,g){R(C,e,g),I(e,n),I(e,i),b||(z=K(e,"click",h),b=!0)},p(C,g){t=C,g[0]&128&&l!==(l=t[32].label+"")&&$(n,l),g[0]&192&&_!==(_=t[32].id===t[6])&&v(e,"aria-selected",_),g[0]&128&&c!==(c=t[32].elem_id)&&v(e,"aria-controls",c),g[0]&128&&a!==(a=!t[32].interactive)&&(e.disabled=a),g[0]&128&&m!==(m=!t[32].interactive)&&v(e,"aria-disabled",m),g[0]&128&&u!==(u=t[32].elem_id?t[32].elem_id+"-button":null)&&v(e,"id",u),g[0]&128&&f!==(f=t[32].id)&&v(e,"data-tab-id",f),g[0]&192&&B(e,"selected",t[32].id===t[6])},d(C){C&&O(e),b=!1,z()}}}function de(t,e){let l,n,i=e[32].visible&&ue(e);return{key:t,first:null,c(){l=V(),i&&i.c(),n=V(),this.first=l},m(_,c){R(_,l,c),i&&i.m(_,c),R(_,n,c)},p(_,c){e=_,e[32].visible?i?i.p(e,c):(i=ue(e),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},d(_){_&&(O(l),O(n)),i&&i.d(_)}}}function be(t){let e,l=t[32].label+"",n,i,_,c;function a(){return t[28](t[32])}return{c(){e=M("button"),n=x(l),i=D(),v(e,"class","svelte-1tcem6n"),B(e,"selected",t[32].id===t[6])},m(m,u){R(m,e,u),I(e,n),I(e,i),_||(c=K(e,"click",a),_=!0)},p(m,u){t=m,u[0]&256&&l!==(l=t[32].label+"")&&$(n,l),u[0]&320&&B(e,"selected",t[32].id===t[6])},d(m){m&&O(e),_=!1,c()}}}function Xe(t){let e,l,n,i,_,c,a=t[13]&&ce(t);const m=t[23].default,u=Oe(m,t,t[22],null);return{c(){e=M("div"),a&&a.c(),l=D(),u&&u.c(),v(e,"class",n="tabs "+t[2].join(" ")+" svelte-1tcem6n"),v(e,"id",t[1]),B(e,"hide",!t[0])},m(f,b){R(f,e,b),a&&a.m(e,null),I(e,l),u&&u.m(e,null),i=!0,_||(c=[K(window,"resize",t[19]),K(window,"click",t[18])],_=!0)},p(f,b){f[13]?a?(a.p(f,b),b[0]&8192&&J(a,1)):(a=ce(f),a.c(),J(a,1),a.m(e,l)):a&&(Fe(),U(a,1,1,()=>{a=null}),Ee()),u&&u.p&&(!i||b[0]&4194304)&&Pe(u,m,f,f[22],i?ye(m,f[22],b,null):De(f[22]),null),(!i||b[0]&4&&n!==(n="tabs "+f[2].join(" ")+" svelte-1tcem6n"))&&v(e,"class",n),(!i||b[0]&2)&&v(e,"id",f[1]),(!i||b[0]&5)&&B(e,"hide",!f[0])},i(f){i||(J(a),J(u,f),i=!0)},o(f){U(a),U(u,f),i=!1},d(f){f&&O(e),a&&a.d(),u&&u.d(f),_=!1,Je(c)}}}const Ye={};function Ze(t,e){const l={};return t.forEach(n=>{l[n.id]=e[n.id]?.getBoundingClientRect()}),l}function $e(t,e,l){let n,i,_,{$$slots:c={},$$scope:a}=e,{visible:m=!0}=e,{elem_id:u=""}=e,{elem_classes:f=[]}=e,{selected:b}=e,{initial_tabs:z}=e,h=[...z],C=[...z],g=[],q=!1,A,S;const j=le(b||h[0]?.id||!1);ie(t,j,s=>l(6,i=s));const E=le(h.findIndex(s=>s.id===b)||0);ie(t,E,s=>l(30,_=s));const y=Ue();let N=!1,k=!1,o={};We(()=>{new IntersectionObserver(w=>{F()}).observe(S)}),Qe(Ye,{register_tab:s=>{let w=h.findIndex(T=>T.id===s.id);return w!==-1?l(3,h[w]={...h[w],...s},h):(l(3,h=[...h,s]),w=h.length-1),i===!1&&s.visible&&s.interactive&&Q(j,i=s.id,i),w},unregister_tab:s=>{h.findIndex(T=>T.id===s.id)!==-1&&(l(3,h=h.filter(T=>T.id!==s.id)),i===s.id&&Q(j,i=h[0]?.id||!1,i))},selected_tab:j,selected_tab_index:E});function d(s){const w=h.find(T=>T.id===s);w&&w.interactive&&w.visible&&i!==w.id&&(l(20,b=s),Q(j,i=s,i),Q(E,_=h.findIndex(T=>T.id===s),_),y("change"),l(9,q=!1))}function r(s){q&&A&&!A.contains(s.target)&&l(9,q=!1)}async function F(){if(!S)return;await Ve();const s=S.getBoundingClientRect();let w=s.width;const T=Ze(h,o);let W=0;const pe=s.left;for(let L=h.length-1;L>=0;L--){const Ce=h[L],te=T[Ce.id];if(te&&te.right-pe<w){W=L;break}}l(8,g=h.slice(W+1)),l(7,C=h.slice(0,W+1)),l(12,k=ee(i)),l(11,N=g.length>0)}function ee(s){return s===!1?!1:g.some(w=>w.id===s)}function he(s,w){Z[s?"unshift":"push"](()=>{o[w.id]=s,l(5,o)})}const me=(s,w)=>{s.id!==i&&(d(s.id),y("select",{value:s.label,index:w}))};function ve(s){Z[s?"unshift":"push"](()=>{S=s,l(4,S)})}const ge=()=>l(9,q=!q),we=s=>d(s.id);function ke(s){Z[s?"unshift":"push"](()=>{A=s,l(10,A)})}return t.$$set=s=>{"visible"in s&&l(0,m=s.visible),"elem_id"in s&&l(1,u=s.elem_id),"elem_classes"in s&&l(2,f=s.elem_classes),"selected"in s&&l(20,b=s.selected),"initial_tabs"in s&&l(21,z=s.initial_tabs),"$$scope"in s&&l(22,a=s.$$scope)},t.$$.update=()=>{t.$$.dirty[0]&8&&l(13,n=h.length>0),t.$$.dirty[0]&1048584&&b!==null&&d(b),t.$$.dirty[0]&56&&F(),t.$$.dirty[0]&64&&l(12,k=ee(i))},[m,u,f,h,S,o,i,C,g,q,A,N,k,n,j,E,y,d,r,F,b,z,a,c,he,me,ve,ge,we,ke]}class xe extends je{constructor(e){super(),Ge(this,e,$e,Xe,Ke,{visible:0,elem_id:1,elem_classes:2,selected:20,initial_tabs:21},null,[-1,-1])}get visible(){return this.$$.ctx[0]}set visible(e){this.$$set({visible:e}),H()}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),H()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),H()}get selected(){return this.$$.ctx[20]}set selected(e){this.$$set({selected:e}),H()}get initial_tabs(){return this.$$.ctx[21]}set initial_tabs(e){this.$$set({initial_tabs:e}),H()}}const tt=xe;export{tt as T,Ye as a};
//# sourceMappingURL=Tabs-BJSOUK9b.js.map
