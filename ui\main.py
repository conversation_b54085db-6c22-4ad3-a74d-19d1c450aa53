import os
import time
import gradio as gr
import roop.globals
import roop.metadata
import roop.utilities as util
import ui.globals as uii

from ui.tabs.faceswap_tab import faceswap_tab
from ui.tabs.livecam_tab import livecam_tab
from ui.tabs.facemgr_tab import facemgr_tab
from ui.tabs.extras_tab import extras_tab
from ui.tabs.settings_tab import settings_tab

roop.globals.keep_fps = None
roop.globals.keep_frames = None
roop.globals.skip_audio = None
roop.globals.use_batch = None


def prepare_environment():
    roop.globals.output_path = os.path.abspath(os.path.join(os.getcwd(), "output"))
    os.makedirs(roop.globals.output_path, exist_ok=True)
    if not roop.globals.CFG.use_os_temp_folder:
        temp_path = os.path.abspath(os.path.join(os.getcwd(), "temp"))
        os.environ["TEMP"] = temp_path
        os.environ["TMP"] = temp_path
        # On Windows, also set TMPDIR for compatibility
        if os.name == 'nt':
            os.environ["TMPDIR"] = temp_path
    os.makedirs(os.environ["TEMP"], exist_ok=True)
    os.environ["GRADIO_TEMP_DIR"] = os.environ["TEMP"]
    os.environ['GRADIO_ANALYTICS_ENABLED'] = '0'

    # Windows-specific environment settings to help with ASGI issues
    if os.name == 'nt':
        os.environ['GRADIO_SERVER_NAME'] = '127.0.0.1'
        os.environ['UVICORN_HOST'] = '127.0.0.1'
        os.environ['UVICORN_PORT'] = '7860'
        # Disable some features that can cause issues on Windows
        os.environ['GRADIO_SHARE'] = 'False'
        os.environ['GRADIO_DEBUG'] = 'False'

def run():
    try:
        from roop.core import decode_execution_providers, set_display_ui

        prepare_environment()

        set_display_ui(show_msg)
        if roop.globals.CFG.provider == "cuda" and util.has_cuda_device() == False:
           roop.globals.CFG.provider = "cpu"

        roop.globals.execution_providers = decode_execution_providers([roop.globals.CFG.provider])
        gputype = util.get_device()
        if gputype == 'cuda':
            util.print_cuda_info()

        print(f'Using provider {roop.globals.execution_providers} - Device:{gputype}')
    except Exception as e:
        print(f"Error during initialization: {e}")
        import traceback
        traceback.print_exc()
        return

    run_server = True
    uii.ui_restart_server = False
    mycss = """
        span {color: var(--block-info-text-color)}
        #fixedheight {
            max-height: 238.4px;
            overflow-y: auto !important;
        }
        .image-container.svelte-1l6wqyv {height: 100%}

    """

    while run_server:
        server_name = roop.globals.CFG.server_name
        if server_name is None or len(server_name) < 1:
            server_name = None
        server_port = roop.globals.CFG.server_port
        if server_port <= 0:
            server_port = None
        ssl_verify = False if server_name == '0.0.0.0' else True
        with gr.Blocks(title=f'{roop.metadata.name} {roop.metadata.version}', theme=roop.globals.CFG.selected_theme, css=mycss, delete_cache=(60, 86400)) as ui:
            with gr.Row(variant='compact'):
                    gr.Markdown(f"### [{roop.metadata.name} {roop.metadata.version}](https://github.com/C0untFloyd/roop-unleashed)")
                    gr.HTML(util.create_version_html(), elem_id="versions")
            faceswap_tab()
            livecam_tab()
            facemgr_tab()
            extras_tab()
            settings_tab()
        launch_browser = roop.globals.CFG.launch_browser

        uii.ui_restart_server = False
        try:
            # Use server_share setting from config
            # Add more conservative settings for Windows compatibility
            launch_kwargs = {
                'inbrowser': launch_browser,
                'server_name': server_name,
                'server_port': server_port,
                'share': roop.globals.CFG.server_share,
                'ssl_verify': ssl_verify,
                'prevent_thread_lock': True,
                'show_error': True,
                'quiet': True,  # Reduce console output
                'max_threads': 40  # Limit concurrent connections
            }

            # On Windows, add additional stability settings
            if os.name == 'nt':
                launch_kwargs['favicon_path'] = None
                launch_kwargs['app_kwargs'] = {'docs_url': None, 'redoc_url': None}

            ui.queue(max_size=20).launch(**launch_kwargs)
        except Exception as e:
            print(f'Exception {e} when launching Gradio Server!')
            print(f'Error type: {type(e).__name__}')
            import traceback
            traceback.print_exc()

            # Try fallback mode with minimal settings
            print("\nTrying fallback mode with minimal settings...")
            try:
                ui.queue(max_size=5).launch(
                    inbrowser=False,
                    server_name="127.0.0.1",
                    server_port=7860,
                    share=False,
                    ssl_verify=False,
                    prevent_thread_lock=True,
                    show_error=False,
                    quiet=True
                )
                print("Fallback mode successful!")
            except Exception as fallback_error:
                print(f"Fallback mode also failed: {fallback_error}")
                uii.ui_restart_server = True
                run_server = False
        try:
            while uii.ui_restart_server == False:
                time.sleep(1.0)

        except (KeyboardInterrupt, OSError):
            print("Keyboard interruption in main thread... closing server.")
            run_server = False
        ui.close()

def show_msg(msg: str):
    gr.Info(msg)

