import{T as w}from"./Tabs-BJSOUK9b.js";import{a as R}from"./Tabs-BJSOUK9b.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";const{SvelteComponent:T,add_flush_callback:k,bind:B,binding_callbacks:S,create_component:q,create_slot:A,destroy_component:C,flush:r,get_all_dirty_from_scope:D,get_slot_changes:E,init:I,mount_component:j,safe_not_equal:z,transition_in:m,transition_out:b,update_slot_base:F}=window.__gradio__svelte__internal,{createEventDispatcher:G}=window.__gradio__svelte__internal;function H(t){let e;const a=t[6].default,i=A(a,t,t[10],null);return{c(){i&&i.c()},m(n,o){i&&i.m(n,o),e=!0},p(n,o){i&&i.p&&(!e||o&1024)&&F(i,a,n,n[10],e?E(a,n[10],o,null):D(n[10]),null)},i(n){e||(m(i,n),e=!0)},o(n){b(i,n),e=!1},d(n){i&&i.d(n)}}}function J(t){let e,a,i;function n(s){t[7](s)}let o={visible:t[1],elem_id:t[2],elem_classes:t[3],initial_tabs:t[4],$$slots:{default:[H]},$$scope:{ctx:t}};return t[0]!==void 0&&(o.selected=t[0]),e=new w({props:o}),S.push(()=>B(e,"selected",n)),e.$on("change",t[8]),e.$on("select",t[9]),{c(){q(e.$$.fragment)},m(s,_){j(e,s,_),i=!0},p(s,[_]){const c={};_&2&&(c.visible=s[1]),_&4&&(c.elem_id=s[2]),_&8&&(c.elem_classes=s[3]),_&16&&(c.initial_tabs=s[4]),_&1024&&(c.$$scope={dirty:_,ctx:s}),!a&&_&1&&(a=!0,c.selected=s[0],k(()=>a=!1)),e.$set(c)},i(s){i||(m(e.$$.fragment,s),i=!0)},o(s){b(e.$$.fragment,s),i=!1},d(s){C(e,s)}}}function K(t,e,a){let{$$slots:i={},$$scope:n}=e;const o=G();let{visible:s=!0}=e,{elem_id:_=""}=e,{elem_classes:c=[]}=e,{selected:d}=e,{initial_tabs:u=[]}=e,{gradio:f}=e;function g(l){d=l,a(0,d)}const h=()=>f?.dispatch("change"),v=l=>f?.dispatch("select",l.detail);return t.$$set=l=>{"visible"in l&&a(1,s=l.visible),"elem_id"in l&&a(2,_=l.elem_id),"elem_classes"in l&&a(3,c=l.elem_classes),"selected"in l&&a(0,d=l.selected),"initial_tabs"in l&&a(4,u=l.initial_tabs),"gradio"in l&&a(5,f=l.gradio),"$$scope"in l&&a(10,n=l.$$scope)},t.$$.update=()=>{t.$$.dirty&1&&o("prop_change",{selected:d})},[d,s,_,c,u,f,i,g,h,v,n]}class O extends T{constructor(e){super(),I(this,e,K,J,z,{visible:1,elem_id:2,elem_classes:3,selected:0,initial_tabs:4,gradio:5})}get visible(){return this.$$.ctx[1]}set visible(e){this.$$set({visible:e}),r()}get elem_id(){return this.$$.ctx[2]}set elem_id(e){this.$$set({elem_id:e}),r()}get elem_classes(){return this.$$.ctx[3]}set elem_classes(e){this.$$set({elem_classes:e}),r()}get selected(){return this.$$.ctx[0]}set selected(e){this.$$set({selected:e}),r()}get initial_tabs(){return this.$$.ctx[4]}set initial_tabs(e){this.$$set({initial_tabs:e}),r()}get gradio(){return this.$$.ctx[5]}set gradio(e){this.$$set({gradio:e}),r()}}export{w as BaseTabs,R as TABS,O as default};
//# sourceMappingURL=Index-BrJl1mAw.js.map
