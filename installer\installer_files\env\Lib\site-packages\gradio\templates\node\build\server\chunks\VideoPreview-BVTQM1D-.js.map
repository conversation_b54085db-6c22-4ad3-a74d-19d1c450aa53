{"version": 3, "file": "VideoPreview-BVTQM1D-.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/VideoPreview.js"], "sourcesContent": ["import { create_ssr_component, escape, each, add_attribute, validate_component } from \"svelte/internal\";\nimport { onMount, onDestroy, createEventDispatcher, afterUpdate, tick } from \"svelte\";\nimport { h as <PERSON><PERSON><PERSON>utton, K as Undo, Z as Trim, z as Play, _ as Pause, a0 as <PERSON>ise, H as prepare_files, e as BlockLabel, V as Video$1, d as Empty, f as IconButtonWrapper, D as Download, i as ShareButton } from \"./client.js\";\nimport { f as format_time, u as uploadToHuggingFace } from \"./utils.js\";\nimport { D as DownloadLink } from \"./DownloadLink.js\";\nimport { V as Video } from \"./Video.js\";\nimport { M as ModifyUpload } from \"./ModifyUpload.js\";\nconst ERROR_RESPONSE_BODY_READER = new Error(\"failed to get response body reader\");\nconst ERROR_INCOMPLETED_DOWNLOAD = new Error(\"failed to complete download\");\nconst HeaderContentLength = \"Content-Length\";\nconst downloadWithProgress = async (url, cb) => {\n  const resp = await fetch(url);\n  let buf;\n  try {\n    const total = parseInt(resp.headers.get(HeaderContentLength) || \"-1\");\n    const reader = resp.body?.getReader();\n    if (!reader)\n      throw ERROR_RESPONSE_BODY_READER;\n    const chunks = [];\n    let received = 0;\n    for (; ; ) {\n      const { done, value } = await reader.read();\n      const delta = value ? value.length : 0;\n      if (done) {\n        if (total != -1 && total !== received)\n          throw ERROR_INCOMPLETED_DOWNLOAD;\n        cb && cb({ url, total, received, delta, done });\n        break;\n      }\n      chunks.push(value);\n      received += delta;\n      cb && cb({ url, total, received, delta, done });\n    }\n    const data = new Uint8Array(received);\n    let position = 0;\n    for (const chunk of chunks) {\n      data.set(chunk, position);\n      position += chunk.length;\n    }\n    buf = data.buffer;\n  } catch (e) {\n    console.log(`failed to send download progress event: `, e);\n    buf = await resp.arrayBuffer();\n  }\n  return buf;\n};\nconst toBlobURL = async (url, mimeType, progress = false, cb) => {\n  const buf = progress ? await downloadWithProgress(url, cb) : await (await fetch(url)).arrayBuffer();\n  const blob = new Blob([buf], { type: mimeType });\n  return URL.createObjectURL(blob);\n};\nclass FFmpeg {\n  constructor() {\n    throw new Error(\"ffmpeg.wasm does not support nodejs\");\n  }\n}\nconst prettyBytes = (bytes) => {\n  let units = [\"B\", \"KB\", \"MB\", \"GB\", \"PB\"];\n  let i = 0;\n  while (bytes > 1024) {\n    bytes /= 1024;\n    i++;\n  }\n  let unit = units[i];\n  return bytes.toFixed(1) + \" \" + unit;\n};\nconst playable = () => {\n  return true;\n};\nfunction loaded(node, { autoplay }) {\n  async function handle_playback() {\n    if (!autoplay)\n      return;\n    await node.play();\n  }\n  node.addEventListener(\"loadeddata\", handle_playback);\n  return {\n    destroy() {\n      node.removeEventListener(\"loadeddata\", handle_playback);\n    }\n  };\n}\nasync function loadFfmpeg() {\n  const ffmpeg = new FFmpeg();\n  const baseURL = \"https://unpkg.com/@ffmpeg/core@0.12.4/dist/esm\";\n  await ffmpeg.load({\n    coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, \"text/javascript\"),\n    wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, \"application/wasm\")\n  });\n  return ffmpeg;\n}\nconst css$2 = {\n  code: \".load-wrap.svelte-10c4beq{display:flex;justify-content:center;align-items:center;height:100%}.loader.svelte-10c4beq{display:flex;position:relative;background-color:var(--border-color-accent-subdued);animation:svelte-10c4beq-shadowPulse 2s linear infinite;box-shadow:-24px 0 var(--border-color-accent-subdued),\\n\t\t\t24px 0 var(--border-color-accent-subdued);margin:var(--spacing-md);border-radius:50%;width:10px;height:10px;scale:0.5}@keyframes svelte-10c4beq-shadowPulse{33%{box-shadow:-24px 0 var(--border-color-accent-subdued),\\n\t\t\t\t24px 0 #fff;background:#fff}66%{box-shadow:-24px 0 #fff,\\n\t\t\t\t24px 0 #fff;background:var(--border-color-accent-subdued)}100%{box-shadow:-24px 0 #fff,\\n\t\t\t\t24px 0 var(--border-color-accent-subdued);background:#fff}}.container.svelte-10c4beq{display:flex;flex-direction:column;align-items:center;justify-content:center;margin:var(--spacing-lg) var(--spacing-lg) 0 var(--spacing-lg)}#timeline.svelte-10c4beq{display:flex;height:var(--size-10);flex:1;position:relative}img.svelte-10c4beq{flex:1 1 auto;min-width:0;object-fit:cover;height:var(--size-12);border:1px solid var(--block-border-color);user-select:none;z-index:1}.handle.svelte-10c4beq{width:3px;background-color:var(--color-accent);cursor:ew-resize;height:var(--size-12);z-index:3;position:absolute}.opaque-layer.svelte-10c4beq{background-color:rgba(230, 103, 40, 0.25);border:1px solid var(--color-accent);height:var(--size-12);position:absolute;z-index:2}\",\n  map: '{\"version\":3,\"file\":\"VideoTimeline.svelte\",\"sources\":[\"VideoTimeline.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount, onDestroy } from \\\\\"svelte\\\\\";\\\\nexport let videoElement;\\\\nexport let trimmedDuration;\\\\nexport let dragStart;\\\\nexport let dragEnd;\\\\nexport let loadingTimeline;\\\\nlet thumbnails = [];\\\\nlet numberOfThumbnails = 10;\\\\nlet intervalId;\\\\nlet videoDuration;\\\\nlet leftHandlePosition = 0;\\\\nlet rightHandlePosition = 100;\\\\nlet dragging = null;\\\\nconst startDragging = (side) => {\\\\n    dragging = side;\\\\n};\\\\n$: loadingTimeline = thumbnails.length !== numberOfThumbnails;\\\\nconst stopDragging = () => {\\\\n    dragging = null;\\\\n};\\\\nconst drag = (event, distance) => {\\\\n    if (dragging) {\\\\n        const timeline = document.getElementById(\\\\\"timeline\\\\\");\\\\n        if (!timeline)\\\\n            return;\\\\n        const rect = timeline.getBoundingClientRect();\\\\n        let newPercentage = (event.clientX - rect.left) / rect.width * 100;\\\\n        if (distance) {\\\\n            newPercentage = dragging === \\\\\"left\\\\\" ? leftHandlePosition + distance : rightHandlePosition + distance;\\\\n        }\\\\n        else {\\\\n            newPercentage = (event.clientX - rect.left) / rect.width * 100;\\\\n        }\\\\n        newPercentage = Math.max(0, Math.min(newPercentage, 100));\\\\n        if (dragging === \\\\\"left\\\\\") {\\\\n            leftHandlePosition = Math.min(newPercentage, rightHandlePosition);\\\\n            const newTimeLeft = leftHandlePosition / 100 * videoDuration;\\\\n            videoElement.currentTime = newTimeLeft;\\\\n            dragStart = newTimeLeft;\\\\n        }\\\\n        else if (dragging === \\\\\"right\\\\\") {\\\\n            rightHandlePosition = Math.max(newPercentage, leftHandlePosition);\\\\n            const newTimeRight = rightHandlePosition / 100 * videoDuration;\\\\n            videoElement.currentTime = newTimeRight;\\\\n            dragEnd = newTimeRight;\\\\n        }\\\\n        const startTime = leftHandlePosition / 100 * videoDuration;\\\\n        const endTime = rightHandlePosition / 100 * videoDuration;\\\\n        trimmedDuration = endTime - startTime;\\\\n        leftHandlePosition = leftHandlePosition;\\\\n        rightHandlePosition = rightHandlePosition;\\\\n    }\\\\n};\\\\nconst moveHandle = (e) => {\\\\n    if (dragging) {\\\\n        const distance = 1 / videoDuration * 100;\\\\n        if (e.key === \\\\\"ArrowLeft\\\\\") {\\\\n            drag({ clientX: 0 }, -distance);\\\\n        }\\\\n        else if (e.key === \\\\\"ArrowRight\\\\\") {\\\\n            drag({ clientX: 0 }, distance);\\\\n        }\\\\n    }\\\\n};\\\\nconst generateThumbnail = () => {\\\\n    const canvas = document.createElement(\\\\\"canvas\\\\\");\\\\n    const ctx = canvas.getContext(\\\\\"2d\\\\\");\\\\n    if (!ctx)\\\\n        return;\\\\n    canvas.width = videoElement.videoWidth;\\\\n    canvas.height = videoElement.videoHeight;\\\\n    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\\\\n    const thumbnail = canvas.toDataURL(\\\\\"image/jpeg\\\\\", 0.7);\\\\n    thumbnails = [...thumbnails, thumbnail];\\\\n};\\\\nonMount(() => {\\\\n    const loadMetadata = () => {\\\\n        videoDuration = videoElement.duration;\\\\n        const interval = videoDuration / numberOfThumbnails;\\\\n        let captures = 0;\\\\n        const onSeeked = () => {\\\\n            generateThumbnail();\\\\n            captures++;\\\\n            if (captures < numberOfThumbnails) {\\\\n                videoElement.currentTime += interval;\\\\n            }\\\\n            else {\\\\n                videoElement.removeEventListener(\\\\\"seeked\\\\\", onSeeked);\\\\n            }\\\\n        };\\\\n        videoElement.addEventListener(\\\\\"seeked\\\\\", onSeeked);\\\\n        videoElement.currentTime = 0;\\\\n    };\\\\n    if (videoElement.readyState >= 1) {\\\\n        loadMetadata();\\\\n    }\\\\n    else {\\\\n        videoElement.addEventListener(\\\\\"loadedmetadata\\\\\", loadMetadata);\\\\n    }\\\\n});\\\\nonDestroy(() => {\\\\n    window.removeEventListener(\\\\\"mousemove\\\\\", drag);\\\\n    window.removeEventListener(\\\\\"mouseup\\\\\", stopDragging);\\\\n    window.removeEventListener(\\\\\"keydown\\\\\", moveHandle);\\\\n    if (intervalId !== void 0) {\\\\n        clearInterval(intervalId);\\\\n    }\\\\n});\\\\nonMount(() => {\\\\n    window.addEventListener(\\\\\"mousemove\\\\\", drag);\\\\n    window.addEventListener(\\\\\"mouseup\\\\\", stopDragging);\\\\n    window.addEventListener(\\\\\"keydown\\\\\", moveHandle);\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"container\\\\\">\\\\n\\\\t{#if loadingTimeline}\\\\n\\\\t\\\\t<div class=\\\\\"load-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t<span aria-label=\\\\\"loading timeline\\\\\" class=\\\\\"loader\\\\\" />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<div id=\\\\\"timeline\\\\\" class=\\\\\"thumbnail-wrapper\\\\\">\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"start drag handle for trimming video\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"handle left\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:mousedown={() => startDragging(\\\\\"left\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:blur={stopDragging}\\\\n\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"ArrowLeft\\\\\" || e.key == \\\\\"ArrowRight\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstartDragging(\\\\\"left\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"left: {leftHandlePosition}%;\\\\\"\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"opaque-layer\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"left: {leftHandlePosition}%; right: {100 - rightHandlePosition}%\\\\\"\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t{#each thumbnails as thumbnail, i (i)}\\\\n\\\\t\\\\t\\\\t\\\\t<img src={thumbnail} alt={`frame-${i}`} draggable=\\\\\"false\\\\\" />\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"end drag handle for trimming video\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"handle right\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:mousedown={() => startDragging(\\\\\"right\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:blur={stopDragging}\\\\n\\\\t\\\\t\\\\t\\\\ton:keydown={(e) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tif (e.key === \\\\\"ArrowLeft\\\\\" || e.key == \\\\\"ArrowRight\\\\\") {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstartDragging(\\\\\"right\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"left: {rightHandlePosition}%;\\\\\"\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.load-wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}\\\\n\\\\t.loader {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbackground-color: var(--border-color-accent-subdued);\\\\n\\\\t\\\\tanimation: shadowPulse 2s linear infinite;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 10px;\\\\n\\\\t\\\\theight: 10px;\\\\n\\\\t\\\\tscale: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes shadowPulse {\\\\n\\\\t\\\\t33% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 var(--border-color-accent-subdued),\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t66% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 #fff;\\\\n\\\\t\\\\t\\\\tbackground: var(--border-color-accent-subdued);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t\\\\t-24px 0 #fff,\\\\n\\\\t\\\\t\\\\t\\\\t24px 0 var(--border-color-accent-subdued);\\\\n\\\\t\\\\t\\\\tbackground: #fff;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmargin: var(--spacing-lg) var(--spacing-lg) 0 var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t#timeline {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: var(--size-10);\\\\n\\\\t\\\\tflex: 1;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\timg {\\\\n\\\\t\\\\tflex: 1 1 auto;\\\\n\\\\t\\\\tmin-width: 0;\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\theight: var(--size-12);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tuser-select: none;\\\\n\\\\t\\\\tz-index: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.handle {\\\\n\\\\t\\\\twidth: 3px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tcursor: ew-resize;\\\\n\\\\t\\\\theight: var(--size-12);\\\\n\\\\t\\\\tz-index: 3;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t}\\\\n\\\\n\\\\t.opaque-layer {\\\\n\\\\t\\\\tbackground-color: rgba(230, 103, 40, 0.25);\\\\n\\\\t\\\\tborder: 1px solid var(--color-accent);\\\\n\\\\t\\\\theight: var(--size-12);\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tz-index: 2;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgKC,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IACT,CACA,sBAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,6BAA6B,CAAC,CACpD,SAAS,CAAE,0BAAW,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CACzC,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC9C,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,GACR,CAEA,WAAW,0BAAY,CACtB,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IACb,CACA,GAAI,CACH,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CACZ,UAAU,CAAE,IAAI,6BAA6B,CAC9C,CACA,IAAK,CACJ,UAAU,CACT,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,6BAA6B,CAAC,CAC1C,UAAU,CAAE,IACb,CACD,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAC/D,CAEA,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,IAAI,CAAE,CAAC,CACP,QAAQ,CAAE,QACX,CAEA,kBAAI,CACH,IAAI,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CACd,SAAS,CAAE,CAAC,CACZ,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,CACV,CAEA,sBAAQ,CACP,KAAK,CAAE,GAAG,CACV,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,MAAM,CAAE,SAAS,CACjB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,OAAO,CAAE,CAAC,CACV,QAAQ,CAAE,QACX,CAEA,4BAAc,CACb,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAC1C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACrC,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CACV\"}'\n};\nlet numberOfThumbnails = 10;\nconst VideoTimeline = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { videoElement } = $$props;\n  let { trimmedDuration } = $$props;\n  let { dragStart } = $$props;\n  let { dragEnd } = $$props;\n  let { loadingTimeline } = $$props;\n  let thumbnails = [];\n  let videoDuration;\n  let leftHandlePosition = 0;\n  let rightHandlePosition = 100;\n  let dragging = null;\n  const stopDragging = () => {\n    dragging = null;\n  };\n  const drag = (event, distance) => {\n    if (dragging) {\n      const timeline = document.getElementById(\"timeline\");\n      if (!timeline)\n        return;\n      const rect = timeline.getBoundingClientRect();\n      let newPercentage = (event.clientX - rect.left) / rect.width * 100;\n      if (distance) {\n        newPercentage = dragging === \"left\" ? leftHandlePosition + distance : rightHandlePosition + distance;\n      } else {\n        newPercentage = (event.clientX - rect.left) / rect.width * 100;\n      }\n      newPercentage = Math.max(0, Math.min(newPercentage, 100));\n      if (dragging === \"left\") {\n        leftHandlePosition = Math.min(newPercentage, rightHandlePosition);\n        const newTimeLeft = leftHandlePosition / 100 * videoDuration;\n        videoElement.currentTime = newTimeLeft;\n        dragStart = newTimeLeft;\n      } else if (dragging === \"right\") {\n        rightHandlePosition = Math.max(newPercentage, leftHandlePosition);\n        const newTimeRight = rightHandlePosition / 100 * videoDuration;\n        videoElement.currentTime = newTimeRight;\n        dragEnd = newTimeRight;\n      }\n      const startTime = leftHandlePosition / 100 * videoDuration;\n      const endTime = rightHandlePosition / 100 * videoDuration;\n      trimmedDuration = endTime - startTime;\n      leftHandlePosition = leftHandlePosition;\n      rightHandlePosition = rightHandlePosition;\n    }\n  };\n  const moveHandle = (e) => {\n    if (dragging) {\n      const distance = 1 / videoDuration * 100;\n      if (e.key === \"ArrowLeft\") {\n        drag({ clientX: 0 }, -distance);\n      } else if (e.key === \"ArrowRight\") {\n        drag({ clientX: 0 }, distance);\n      }\n    }\n  };\n  const generateThumbnail = () => {\n    const canvas = document.createElement(\"canvas\");\n    const ctx = canvas.getContext(\"2d\");\n    if (!ctx)\n      return;\n    canvas.width = videoElement.videoWidth;\n    canvas.height = videoElement.videoHeight;\n    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);\n    const thumbnail = canvas.toDataURL(\"image/jpeg\", 0.7);\n    thumbnails = [...thumbnails, thumbnail];\n  };\n  onMount(() => {\n    const loadMetadata = () => {\n      videoDuration = videoElement.duration;\n      const interval = videoDuration / numberOfThumbnails;\n      let captures = 0;\n      const onSeeked = () => {\n        generateThumbnail();\n        captures++;\n        if (captures < numberOfThumbnails) {\n          videoElement.currentTime += interval;\n        } else {\n          videoElement.removeEventListener(\"seeked\", onSeeked);\n        }\n      };\n      videoElement.addEventListener(\"seeked\", onSeeked);\n      videoElement.currentTime = 0;\n    };\n    if (videoElement.readyState >= 1) {\n      loadMetadata();\n    } else {\n      videoElement.addEventListener(\"loadedmetadata\", loadMetadata);\n    }\n  });\n  onDestroy(() => {\n    window.removeEventListener(\"mousemove\", drag);\n    window.removeEventListener(\"mouseup\", stopDragging);\n    window.removeEventListener(\"keydown\", moveHandle);\n  });\n  onMount(() => {\n    window.addEventListener(\"mousemove\", drag);\n    window.addEventListener(\"mouseup\", stopDragging);\n    window.addEventListener(\"keydown\", moveHandle);\n  });\n  if ($$props.videoElement === void 0 && $$bindings.videoElement && videoElement !== void 0)\n    $$bindings.videoElement(videoElement);\n  if ($$props.trimmedDuration === void 0 && $$bindings.trimmedDuration && trimmedDuration !== void 0)\n    $$bindings.trimmedDuration(trimmedDuration);\n  if ($$props.dragStart === void 0 && $$bindings.dragStart && dragStart !== void 0)\n    $$bindings.dragStart(dragStart);\n  if ($$props.dragEnd === void 0 && $$bindings.dragEnd && dragEnd !== void 0)\n    $$bindings.dragEnd(dragEnd);\n  if ($$props.loadingTimeline === void 0 && $$bindings.loadingTimeline && loadingTimeline !== void 0)\n    $$bindings.loadingTimeline(loadingTimeline);\n  $$result.css.add(css$2);\n  loadingTimeline = thumbnails.length !== numberOfThumbnails;\n  return `<div class=\"container svelte-10c4beq\">${loadingTimeline ? `<div class=\"load-wrap svelte-10c4beq\" data-svelte-h=\"svelte-13yzice\"><span aria-label=\"loading timeline\" class=\"loader svelte-10c4beq\"></span></div>` : `<div id=\"timeline\" class=\"thumbnail-wrapper svelte-10c4beq\"><button aria-label=\"start drag handle for trimming video\" class=\"handle left svelte-10c4beq\" style=\"${\"left: \" + escape(leftHandlePosition, true) + \"%;\"}\"></button> <div class=\"opaque-layer svelte-10c4beq\" style=\"${\"left: \" + escape(leftHandlePosition, true) + \"%; right: \" + escape(100 - rightHandlePosition, true) + \"%\"}\"></div> ${each(thumbnails, (thumbnail, i) => {\n    return `<img${add_attribute(\"src\", thumbnail, 0)}${add_attribute(\"alt\", `frame-${i}`, 0)} draggable=\"false\" class=\"svelte-10c4beq\">`;\n  })} <button aria-label=\"end drag handle for trimming video\" class=\"handle right svelte-10c4beq\" style=\"${\"left: \" + escape(rightHandlePosition, true) + \"%;\"}\"></button></div>`} </div>`;\n});\nconst css$1 = {\n  code: \".container.svelte-7yrr5f.svelte-7yrr5f{width:100%}time.svelte-7yrr5f.svelte-7yrr5f{color:var(--color-accent);font-weight:bold;padding-left:var(--spacing-xs)}.timeline-wrapper.svelte-7yrr5f.svelte-7yrr5f{display:flex;align-items:center;justify-content:center;width:100%}.text-button.svelte-7yrr5f.svelte-7yrr5f{border:1px solid var(--neutral-400);border-radius:var(--radius-sm);font-weight:300;font-size:var(--size-3);text-align:center;color:var(--neutral-400);height:var(--size-5);font-weight:bold;padding:0 5px;margin-left:5px}.text-button.svelte-7yrr5f.svelte-7yrr5f:hover,.text-button.svelte-7yrr5f.svelte-7yrr5f:focus{color:var(--color-accent);border-color:var(--color-accent)}.controls.svelte-7yrr5f.svelte-7yrr5f{display:flex;justify-content:space-between;align-items:center;margin:var(--spacing-lg);overflow:hidden}.edit-buttons.svelte-7yrr5f.svelte-7yrr5f{display:flex;gap:var(--spacing-sm)}@media(max-width: 320px){.controls.svelte-7yrr5f.svelte-7yrr5f{flex-direction:column;align-items:flex-start}.edit-buttons.svelte-7yrr5f.svelte-7yrr5f{margin-top:var(--spacing-sm)}.controls.svelte-7yrr5f .svelte-7yrr5f{margin:var(--spacing-sm)}.controls.svelte-7yrr5f .text-button.svelte-7yrr5f{margin-left:0}}.container.svelte-7yrr5f.svelte-7yrr5f{display:flex;flex-direction:column}.hidden.svelte-7yrr5f.svelte-7yrr5f{display:none}\",\n  map: '{\"version\":3,\"file\":\"VideoControls.svelte\",\"sources\":[\"VideoControls.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Undo, Trim, Clear } from \\\\\"@gradio/icons\\\\\";\\\\nimport VideoTimeline from \\\\\"./VideoTimeline.svelte\\\\\";\\\\nimport { trimVideo } from \\\\\"./utils\\\\\";\\\\nimport { FFmpeg } from \\\\\"@ffmpeg/ffmpeg\\\\\";\\\\nimport loadFfmpeg from \\\\\"./utils\\\\\";\\\\nimport { onMount } from \\\\\"svelte\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nimport { IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nexport let videoElement;\\\\nexport let showRedo = false;\\\\nexport let interactive = true;\\\\nexport let mode = \\\\\"\\\\\";\\\\nexport let handle_reset_value;\\\\nexport let handle_trim_video;\\\\nexport let processingVideo = false;\\\\nexport let i18n;\\\\nexport let value = null;\\\\nexport let show_download_button = false;\\\\nexport let handle_clear = () => {\\\\n};\\\\nexport let has_change_history = false;\\\\nlet ffmpeg;\\\\nonMount(async () => {\\\\n    ffmpeg = await loadFfmpeg();\\\\n});\\\\n$: if (mode === \\\\\"edit\\\\\" && trimmedDuration === null && videoElement)\\\\n    trimmedDuration = videoElement.duration;\\\\nlet trimmedDuration = null;\\\\nlet dragStart = 0;\\\\nlet dragEnd = 0;\\\\nlet loadingTimeline = false;\\\\nconst toggleTrimmingMode = () => {\\\\n    if (mode === \\\\\"edit\\\\\") {\\\\n        mode = \\\\\"\\\\\";\\\\n        trimmedDuration = videoElement.duration;\\\\n    }\\\\n    else {\\\\n        mode = \\\\\"edit\\\\\";\\\\n    }\\\\n};\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"container\\\\\" class:hidden={mode !== \\\\\"edit\\\\\"}>\\\\n\\\\t{#if mode === \\\\\"edit\\\\\"}\\\\n\\\\t\\\\t<div class=\\\\\"timeline-wrapper\\\\\">\\\\n\\\\t\\\\t\\\\t<VideoTimeline\\\\n\\\\t\\\\t\\\\t\\\\t{videoElement}\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragStart\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragEnd\\\\n\\\\t\\\\t\\\\t\\\\tbind:trimmedDuration\\\\n\\\\t\\\\t\\\\t\\\\tbind:loadingTimeline\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t<div class=\\\\\"controls\\\\\" data-testid=\\\\\"waveform-controls\\\\\">\\\\n\\\\t\\\\t{#if mode === \\\\\"edit\\\\\" && trimmedDuration !== null}\\\\n\\\\t\\\\t\\\\t<time\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"duration of selected region in seconds\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:hidden={loadingTimeline}>{format_time(trimmedDuration)}</time\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"edit-buttons\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:hidden={loadingTimeline}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"text-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmode = \\\\\"\\\\\";\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tprocessingVideo = true;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttrimVideo(ffmpeg, dragStart, dragEnd, videoElement)\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.then((videoBlob) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\thandle_trim_video(videoBlob);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t})\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t.then(() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tprocessingVideo = false;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t});\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t}}>Trim</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"text-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:hidden={loadingTimeline}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={toggleTrimmingMode}>Cancel</button\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<div />\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<ModifyUpload\\\\n\\\\t{i18n}\\\\n\\\\ton:clear={() => handle_clear()}\\\\n\\\\tdownload={show_download_button ? value?.url : null}\\\\n>\\\\n\\\\t{#if showRedo && mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={Undo}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Reset video to initial value\\\\\"\\\\n\\\\t\\\\t\\\\tdisabled={processingVideo || !has_change_history}\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\thandle_reset_value();\\\\n\\\\t\\\\t\\\\t\\\\tmode = \\\\\"\\\\\";\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t{#if interactive && mode === \\\\\"\\\\\"}\\\\n\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\tIcon={Trim}\\\\n\\\\t\\\\t\\\\tlabel=\\\\\"Trim video to selection\\\\\"\\\\n\\\\t\\\\t\\\\tdisabled={processingVideo}\\\\n\\\\t\\\\t\\\\ton:click={toggleTrimmingMode}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</ModifyUpload>\\\\n\\\\n<style>\\\\n\\\\t.container {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\ttime {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t\\\\tpadding-left: var(--spacing-xs);\\\\n\\\\t}\\\\n\\\\n\\\\t.timeline-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.text-button {\\\\n\\\\t\\\\tborder: 1px solid var(--neutral-400);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tfont-weight: 300;\\\\n\\\\t\\\\tfont-size: var(--size-3);\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\tfont-weight: bold;\\\\n\\\\t\\\\tpadding: 0 5px;\\\\n\\\\t\\\\tmargin-left: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.text-button:hover,\\\\n\\\\t.text-button:focus {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin: var(--spacing-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.edit-buttons {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tgap: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 320px) {\\\\n\\\\t\\\\t.controls {\\\\n\\\\t\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\t\\\\talign-items: flex-start;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.edit-buttons {\\\\n\\\\t\\\\t\\\\tmargin-top: var(--spacing-sm);\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.controls * {\\\\n\\\\t\\\\t\\\\tmargin: var(--spacing-sm);\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.controls .text-button {\\\\n\\\\t\\\\t\\\\tmargin-left: 0;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAsHC,sCAAW,CACV,KAAK,CAAE,IACR,CACA,gCAAK,CACJ,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,WAAW,CAAE,IAAI,CACjB,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,6CAAkB,CACjB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IACR,CAEA,wCAAa,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,QAAQ,CAAC,CACxB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,aAAa,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,WAAW,CAAE,IAAI,CACjB,OAAO,CAAE,CAAC,CAAC,GAAG,CACd,WAAW,CAAE,GACd,CAEA,wCAAY,MAAM,CAClB,wCAAY,MAAO,CAClB,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,cAAc,CACjC,CAEA,qCAAU,CACT,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,QAAQ,CAAE,MACX,CAEA,yCAAc,CACb,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,IAAI,YAAY,CACtB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qCAAU,CACT,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,UACd,CAEA,yCAAc,CACb,UAAU,CAAE,IAAI,YAAY,CAC7B,CAEA,uBAAS,CAAC,cAAE,CACX,MAAM,CAAE,IAAI,YAAY,CACzB,CAEA,uBAAS,CAAC,0BAAa,CACtB,WAAW,CAAE,CACd,CACD,CAEA,sCAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,mCAAQ,CACP,OAAO,CAAE,IACV\"}'\n};\nconst VideoControls = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { videoElement } = $$props;\n  let { showRedo = false } = $$props;\n  let { interactive = true } = $$props;\n  let { mode = \"\" } = $$props;\n  let { handle_reset_value } = $$props;\n  let { handle_trim_video } = $$props;\n  let { processingVideo = false } = $$props;\n  let { i18n } = $$props;\n  let { value = null } = $$props;\n  let { show_download_button = false } = $$props;\n  let { handle_clear = () => {\n  } } = $$props;\n  let { has_change_history = false } = $$props;\n  onMount(async () => {\n    await loadFfmpeg();\n  });\n  let trimmedDuration = null;\n  let dragStart = 0;\n  let dragEnd = 0;\n  let loadingTimeline = false;\n  if ($$props.videoElement === void 0 && $$bindings.videoElement && videoElement !== void 0)\n    $$bindings.videoElement(videoElement);\n  if ($$props.showRedo === void 0 && $$bindings.showRedo && showRedo !== void 0)\n    $$bindings.showRedo(showRedo);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.handle_trim_video === void 0 && $$bindings.handle_trim_video && handle_trim_video !== void 0)\n    $$bindings.handle_trim_video(handle_trim_video);\n  if ($$props.processingVideo === void 0 && $$bindings.processingVideo && processingVideo !== void 0)\n    $$bindings.processingVideo(processingVideo);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.handle_clear === void 0 && $$bindings.handle_clear && handle_clear !== void 0)\n    $$bindings.handle_clear(handle_clear);\n  if ($$props.has_change_history === void 0 && $$bindings.has_change_history && has_change_history !== void 0)\n    $$bindings.has_change_history(has_change_history);\n  $$result.css.add(css$1);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (mode === \"edit\" && trimmedDuration === null && videoElement)\n        trimmedDuration = videoElement.duration;\n    }\n    $$rendered = `<div class=\"${[\"container svelte-7yrr5f\", mode !== \"edit\" ? \"hidden\" : \"\"].join(\" \").trim()}\">${mode === \"edit\" ? `<div class=\"timeline-wrapper svelte-7yrr5f\">${validate_component(VideoTimeline, \"VideoTimeline\").$$render(\n      $$result,\n      {\n        videoElement,\n        dragStart,\n        dragEnd,\n        trimmedDuration,\n        loadingTimeline\n      },\n      {\n        dragStart: ($$value) => {\n          dragStart = $$value;\n          $$settled = false;\n        },\n        dragEnd: ($$value) => {\n          dragEnd = $$value;\n          $$settled = false;\n        },\n        trimmedDuration: ($$value) => {\n          trimmedDuration = $$value;\n          $$settled = false;\n        },\n        loadingTimeline: ($$value) => {\n          loadingTimeline = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}</div>` : ``} <div class=\"controls svelte-7yrr5f\" data-testid=\"waveform-controls\">${mode === \"edit\" && trimmedDuration !== null ? `<time aria-label=\"duration of selected region in seconds\" class=\"${[\"svelte-7yrr5f\", loadingTimeline ? \"hidden\" : \"\"].join(\" \").trim()}\">${escape(format_time(trimmedDuration))}</time> <div class=\"edit-buttons svelte-7yrr5f\"><button class=\"${[\"text-button svelte-7yrr5f\", loadingTimeline ? \"hidden\" : \"\"].join(\" \").trim()}\" data-svelte-h=\"svelte-18nzick\">Trim</button> <button class=\"${[\"text-button svelte-7yrr5f\", loadingTimeline ? \"hidden\" : \"\"].join(\" \").trim()}\" data-svelte-h=\"svelte-1mj98i4\">Cancel</button></div>` : `<div class=\"svelte-7yrr5f\"></div>`}</div></div> ${validate_component(ModifyUpload, \"ModifyUpload\").$$render(\n      $$result,\n      {\n        i18n,\n        download: show_download_button ? value?.url : null\n      },\n      {},\n      {\n        default: () => {\n          return `${showRedo && mode === \"\" ? `${validate_component(IconButton, \"IconButton\").$$render(\n            $$result,\n            {\n              Icon: Undo,\n              label: \"Reset video to initial value\",\n              disabled: processingVideo || !has_change_history\n            },\n            {},\n            {}\n          )}` : ``} ${interactive && mode === \"\" ? `${validate_component(IconButton, \"IconButton\").$$render(\n            $$result,\n            {\n              Icon: Trim,\n              label: \"Trim video to selection\",\n              disabled: processingVideo\n            },\n            {},\n            {}\n          )}` : ``}`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst css = {\n  code: \"span.svelte-euo1cw.svelte-euo1cw{text-shadow:0 0 8px rgba(0, 0, 0, 0.5)}progress.svelte-euo1cw.svelte-euo1cw{margin-right:var(--size-3);border-radius:var(--radius-sm);width:var(--size-full);height:var(--size-2)}progress.svelte-euo1cw.svelte-euo1cw::-webkit-progress-bar{border-radius:2px;background-color:rgba(255, 255, 255, 0.2);overflow:hidden}progress.svelte-euo1cw.svelte-euo1cw::-webkit-progress-value{background-color:rgba(255, 255, 255, 0.9)}.mirror.svelte-euo1cw.svelte-euo1cw{transform:scaleX(-1)}.mirror-wrap.svelte-euo1cw.svelte-euo1cw{position:relative;height:100%;width:100%}.controls.svelte-euo1cw.svelte-euo1cw{position:absolute;bottom:0;opacity:0;transition:500ms;margin:var(--size-2);border-radius:var(--radius-md);background:var(--color-grey-800);padding:var(--size-2) var(--size-1);width:calc(100% - 0.375rem * 2);width:calc(100% - var(--size-2) * 2)}.wrap.svelte-euo1cw:hover .controls.svelte-euo1cw{opacity:1}.inner.svelte-euo1cw.svelte-euo1cw{display:flex;justify-content:space-between;align-items:center;padding-right:var(--size-2);padding-left:var(--size-2);width:var(--size-full);height:var(--size-full)}.icon.svelte-euo1cw.svelte-euo1cw{display:flex;justify-content:center;cursor:pointer;width:var(--size-6);color:white}.time.svelte-euo1cw.svelte-euo1cw{flex-shrink:0;margin-right:var(--size-3);margin-left:var(--size-3);color:white;font-size:var(--text-sm);font-family:var(--font-mono)}.wrap.svelte-euo1cw.svelte-euo1cw{position:relative;background-color:var(--background-fill-secondary);height:var(--size-full);width:var(--size-full);border-radius:var(--radius-xl)}.wrap.svelte-euo1cw video{height:var(--size-full);width:var(--size-full)}\",\n  map: '{\"version\":3,\"file\":\"Player.svelte\",\"sources\":[\"Player.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport { Play, Pause, Maximise, Undo } from \\\\\"@gradio/icons\\\\\";\\\\nimport Video from \\\\\"./Video.svelte\\\\\";\\\\nimport VideoControls from \\\\\"./VideoControls.svelte\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nexport let root = \\\\\"\\\\\";\\\\nexport let src;\\\\nexport let subtitle = null;\\\\nexport let mirror;\\\\nexport let autoplay;\\\\nexport let loop;\\\\nexport let label = \\\\\"test\\\\\";\\\\nexport let interactive = false;\\\\nexport let handle_change = () => {\\\\n};\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nexport let upload;\\\\nexport let is_stream;\\\\nexport let i18n;\\\\nexport let show_download_button = false;\\\\nexport let value = null;\\\\nexport let handle_clear = () => {\\\\n};\\\\nexport let has_change_history = false;\\\\nconst dispatch = createEventDispatcher();\\\\nlet time = 0;\\\\nlet duration;\\\\nlet paused = true;\\\\nlet video;\\\\nlet processingVideo = false;\\\\nfunction handleMove(e) {\\\\n    if (!duration)\\\\n        return;\\\\n    if (e.type === \\\\\"click\\\\\") {\\\\n        handle_click(e);\\\\n        return;\\\\n    }\\\\n    if (e.type !== \\\\\"touchmove\\\\\" && !(e.buttons & 1))\\\\n        return;\\\\n    const clientX = e.type === \\\\\"touchmove\\\\\" ? e.touches[0].clientX : e.clientX;\\\\n    const { left, right } = e.currentTarget.getBoundingClientRect();\\\\n    time = duration * (clientX - left) / (right - left);\\\\n}\\\\nasync function play_pause() {\\\\n    if (document.fullscreenElement != video) {\\\\n        const isPlaying = video.currentTime > 0 && !video.paused && !video.ended && video.readyState > video.HAVE_CURRENT_DATA;\\\\n        if (!isPlaying) {\\\\n            await video.play();\\\\n        }\\\\n        else\\\\n            video.pause();\\\\n    }\\\\n}\\\\nfunction handle_click(e) {\\\\n    const { left, right } = e.currentTarget.getBoundingClientRect();\\\\n    time = duration * (e.clientX - left) / (right - left);\\\\n}\\\\nfunction handle_end() {\\\\n    dispatch(\\\\\"stop\\\\\");\\\\n    dispatch(\\\\\"end\\\\\");\\\\n}\\\\nconst handle_trim_video = async (videoBlob) => {\\\\n    let _video_blob = new File([videoBlob], \\\\\"video.mp4\\\\\");\\\\n    const val = await prepare_files([_video_blob]);\\\\n    let value2 = ((await upload(val, root))?.filter(Boolean))[0];\\\\n    handle_change(value2);\\\\n};\\\\nfunction open_full_screen() {\\\\n    video.requestFullscreen();\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"wrap\\\\\">\\\\n\\\\t<div class=\\\\\"mirror-wrap\\\\\" class:mirror>\\\\n\\\\t\\\\t<Video\\\\n\\\\t\\\\t\\\\t{src}\\\\n\\\\t\\\\t\\\\tpreload=\\\\\"auto\\\\\"\\\\n\\\\t\\\\t\\\\t{autoplay}\\\\n\\\\t\\\\t\\\\t{loop}\\\\n\\\\t\\\\t\\\\t{is_stream}\\\\n\\\\t\\\\t\\\\ton:click={play_pause}\\\\n\\\\t\\\\t\\\\ton:play\\\\n\\\\t\\\\t\\\\ton:pause\\\\n\\\\t\\\\t\\\\ton:ended={handle_end}\\\\n\\\\t\\\\t\\\\tbind:currentTime={time}\\\\n\\\\t\\\\t\\\\tbind:duration\\\\n\\\\t\\\\t\\\\tbind:paused\\\\n\\\\t\\\\t\\\\tbind:node={video}\\\\n\\\\t\\\\t\\\\tdata-testid={`${label}-player`}\\\\n\\\\t\\\\t\\\\t{processingVideo}\\\\n\\\\t\\\\t\\\\ton:loadstart\\\\n\\\\t\\\\t\\\\ton:loadeddata\\\\n\\\\t\\\\t\\\\ton:loadedmetadata\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<track kind=\\\\\"captions\\\\\" src={subtitle} default />\\\\n\\\\t\\\\t</Video>\\\\n\\\\t</div>\\\\n\\\\n\\\\t<div class=\\\\\"controls\\\\\">\\\\n\\\\t\\\\t<div class=\\\\\"inner\\\\\">\\\\n\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"play-pause-replay-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={play_pause}\\\\n\\\\t\\\\t\\\\t\\\\ton:keydown={play_pause}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if time === duration}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Undo />\\\\n\\\\t\\\\t\\\\t\\\\t{:else if paused}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Play />\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Pause />\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"time\\\\\">{format_time(time)} / {format_time(duration)}</span>\\\\n\\\\n\\\\t\\\\t\\\\t<!-- TODO: implement accessible video timeline for 4.0 -->\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-click-events-have-key-events -->\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->\\\\n\\\\t\\\\t\\\\t<progress\\\\n\\\\t\\\\t\\\\t\\\\tvalue={time / duration || 0}\\\\n\\\\t\\\\t\\\\t\\\\ton:mousemove={handleMove}\\\\n\\\\t\\\\t\\\\t\\\\ton:touchmove|preventDefault={handleMove}\\\\n\\\\t\\\\t\\\\t\\\\ton:click|stopPropagation|preventDefault={handle_click}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\trole=\\\\\"button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ttabindex=\\\\\"0\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"full-screen\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={open_full_screen}\\\\n\\\\t\\\\t\\\\t\\\\ton:keypress={open_full_screen}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<Maximise />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</div>\\\\n</div>\\\\n{#if interactive}\\\\n\\\\t<VideoControls\\\\n\\\\t\\\\tvideoElement={video}\\\\n\\\\t\\\\tshowRedo\\\\n\\\\t\\\\t{handle_trim_video}\\\\n\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\tbind:processingVideo\\\\n\\\\t\\\\t{value}\\\\n\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t{show_download_button}\\\\n\\\\t\\\\t{handle_clear}\\\\n\\\\t\\\\t{has_change_history}\\\\n\\\\t/>\\\\n{/if}\\\\n\\\\n<style lang=\\\\\"postcss\\\\\">\\\\n\\\\tspan {\\\\n\\\\t\\\\ttext-shadow: 0 0 8px rgba(0, 0, 0, 0.5);\\\\n\\\\t}\\\\n\\\\n\\\\tprogress {\\\\n\\\\t\\\\tmargin-right: var(--size-3);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\tprogress::-webkit-progress-bar {\\\\n\\\\t\\\\tborder-radius: 2px;\\\\n\\\\t\\\\tbackground-color: rgba(255, 255, 255, 0.2);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\tprogress::-webkit-progress-value {\\\\n\\\\t\\\\tbackground-color: rgba(255, 255, 255, 0.9);\\\\n\\\\t}\\\\n\\\\n\\\\t.mirror {\\\\n\\\\t\\\\ttransform: scaleX(-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.mirror-wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.controls {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\topacity: 0;\\\\n\\\\t\\\\ttransition: 500ms;\\\\n\\\\t\\\\tmargin: var(--size-2);\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\tbackground: var(--color-grey-800);\\\\n\\\\t\\\\tpadding: var(--size-2) var(--size-1);\\\\n\\\\t\\\\twidth: calc(100% - 0.375rem * 2);\\\\n\\\\t\\\\twidth: calc(100% - var(--size-2) * 2);\\\\n\\\\t}\\\\n\\\\t.wrap:hover .controls {\\\\n\\\\t\\\\topacity: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.inner {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tpadding-right: var(--size-2);\\\\n\\\\t\\\\tpadding-left: var(--size-2);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: var(--size-6);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t}\\\\n\\\\n\\\\t.time {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tmargin-right: var(--size-3);\\\\n\\\\t\\\\tmargin-left: var(--size-3);\\\\n\\\\t\\\\tcolor: white;\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t}\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t}\\\\n\\\\t.wrap :global(video) {\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAgKC,gCAAK,CACJ,WAAW,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CACvC,CAEA,oCAAS,CACR,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,oCAAQ,sBAAuB,CAC9B,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAC1C,QAAQ,CAAE,MACX,CAEA,oCAAQ,wBAAyB,CAChC,gBAAgB,CAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAC1C,CAEA,mCAAQ,CACP,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,wCAAa,CACZ,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,qCAAU,CACT,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,CAAC,CACT,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAChC,KAAK,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CACrC,CACA,mBAAK,MAAM,CAAC,uBAAU,CACrB,OAAO,CAAE,CACV,CAEA,kCAAO,CACN,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,iCAAM,CACL,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,KAAK,CAAE,KACR,CAEA,iCAAM,CACL,WAAW,CAAE,CAAC,CACd,YAAY,CAAE,IAAI,QAAQ,CAAC,CAC3B,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,WAAW,CAC7B,CACA,iCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,aAAa,CAAE,IAAI,WAAW,CAC/B,CACA,mBAAK,CAAS,KAAO,CACpB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,KAAK,CAAE,IAAI,WAAW,CACvB\"}'\n};\nconst Player = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { root = \"\" } = $$props;\n  let { src } = $$props;\n  let { subtitle = null } = $$props;\n  let { mirror } = $$props;\n  let { autoplay } = $$props;\n  let { loop } = $$props;\n  let { label = \"test\" } = $$props;\n  let { interactive = false } = $$props;\n  let { handle_change = () => {\n  } } = $$props;\n  let { handle_reset_value = () => {\n  } } = $$props;\n  let { upload } = $$props;\n  let { is_stream } = $$props;\n  let { i18n } = $$props;\n  let { show_download_button = false } = $$props;\n  let { value = null } = $$props;\n  let { handle_clear = () => {\n  } } = $$props;\n  let { has_change_history = false } = $$props;\n  createEventDispatcher();\n  let time = 0;\n  let duration;\n  let paused = true;\n  let video;\n  let processingVideo = false;\n  const handle_trim_video = async (videoBlob) => {\n    let _video_blob = new File([videoBlob], \"video.mp4\");\n    const val = await prepare_files([_video_blob]);\n    let value2 = (await upload(val, root))?.filter(Boolean)[0];\n    handle_change(value2);\n  };\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.src === void 0 && $$bindings.src && src !== void 0)\n    $$bindings.src(src);\n  if ($$props.subtitle === void 0 && $$bindings.subtitle && subtitle !== void 0)\n    $$bindings.subtitle(subtitle);\n  if ($$props.mirror === void 0 && $$bindings.mirror && mirror !== void 0)\n    $$bindings.mirror(mirror);\n  if ($$props.autoplay === void 0 && $$bindings.autoplay && autoplay !== void 0)\n    $$bindings.autoplay(autoplay);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.handle_change === void 0 && $$bindings.handle_change && handle_change !== void 0)\n    $$bindings.handle_change(handle_change);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.is_stream === void 0 && $$bindings.is_stream && is_stream !== void 0)\n    $$bindings.is_stream(is_stream);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.handle_clear === void 0 && $$bindings.handle_clear && handle_clear !== void 0)\n    $$bindings.handle_clear(handle_clear);\n  if ($$props.has_change_history === void 0 && $$bindings.has_change_history && has_change_history !== void 0)\n    $$bindings.has_change_history(has_change_history);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    $$rendered = `<div class=\"wrap svelte-euo1cw\"><div class=\"${[\"mirror-wrap svelte-euo1cw\", mirror ? \"mirror\" : \"\"].join(\" \").trim()}\">${validate_component(Video, \"Video\").$$render(\n      $$result,\n      {\n        src,\n        preload: \"auto\",\n        autoplay,\n        loop,\n        is_stream,\n        \"data-testid\": `${label}-player`,\n        processingVideo,\n        currentTime: time,\n        duration,\n        paused,\n        node: video\n      },\n      {\n        currentTime: ($$value) => {\n          time = $$value;\n          $$settled = false;\n        },\n        duration: ($$value) => {\n          duration = $$value;\n          $$settled = false;\n        },\n        paused: ($$value) => {\n          paused = $$value;\n          $$settled = false;\n        },\n        node: ($$value) => {\n          video = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `<track kind=\"captions\"${add_attribute(\"src\", subtitle, 0)} default>`;\n        }\n      }\n    )}</div> <div class=\"controls svelte-euo1cw\"><div class=\"inner svelte-euo1cw\"><span role=\"button\" tabindex=\"0\" class=\"icon svelte-euo1cw\" aria-label=\"play-pause-replay-button\">${time === duration ? `${validate_component(Undo, \"Undo\").$$render($$result, {}, {}, {})}` : `${paused ? `${validate_component(Play, \"Play\").$$render($$result, {}, {}, {})}` : `${validate_component(Pause, \"Pause\").$$render($$result, {}, {}, {})}`}`}</span> <span class=\"time svelte-euo1cw\">${escape(format_time(time))} / ${escape(format_time(duration))}</span>    <progress${add_attribute(\"value\", time / duration || 0, 0)} class=\"svelte-euo1cw\"></progress> <div role=\"button\" tabindex=\"0\" class=\"icon svelte-euo1cw\" aria-label=\"full-screen\">${validate_component(Maximise, \"Maximise\").$$render($$result, {}, {}, {})}</div></div></div></div> ${interactive ? `${validate_component(VideoControls, \"VideoControls\").$$render(\n      $$result,\n      {\n        videoElement: video,\n        showRedo: true,\n        handle_trim_video,\n        handle_reset_value,\n        value,\n        i18n,\n        show_download_button,\n        handle_clear,\n        has_change_history,\n        processingVideo\n      },\n      {\n        processingVideo: ($$value) => {\n          processingVideo = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : ``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst Player$1 = Player;\nconst VideoPreview = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value = null } = $$props;\n  let { subtitle = null } = $$props;\n  let { label = void 0 } = $$props;\n  let { show_label = true } = $$props;\n  let { autoplay } = $$props;\n  let { show_share_button = true } = $$props;\n  let { show_download_button = true } = $$props;\n  let { loop } = $$props;\n  let { i18n } = $$props;\n  let { upload } = $$props;\n  let { display_icon_button_wrapper_top_corner = false } = $$props;\n  let old_value = null;\n  let old_subtitle = null;\n  const dispatch = createEventDispatcher();\n  afterUpdate(async () => {\n    if (value !== old_value && subtitle !== old_subtitle && old_subtitle !== null) {\n      old_value = value;\n      value = null;\n      await tick();\n      value = old_value;\n    }\n    old_value = value;\n    old_subtitle = subtitle;\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.subtitle === void 0 && $$bindings.subtitle && subtitle !== void 0)\n    $$bindings.subtitle(subtitle);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.autoplay === void 0 && $$bindings.autoplay && autoplay !== void 0)\n    $$bindings.autoplay(autoplay);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.display_icon_button_wrapper_top_corner === void 0 && $$bindings.display_icon_button_wrapper_top_corner && display_icon_button_wrapper_top_corner !== void 0)\n    $$bindings.display_icon_button_wrapper_top_corner(display_icon_button_wrapper_top_corner);\n  value && dispatch(\"change\", value);\n  return `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n    $$result,\n    {\n      show_label,\n      Icon: Video$1,\n      label: label || \"Video\"\n    },\n    {},\n    {}\n  )} ${!value || value.url === void 0 ? `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n    default: () => {\n      return `${validate_component(Video$1, \"Video\").$$render($$result, {}, {}, {})}`;\n    }\n  })}` : `${validate_component(Player$1, \"Player\").$$render(\n    $$result,\n    {\n      src: value.url,\n      subtitle: subtitle?.url,\n      is_stream: value.is_stream,\n      autoplay,\n      mirror: false,\n      label,\n      loop,\n      interactive: false,\n      upload,\n      i18n\n    },\n    {},\n    {}\n  )} <div data-testid=\"download-div\">${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render(\n    $$result,\n    {\n      display_top_corner: display_icon_button_wrapper_top_corner\n    },\n    {},\n    {\n      default: () => {\n        return `${show_download_button ? `${validate_component(DownloadLink, \"DownloadLink\").$$render(\n          $$result,\n          {\n            href: value.is_stream ? value.url?.replace(\"playlist.m3u8\", \"playlist-file\") : value.url,\n            download: value.orig_name || value.path\n          },\n          {},\n          {\n            default: () => {\n              return `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Download, label: \"Download\" }, {}, {})}`;\n            }\n          }\n        )}` : ``} ${show_share_button ? `${validate_component(ShareButton, \"ShareButton\").$$render(\n          $$result,\n          {\n            i18n,\n            value,\n            formatter: async (value2) => {\n              if (!value2)\n                return \"\";\n              let url = await uploadToHuggingFace(value2.data);\n              return url;\n            }\n          },\n          {},\n          {}\n        )}` : ``}`;\n      }\n    }\n  )}</div>`}`;\n});\nconst VideoPreview$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  default: VideoPreview\n}, Symbol.toStringTag, { value: \"Module\" }));\nexport {\n  Player$1 as P,\n  VideoPreview as V,\n  playable as a,\n  VideoPreview$1 as b,\n  loaded as l,\n  prettyBytes as p\n};\n"], "names": ["Video", "Video$1"], "mappings": ";;;;;;;;;;;;;AAwDK,MAAC,WAAW,GAAG,CAAC,KAAK,KAAK;AAC/B,EAAE,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,EAAE,OAAO,KAAK,GAAG,IAAI,EAAE;AACvB,IAAI,KAAK,IAAI,IAAI,CAAC;AAClB,IAAI,CAAC,EAAE,CAAC;AACR,GAAG;AACH,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;AACvC,EAAE;AACG,MAAC,QAAQ,GAAG,MAAM;AACvB,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF,SAAS,MAAM,CAAC,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE;AACpC,EAAE,eAAe,eAAe,GAAG;AACnC,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACtB,GAAG;AACH,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;AACvD,EAAE,OAAO;AACT,IAAI,OAAO,GAAG;AACd,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;AAC9D,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AAUD,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,06CAA06C;AACl7C,EAAE,GAAG,EAAE,qiTAAqiT;AAC5iT,CAAC,CAAC;AACF,IAAI,kBAAkB,GAAG,EAAE,CAAC;AAC5B,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AACtB,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,kBAAkB,GAAG,CAAC,CAAC;AAC7B,EAAE,IAAI,mBAAmB,GAAG,GAAG,CAAC;AAChC,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC;AACtB,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC;AACpB,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,QAAQ,KAAK;AACpC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC3D,MAAM,IAAI,CAAC,QAAQ;AACnB,QAAQ,OAAO;AACf,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;AACpD,MAAM,IAAI,aAAa,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACzE,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,aAAa,GAAG,QAAQ,KAAK,MAAM,GAAG,kBAAkB,GAAG,QAAQ,GAAG,mBAAmB,GAAG,QAAQ,CAAC;AAC7G,OAAO,MAAM;AACb,QAAQ,aAAa,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACvE,OAAO;AACP,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC;AAChE,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;AAC/B,QAAQ,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;AAC1E,QAAQ,MAAM,WAAW,GAAG,kBAAkB,GAAG,GAAG,GAAG,aAAa,CAAC;AACrE,QAAQ,YAAY,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/C,QAAQ,SAAS,GAAG,WAAW,CAAC;AAChC,OAAO,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACvC,QAAQ,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AAC1E,QAAQ,MAAM,YAAY,GAAG,mBAAmB,GAAG,GAAG,GAAG,aAAa,CAAC;AACvE,QAAQ,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC;AAChD,QAAQ,OAAO,GAAG,YAAY,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM,SAAS,GAAG,kBAAkB,GAAG,GAAG,GAAG,aAAa,CAAC;AACjE,MAAM,MAAM,OAAO,GAAG,mBAAmB,GAAG,GAAG,GAAG,aAAa,CAAC;AAChE,MAAM,eAAe,GAAG,OAAO,GAAG,SAAS,CAAC;AAC5C,MAAM,kBAAkB,GAAG,kBAAkB,CAAC;AAC9C,MAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAChD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK;AAC5B,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,aAAa,GAAG,GAAG,CAAC;AAC/C,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,WAAW,EAAE;AACjC,QAAQ,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;AACxC,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,YAAY,EAAE;AACzC,QAAQ,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AAmCJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAClD,IAAI,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AACxD,IAAI,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACtD,GAAG,CAAC,CAAC;AAML,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,eAAe,GAAG,UAAU,CAAC,MAAM,KAAK,kBAAkB,CAAC;AAC7D,EAAE,OAAO,CAAC,sCAAsC,EAAE,eAAe,GAAG,CAAC,oJAAoJ,CAAC,GAAG,CAAC,gKAAgK,EAAE,QAAQ,GAAG,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,4DAA4D,EAAE,QAAQ,GAAG,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,GAAG,GAAG,mBAAmB,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK;AAC1oB,IAAI,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,0CAA0C,CAAC,CAAC;AACzI,GAAG,CAAC,CAAC,oGAAoG,EAAE,QAAQ,GAAG,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC;AAC3L,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,qzCAAqzC;AAC7zC,EAAE,GAAG,EAAE,u1MAAu1M;AAC91M,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,WAAW,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,YAAY,GAAG,MAAM;AAC7B,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAI/C,EAAE,IAAI,eAAe,GAAG,IAAI,CAAC;AAC7B,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC;AACpB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,eAAe,KAAK,IAAI,IAAI,YAAY;AACrE,QAAQ,eAAe,GAAG,YAAY,CAAC,QAAQ,CAAC;AAChD,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,YAAY,EAAE,CAAC,yBAAyB,EAAE,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,GAAG,CAAC,4CAA4C,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ;AAC9O,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,YAAY;AACpB,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,eAAe;AACvB,QAAQ,eAAe;AACvB,OAAO;AACP,MAAM;AACN,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,OAAO,EAAE,CAAC,OAAO,KAAK;AAC9B,UAAU,OAAO,GAAG,OAAO,CAAC;AAC5B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,OAAO,KAAK;AACtC,UAAU,eAAe,GAAG,OAAO,CAAC;AACpC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,eAAe,EAAE,CAAC,OAAO,KAAK;AACtC,UAAU,eAAe,GAAG,OAAO,CAAC;AACpC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,qEAAqE,EAAE,IAAI,KAAK,MAAM,IAAI,eAAe,KAAK,IAAI,GAAG,CAAC,iEAAiE,EAAE,CAAC,eAAe,EAAE,eAAe,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,+DAA+D,EAAE,CAAC,2BAA2B,EAAE,eAAe,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,8DAA8D,EAAE,CAAC,2BAA2B,EAAE,eAAe,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,sDAAsD,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC,aAAa,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAChwB,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,QAAQ,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,GAAG,IAAI;AAC1D,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,QAAQ,IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACtG,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,IAAI,EAAE,IAAI;AACxB,cAAc,KAAK,EAAE,8BAA8B;AACnD,cAAc,QAAQ,EAAE,eAAe,IAAI,CAAC,kBAAkB;AAC9D,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AAC3G,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,IAAI,EAAE,IAAI;AACxB,cAAc,KAAK,EAAE,yBAAyB;AAC9C,cAAc,QAAQ,EAAE,eAAe;AACvC,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,ooDAAooD;AAC5oD,EAAE,GAAG,EAAE,siRAAsiR;AAC7iR,CAAC,CAAC;AACF,MAAM,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;AACxB,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM;AAC9B,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,kBAAkB,GAAG,MAAM;AACnC,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,MAAM;AAC7B,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,kBAAkB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC/C,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;AACpB,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,MAAM,iBAAiB,GAAG,OAAO,SAAS,KAAK;AACjD,IAAI,IAAI,WAAW,GAAG,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACnD,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,CAAC,4CAA4C,EAAE,CAAC,2BAA2B,EAAE,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAACA,OAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACtL,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG;AACX,QAAQ,OAAO,EAAE,MAAM;AACvB,QAAQ,QAAQ;AAChB,QAAQ,IAAI;AACZ,QAAQ,SAAS;AACjB,QAAQ,aAAa,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC;AACxC,QAAQ,eAAe;AACvB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,QAAQ;AAChB,QAAQ,MAAM;AACd,QAAQ,IAAI,EAAE,KAAK;AACnB,OAAO;AACP,MAAM;AACN,QAAQ,WAAW,EAAE,CAAC,OAAO,KAAK;AAClC,UAAU,IAAI,GAAG,OAAO,CAAC;AACzB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,OAAO,KAAK;AAC7B,UAAU,MAAM,GAAG,OAAO,CAAC;AAC3B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,KAAK,GAAG,OAAO,CAAC;AAC1B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,sBAAsB,EAAE,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AACvF,SAAS;AACT,OAAO;AACP,KAAK,CAAC,8KAA8K,EAAE,IAAI,KAAK,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,oBAAoB,EAAE,aAAa,CAAC,OAAO,EAAE,IAAI,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,uHAAuH,EAAE,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,yBAAyB,EAAE,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ;AACn4B,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,YAAY,EAAE,KAAK;AAC3B,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,iBAAiB;AACzB,QAAQ,kBAAkB;AAC1B,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,oBAAoB;AAC5B,QAAQ,YAAY;AACpB,QAAQ,kBAAkB;AAC1B,QAAQ,eAAe;AACvB,OAAO;AACP,MAAM;AACN,QAAQ,eAAe,EAAE,CAAC,OAAO,KAAK;AACtC,UAAU,eAAe,GAAG,OAAO,CAAC;AACpC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,QAAQ,GAAG,OAAO;AACnB,MAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,iBAAiB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,oBAAoB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,sCAAsC,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAGnE,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAW3C,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,sCAAsC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sCAAsC,IAAI,sCAAsC,KAAK,KAAK,CAAC;AACzK,IAAI,UAAU,CAAC,sCAAsC,CAAC,sCAAsC,CAAC,CAAC;AAC9F,EAAE,KAAK,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrC,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjE,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,UAAU;AAChB,MAAM,IAAI,EAAEC,KAAO;AACnB,MAAM,KAAK,EAAE,KAAK,IAAI,OAAO;AAC7B,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC5I,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,kBAAkB,CAACA,KAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACtF,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC3D,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,GAAG,EAAE,KAAK,CAAC,GAAG;AACpB,MAAM,QAAQ,EAAE,QAAQ,EAAE,GAAG;AAC7B,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;AAChC,MAAM,QAAQ;AACd,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,KAAK;AACX,MAAM,IAAI;AACV,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,iCAAiC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ;AAC1G,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,kBAAkB,EAAE,sCAAsC;AAChE,KAAK;AACL,IAAI,EAAE;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,oBAAoB,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACrG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,eAAe,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,GAAG;AACpG,YAAY,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI;AACnD,WAAW;AACX,UAAU,EAAE;AACZ,UAAU;AACV,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzI,aAAa;AACb,WAAW;AACX,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAClG,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,IAAI;AAChB,YAAY,KAAK;AACjB,YAAY,SAAS,EAAE,OAAO,MAAM,KAAK;AACzC,cAAc,IAAI,CAAC,MAAM;AACzB,gBAAgB,OAAO,EAAE,CAAC;AAC1B,cAAc,IAAI,GAAG,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC/D,cAAc,OAAO,GAAG,CAAC;AACzB,aAAa;AACb,WAAW;AACX,UAAU,EAAE;AACZ,UAAU,EAAE;AACZ,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnB,OAAO;AACP,KAAK;AACL,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,CAAC,EAAE;AACE,MAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,iBAAiB,MAAM,CAAC,cAAc,CAAC;AAC3F,EAAE,SAAS,EAAE,IAAI;AACjB,EAAE,OAAO,EAAE,YAAY;AACvB,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;;;;"}