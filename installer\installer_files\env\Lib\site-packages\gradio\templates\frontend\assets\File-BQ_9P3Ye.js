const{SvelteComponent:c,append:s,attr:t,detach:h,init:d,insert:_,noop:r,safe_not_equal:u,svg_element:i}=window.__gradio__svelte__internal;function w(a){let e,n,o;return{c(){e=i("svg"),n=i("path"),o=i("polyline"),t(n,"d","M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"),t(o,"points","13 2 13 9 20 9"),t(e,"xmlns","http://www.w3.org/2000/svg"),t(e,"width","100%"),t(e,"height","100%"),t(e,"viewBox","0 0 24 24"),t(e,"fill","none"),t(e,"stroke","currentColor"),t(e,"stroke-width","1.5"),t(e,"stroke-linecap","round"),t(e,"stroke-linejoin","round"),t(e,"class","feather feather-file")},m(l,p){_(l,e,p),s(e,n),s(e,o)},p:r,i:r,o:r,d(l){l&&h(e)}}}class f extends c{constructor(e){super(),d(this,e,null,w,u,{})}}export{f as F};
//# sourceMappingURL=File-BQ_9P3Ye.js.map
