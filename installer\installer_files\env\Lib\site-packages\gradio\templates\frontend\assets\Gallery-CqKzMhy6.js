import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{B as Ke}from"./BlockLabel-CCoHIDM7.js";import{I as Be}from"./IconButton-B3BI2i6T.js";import{E as Qe}from"./Empty-B_fwEKaS.js";import{S as Ye}from"./ShareButton-HkxSYutN.js";import{C as Ze}from"./Clear-By3xiIwg.js";import{D as xe}from"./Download-DVtk-Jv3.js";import{I as Le,F as et}from"./FullscreenButton-BWloBRD_.js";import{P as Te}from"./Play-B0Q0U1Qz.js";import{I as tt}from"./IconButtonWrapper-BMUxHqmv.js";import"./index-BkoKOheB.js";/* empty css                                             */import{M as lt}from"./ModifyUpload-89NSLc0b.js";import{I as re}from"./Image-BRVH1yXn.js";/* empty css                                                   *//* empty css                                                   */import{V as oe}from"./Video-DiLYgEjl.js";import{u as nt}from"./utils-BsGrhMNe.js";import"./prism-python-B8dcvKZU.js";import"./Community-Dw1micSV.js";import"./svelte/svelte.js";import"./Undo-DCjBnnSO.js";import"./DownloadLink-IzUam-rM.js";import"./context-TgWPFwN2.js";import"./file-url-DgijyRSD.js";import"./hls-CnVhpNcu.js";var ce=Object.prototype.hasOwnProperty;function _e(l,e,n){for(n of l.keys())if(Q(n,e))return n}function Q(l,e){var n,t,r;if(l===e)return!0;if(l&&e&&(n=l.constructor)===e.constructor){if(n===Date)return l.getTime()===e.getTime();if(n===RegExp)return l.toString()===e.toString();if(n===Array){if((t=l.length)===e.length)for(;t--&&Q(l[t],e[t]););return t===-1}if(n===Set){if(l.size!==e.size)return!1;for(t of l)if(r=t,r&&typeof r=="object"&&(r=_e(e,r),!r)||!e.has(r))return!1;return!0}if(n===Map){if(l.size!==e.size)return!1;for(t of l)if(r=t[0],r&&typeof r=="object"&&(r=_e(e,r),!r)||!Q(t[1],e.get(r)))return!1;return!0}if(n===ArrayBuffer)l=new Uint8Array(l),e=new Uint8Array(e);else if(n===DataView){if((t=l.byteLength)===e.byteLength)for(;t--&&l.getInt8(t)===e.getInt8(t););return t===-1}if(ArrayBuffer.isView(l)){if((t=l.byteLength)===e.byteLength)for(;t--&&l[t]===e[t];);return t===-1}if(!n||typeof l=="object"){t=0;for(n in l)if(ce.call(l,n)&&++t&&!ce.call(e,n)||!(n in e)||!Q(l[n],e[n]))return!1;return Object.keys(e).length===t}}return l!==l&&e!==e}async function it(l){return l?`<div style="display: flex; flex-wrap: wrap; gap: 16px">${(await Promise.all(l.map(async([n,t])=>n===null||!n.url?"":await nt(n.url)))).map(n=>`<img src="${n}" style="height: 400px" />`).join("")}</div>`:""}const{SvelteComponent:rt,add_render_callback:ot,append:A,attr:I,binding_callbacks:ie,bubble:me,check_outros:C,create_component:B,destroy_component:L,destroy_each:Ee,detach:E,element:O,empty:De,ensure_array_like:te,flush:z,globals:ft,group_outros:N,init:st,insert:D,is_function:ut,listen:Y,mount_component:T,noop:at,run_all:ct,safe_not_equal:_t,set_data:Re,set_style:P,space:U,text:Ae,toggle_class:R,transition_in:h,transition_out:d}=window.__gradio__svelte__internal,{window:Ue}=ft,{createEventDispatcher:mt,onMount:ht}=window.__gradio__svelte__internal,{tick:gt}=window.__gradio__svelte__internal;function he(l,e,n){const t=l.slice();return t[47]=e[n],t[49]=n,t}function ge(l,e,n){const t=l.slice();return t[50]=e[n],t[51]=e,t[49]=n,t}function pe(l){let e,n;return e=new Ke({props:{show_label:l[2],Icon:Le,label:l[3]||"Gallery"}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&4&&(i.show_label=t[2]),r[0]&8&&(i.label=t[3]||"Gallery"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function pt(l){let e,n,t,r,i,s,c=l[22]&&l[7]&&de(l),o=l[12]&&l[1]===null&&je(l),p=te(l[16]),u=[];for(let a=0;a<p.length;a+=1)u[a]=Ie(he(l,p,a));const g=a=>d(u[a],1,1,()=>{u[a]=null});return{c(){e=O("div"),c&&c.c(),n=U(),t=O("div"),o&&o.c(),r=U(),i=O("div");for(let a=0;a<u.length;a+=1)u[a].c();I(i,"class","grid-container svelte-842rpi"),P(i,"--grid-cols",l[4]),P(i,"--grid-rows",l[5]),P(i,"--object-fit",l[8]),P(i,"height",l[6]),R(i,"pt-6",l[2]),I(t,"class","grid-wrap svelte-842rpi"),R(t,"minimal",l[13]==="minimal"),R(t,"fixed-height",l[13]!=="minimal"&&(!l[6]||l[6]=="auto")),R(t,"hidden",l[17]),I(e,"class","gallery-container")},m(a,_){D(a,e,_),c&&c.m(e,null),A(e,n),A(e,t),o&&o.m(t,null),A(t,r),A(t,i);for(let b=0;b<u.length;b+=1)u[b]&&u[b].m(i,null);l[42](e),s=!0},p(a,_){if(a[22]&&a[7]?c?(c.p(a,_),_[0]&4194432&&h(c,1)):(c=de(a),c.c(),h(c,1),c.m(e,n)):c&&(N(),d(c,1,1,()=>{c=null}),C()),a[12]&&a[1]===null?o?(o.p(a,_),_[0]&4098&&h(o,1)):(o=je(a),o.c(),h(o,1),o.m(t,r)):o&&(N(),d(o,1,1,()=>{o=null}),C()),_[0]&65538){p=te(a[16]);let b;for(b=0;b<p.length;b+=1){const $=he(a,p,b);u[b]?(u[b].p($,_),h(u[b],1)):(u[b]=Ie($),u[b].c(),h(u[b],1),u[b].m(i,null))}for(N(),b=p.length;b<u.length;b+=1)g(b);C()}(!s||_[0]&16)&&P(i,"--grid-cols",a[4]),(!s||_[0]&32)&&P(i,"--grid-rows",a[5]),(!s||_[0]&256)&&P(i,"--object-fit",a[8]),(!s||_[0]&64)&&P(i,"height",a[6]),(!s||_[0]&4)&&R(i,"pt-6",a[2]),(!s||_[0]&8192)&&R(t,"minimal",a[13]==="minimal"),(!s||_[0]&8256)&&R(t,"fixed-height",a[13]!=="minimal"&&(!a[6]||a[6]=="auto")),(!s||_[0]&131072)&&R(t,"hidden",a[17])},i(a){if(!s){h(c),h(o);for(let _=0;_<p.length;_+=1)h(u[_]);s=!0}},o(a){d(c),d(o),u=u.filter(Boolean);for(let _=0;_<u.length;_+=1)d(u[_]);s=!1},d(a){a&&E(e),c&&c.d(),o&&o.d(),Ee(u,a),l[42](null)}}}function dt(l){let e,n;return e=new Qe({props:{unpadded_box:!0,size:"large",$$slots:{default:[zt]},$$scope:{ctx:l}}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[1]&2097152&&(i.$$scope={dirty:r,ctx:t}),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function de(l){let e,n,t,r,i,s,c,o,p,u,g,a;n=new tt({props:{display_top_corner:l[15],$$slots:{default:[bt]},$$scope:{ctx:l}}});const _=[kt,wt],b=[];function $(k,v){return"image"in k[22]?0:1}i=$(l),s=b[i]=_[i](l);let w=l[22]?.caption&&ye(l),y=te(l[16]),m=[];for(let k=0;k<y.length;k+=1)m[k]=$e(ge(l,y,k));const Z=k=>d(m[k],1,1,()=>{m[k]=null});return{c(){e=O("button"),B(n.$$.fragment),t=U(),r=O("button"),s.c(),c=U(),w&&w.c(),o=U(),p=O("div");for(let k=0;k<m.length;k+=1)m[k].c();I(r,"class","media-button svelte-842rpi"),P(r,"height","calc(100% - "+(l[22].caption?"80px":"60px")+")"),I(r,"aria-label","detailed view of selected image"),I(p,"class","thumbnails scroll-hide svelte-842rpi"),I(p,"data-testid","container_el"),I(e,"class","preview svelte-842rpi"),R(e,"minimal",l[13]==="minimal")},m(k,v){D(k,e,v),T(n,e,null),A(e,t),A(e,r),b[i].m(r,null),A(e,c),w&&w.m(e,null),A(e,o),A(e,p);for(let S=0;S<m.length;S+=1)m[S]&&m[S].m(p,null);l[39](p),u=!0,g||(a=[Y(r,"click",function(){ut("image"in l[22]?l[36]:null)&&("image"in l[22]?l[36]:null).apply(this,arguments)}),Y(e,"keydown",l[24])],g=!0)},p(k,v){l=k;const S={};v[0]&32768&&(S.display_top_corner=l[15]),v[0]&4673026|v[1]&2097152&&(S.$$scope={dirty:v,ctx:l}),n.$set(S);let q=i;if(i=$(l),i===q?b[i].p(l,v):(N(),d(b[q],1,1,()=>{b[q]=null}),C(),s=b[i],s?s.p(l,v):(s=b[i]=_[i](l),s.c()),h(s,1),s.m(r,null)),(!u||v[0]&4194304)&&P(r,"height","calc(100% - "+(l[22].caption?"80px":"60px")+")"),l[22]?.caption?w?w.p(l,v):(w=ye(l),w.c(),w.m(e,o)):w&&(w.d(1),w=null),v[0]&598018){y=te(l[16]);let j;for(j=0;j<y.length;j+=1){const H=ge(l,y,j);m[j]?(m[j].p(H,v),h(m[j],1)):(m[j]=$e(H),m[j].c(),h(m[j],1),m[j].m(p,null))}for(N(),j=y.length;j<m.length;j+=1)Z(j);C()}(!u||v[0]&8192)&&R(e,"minimal",l[13]==="minimal")},i(k){if(!u){h(n.$$.fragment,k),h(s);for(let v=0;v<y.length;v+=1)h(m[v]);u=!0}},o(k){d(n.$$.fragment,k),d(s),m=m.filter(Boolean);for(let v=0;v<m.length;v+=1)d(m[v]);u=!1},d(k){k&&E(e),L(n),b[i].d(),w&&w.d(),Ee(m,k),l[39](null),g=!1,ct(a)}}}function be(l){let e,n;return e=new Be({props:{Icon:xe,label:l[11]("common.download")}}),e.$on("click",l[32]),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&2048&&(i.label=t[11]("common.download")),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function we(l){let e,n;return e=new et({props:{container:l[18]}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&262144&&(i.container=t[18]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function ke(l){let e,n,t;return n=new Ye({props:{i18n:l[11],value:l[16],formatter:it}}),n.$on("share",l[33]),n.$on("error",l[34]),{c(){e=O("div"),B(n.$$.fragment),I(e,"class","icon-button")},m(r,i){D(r,e,i),T(n,e,null),t=!0},p(r,i){const s={};i[0]&2048&&(s.i18n=r[11]),i[0]&65536&&(s.value=r[16]),n.$set(s)},i(r){t||(h(n.$$.fragment,r),t=!0)},o(r){d(n.$$.fragment,r),t=!1},d(r){r&&E(e),L(n)}}}function ve(l){let e,n;return e=new Be({props:{Icon:Ze,label:"Close"}}),e.$on("click",l[35]),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p:at,i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function bt(l){let e,n,t,r,i,s=l[10]&&be(l),c=l[14]&&we(l),o=l[9]&&ke(l),p=!l[17]&&ve(l);return{c(){s&&s.c(),e=U(),c&&c.c(),n=U(),o&&o.c(),t=U(),p&&p.c(),r=De()},m(u,g){s&&s.m(u,g),D(u,e,g),c&&c.m(u,g),D(u,n,g),o&&o.m(u,g),D(u,t,g),p&&p.m(u,g),D(u,r,g),i=!0},p(u,g){u[10]?s?(s.p(u,g),g[0]&1024&&h(s,1)):(s=be(u),s.c(),h(s,1),s.m(e.parentNode,e)):s&&(N(),d(s,1,1,()=>{s=null}),C()),u[14]?c?(c.p(u,g),g[0]&16384&&h(c,1)):(c=we(u),c.c(),h(c,1),c.m(n.parentNode,n)):c&&(N(),d(c,1,1,()=>{c=null}),C()),u[9]?o?(o.p(u,g),g[0]&512&&h(o,1)):(o=ke(u),o.c(),h(o,1),o.m(t.parentNode,t)):o&&(N(),d(o,1,1,()=>{o=null}),C()),u[17]?p&&(N(),d(p,1,1,()=>{p=null}),C()):p?(p.p(u,g),g[0]&131072&&h(p,1)):(p=ve(u),p.c(),h(p,1),p.m(r.parentNode,r))},i(u){i||(h(s),h(c),h(o),h(p),i=!0)},o(u){d(s),d(c),d(o),d(p),i=!1},d(u){u&&(E(e),E(n),E(t),E(r)),s&&s.d(u),c&&c.d(u),o&&o.d(u),p&&p.d(u)}}}function wt(l){let e,n;return e=new oe({props:{src:l[22].video.url,"data-testid":"detailed-video",alt:l[22].caption||"",loading:"lazy",loop:!1,is_stream:!1,muted:!1,controls:!0}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&4194304&&(i.src=t[22].video.url),r[0]&4194304&&(i.alt=t[22].caption||""),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function kt(l){let e,n;return e=new re({props:{"data-testid":"detailed-image",src:l[22].image.url,alt:l[22].caption||"",title:l[22].caption||null,class:l[22].caption&&"with-caption",loading:"lazy"}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&4194304&&(i.src=t[22].image.url),r[0]&4194304&&(i.alt=t[22].caption||""),r[0]&4194304&&(i.title=t[22].caption||null),r[0]&4194304&&(i.class=t[22].caption&&"with-caption"),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function ye(l){let e,n=l[22].caption+"",t;return{c(){e=O("caption"),t=Ae(n),I(e,"class","caption svelte-842rpi")},m(r,i){D(r,e,i),A(e,t)},p(r,i){i[0]&4194304&&n!==(n=r[22].caption+"")&&Re(t,n)},d(r){r&&E(e)}}}function vt(l){let e,n,t,r;return e=new Te({}),t=new oe({props:{src:l[50].video.url,title:l[50].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){B(e.$$.fragment),n=U(),B(t.$$.fragment)},m(i,s){T(e,i,s),D(i,n,s),T(t,i,s),r=!0},p(i,s){const c={};s[0]&65536&&(c.src=i[50].video.url),s[0]&65536&&(c.title=i[50].caption||null),t.$set(c)},i(i){r||(h(e.$$.fragment,i),h(t.$$.fragment,i),r=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),r=!1},d(i){i&&E(n),L(e,i),L(t,i)}}}function yt(l){let e,n;return e=new re({props:{src:l[50].image.url,title:l[50].caption||null,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy"}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&65536&&(i.src=t[50].image.url),r[0]&65536&&(i.title=t[50].caption||null),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function $e(l){let e,n,t,r,i,s=l[49],c,o,p;const u=[yt,vt],g=[];function a(w,y){return"image"in w[50]?0:1}n=a(l),t=g[n]=u[n](l);const _=()=>l[37](e,s),b=()=>l[37](null,s);function $(){return l[38](l[49])}return{c(){e=O("button"),t.c(),r=U(),I(e,"class","thumbnail-item thumbnail-small svelte-842rpi"),I(e,"aria-label",i="Thumbnail "+(l[49]+1)+" of "+l[16].length),R(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},m(w,y){D(w,e,y),g[n].m(e,null),A(e,r),_(),c=!0,o||(p=Y(e,"click",$),o=!0)},p(w,y){l=w;let m=n;n=a(l),n===m?g[n].p(l,y):(N(),d(g[m],1,1,()=>{g[m]=null}),C(),t=g[n],t?t.p(l,y):(t=g[n]=u[n](l),t.c()),h(t,1),t.m(e,r)),(!c||y[0]&65536&&i!==(i="Thumbnail "+(l[49]+1)+" of "+l[16].length))&&I(e,"aria-label",i),s!==l[49]&&(b(),s=l[49],_()),(!c||y[0]&8194)&&R(e,"selected",l[1]===l[49]&&l[13]!=="minimal")},i(w){c||(h(t),c=!0)},o(w){d(t),c=!1},d(w){w&&E(e),g[n].d(),b(),o=!1,p()}}}function je(l){let e,n;return e=new lt({props:{i18n:l[11]}}),e.$on("clear",l[40]),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&2048&&(i.i18n=t[11]),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function $t(l){let e,n,t,r;return e=new Te({}),t=new oe({props:{src:l[47].video.url,title:l[47].caption||null,is_stream:!1,"data-testid":"thumbnail "+(l[49]+1),alt:"",loading:"lazy",loop:!1}}),{c(){B(e.$$.fragment),n=U(),B(t.$$.fragment)},m(i,s){T(e,i,s),D(i,n,s),T(t,i,s),r=!0},p(i,s){const c={};s[0]&65536&&(c.src=i[47].video.url),s[0]&65536&&(c.title=i[47].caption||null),t.$set(c)},i(i){r||(h(e.$$.fragment,i),h(t.$$.fragment,i),r=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),r=!1},d(i){i&&E(n),L(e,i),L(t,i)}}}function jt(l){let e,n;return e=new re({props:{alt:l[47].caption||"",src:typeof l[47].image=="string"?l[47].image:l[47].image.url,loading:"lazy"}}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},p(t,r){const i={};r[0]&65536&&(i.alt=t[47].caption||""),r[0]&65536&&(i.src=typeof t[47].image=="string"?t[47].image:t[47].image.url),e.$set(i)},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function ze(l){let e,n=l[47].caption+"",t;return{c(){e=O("div"),t=Ae(n),I(e,"class","caption-label svelte-842rpi")},m(r,i){D(r,e,i),A(e,t)},p(r,i){i[0]&65536&&n!==(n=r[47].caption+"")&&Re(t,n)},d(r){r&&E(e)}}}function Ie(l){let e,n,t,r,i,s,c,o,p;const u=[jt,$t],g=[];function a($,w){return"image"in $[47]?0:1}n=a(l),t=g[n]=u[n](l);let _=l[47].caption&&ze(l);function b(){return l[41](l[49])}return{c(){e=O("button"),t.c(),r=U(),_&&_.c(),i=U(),I(e,"class","thumbnail-item thumbnail-lg svelte-842rpi"),I(e,"aria-label",s="Thumbnail "+(l[49]+1)+" of "+l[16].length),R(e,"selected",l[1]===l[49])},m($,w){D($,e,w),g[n].m(e,null),A(e,r),_&&_.m(e,null),A(e,i),c=!0,o||(p=Y(e,"click",b),o=!0)},p($,w){l=$;let y=n;n=a(l),n===y?g[n].p(l,w):(N(),d(g[y],1,1,()=>{g[y]=null}),C(),t=g[n],t?t.p(l,w):(t=g[n]=u[n](l),t.c()),h(t,1),t.m(e,r)),l[47].caption?_?_.p(l,w):(_=ze(l),_.c(),_.m(e,i)):_&&(_.d(1),_=null),(!c||w[0]&65536&&s!==(s="Thumbnail "+(l[49]+1)+" of "+l[16].length))&&I(e,"aria-label",s),(!c||w[0]&2)&&R(e,"selected",l[1]===l[49])},i($){c||(h(t),c=!0)},o($){d(t),c=!1},d($){$&&E(e),g[n].d(),_&&_.d(),o=!1,p()}}}function zt(l){let e,n;return e=new Le({}),{c(){B(e.$$.fragment)},m(t,r){T(e,t,r),n=!0},i(t){n||(h(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){L(e,t)}}}function It(l){let e,n,t,r,i,s,c;ot(l[31]);let o=l[2]&&pe(l);const p=[dt,pt],u=[];function g(a,_){return a[0]==null||a[16]==null||a[16].length===0?0:1}return n=g(l),t=u[n]=p[n](l),{c(){o&&o.c(),e=U(),t.c(),r=De()},m(a,_){o&&o.m(a,_),D(a,e,_),u[n].m(a,_),D(a,r,_),i=!0,s||(c=Y(Ue,"resize",l[31]),s=!0)},p(a,_){a[2]?o?(o.p(a,_),_[0]&4&&h(o,1)):(o=pe(a),o.c(),h(o,1),o.m(e.parentNode,e)):o&&(N(),d(o,1,1,()=>{o=null}),C());let b=n;n=g(a),n===b?u[n].p(a,_):(N(),d(u[b],1,1,()=>{u[b]=null}),C(),t=u[n],t?t.p(a,_):(t=u[n]=p[n](a),t.c()),h(t,1),t.m(r.parentNode,r))},i(a){i||(h(o),h(t),i=!0)},o(a){d(o),d(t),i=!1},d(a){a&&(E(e),E(r)),o&&o.d(a),u[n].d(a),s=!1,c()}}}function Bt(l,e,n){let t,r,i,{show_label:s=!0}=e,{label:c}=e,{value:o=null}=e,{columns:p=[2]}=e,{rows:u=void 0}=e,{height:g="auto"}=e,{preview:a}=e,{allow_preview:_=!0}=e,{object_fit:b="cover"}=e,{show_share_button:$=!1}=e,{show_download_button:w=!1}=e,{i18n:y}=e,{selected_index:m=null}=e,{interactive:Z}=e,{_fetch:k}=e,{mode:v="normal"}=e,{show_fullscreen_button:S=!0}=e,{display_icon_button_wrapper_top_corner:q=!1}=e,j=!1,H;const fe=mt();let J=!0,V=null,le=o;m==null&&a&&o?.length&&(m=0);let ne=m;function se(f){const M=f.target,G=f.offsetX,X=M.offsetWidth/2;G<X?n(1,m=t):n(1,m=r)}function Ce(f){switch(f.code){case"Escape":f.preventDefault(),n(1,m=null);break;case"ArrowLeft":f.preventDefault(),n(1,m=t);break;case"ArrowRight":f.preventDefault(),n(1,m=r);break}}let W=[],F;async function Ne(f){if(typeof f!="number"||(await gt(),W[f]===void 0))return;W[f]?.focus();const{left:M,width:G}=F.getBoundingClientRect(),{left:x,width:X}=W[f].getBoundingClientRect(),K=x-M+X/2-G/2+F.scrollLeft;F&&typeof F.scrollTo=="function"&&F.scrollTo({left:K<0?0:K,behavior:"smooth"})}let ue=0;async function ae(f,M){let G;try{G=await k(f)}catch(K){if(K instanceof TypeError){window.open(f,"_blank","noreferrer");return}throw K}const x=await G.blob(),X=URL.createObjectURL(x),ee=document.createElement("a");ee.href=X,ee.download=M,ee.click(),URL.revokeObjectURL(X)}ht(()=>{document.addEventListener("fullscreenchange",()=>{n(17,j=!!document.fullscreenElement)})});function Se(){n(21,ue=Ue.innerHeight)}const Oe=()=>{const f="image"in i?i?.image:i?.video;if(f==null)return;const{url:M,orig_name:G}=f;M&&ae(M,G??"image")};function Me(f){me.call(this,l,f)}function Pe(f){me.call(this,l,f)}const Ve=()=>n(1,m=null),Fe=f=>se(f);function Ge(f,M){ie[f?"unshift":"push"](()=>{W[M]=f,n(19,W)})}const qe=f=>n(1,m=f);function He(f){ie[f?"unshift":"push"](()=>{F=f,n(20,F)})}const We=()=>n(0,o=[]),Xe=f=>n(1,m=f);function Je(f){ie[f?"unshift":"push"](()=>{H=f,n(18,H)})}return l.$$set=f=>{"show_label"in f&&n(2,s=f.show_label),"label"in f&&n(3,c=f.label),"value"in f&&n(0,o=f.value),"columns"in f&&n(4,p=f.columns),"rows"in f&&n(5,u=f.rows),"height"in f&&n(6,g=f.height),"preview"in f&&n(26,a=f.preview),"allow_preview"in f&&n(7,_=f.allow_preview),"object_fit"in f&&n(8,b=f.object_fit),"show_share_button"in f&&n(9,$=f.show_share_button),"show_download_button"in f&&n(10,w=f.show_download_button),"i18n"in f&&n(11,y=f.i18n),"selected_index"in f&&n(1,m=f.selected_index),"interactive"in f&&n(12,Z=f.interactive),"_fetch"in f&&n(27,k=f._fetch),"mode"in f&&n(13,v=f.mode),"show_fullscreen_button"in f&&n(14,S=f.show_fullscreen_button),"display_icon_button_wrapper_top_corner"in f&&n(15,q=f.display_icon_button_wrapper_top_corner)},l.$$.update=()=>{l.$$.dirty[0]&268435457&&n(28,J=o==null||o.length===0?!0:J),l.$$.dirty[0]&1&&n(16,V=o==null?null:o.map(f=>"video"in f?{video:f.video,caption:f.caption}:"image"in f?{image:f.image,caption:f.caption}:{})),l.$$.dirty[0]&872415235&&(Q(le,o)||(J?(n(1,m=a&&o?.length?0:null),n(28,J=!1)):n(1,m=m!=null&&o!=null&&m<o.length?m:null),fe("change"),n(29,le=o))),l.$$.dirty[0]&65538&&(t=((m??0)+(V?.length??0)-1)%(V?.length??0)),l.$$.dirty[0]&65538&&(r=((m??0)+1)%(V?.length??0)),l.$$.dirty[0]&1073807362&&m!==ne&&(n(30,ne=m),m!==null&&fe("select",{index:m,value:V?.[m]})),l.$$.dirty[0]&130&&_&&Ne(m),l.$$.dirty[0]&65538&&n(22,i=m!=null&&V!=null?V[m]:null)},[o,m,s,c,p,u,g,_,b,$,w,y,Z,v,S,q,V,j,H,W,F,ue,i,se,Ce,ae,a,k,J,le,ne,Se,Oe,Me,Pe,Ve,Fe,Ge,qe,He,We,Xe,Je]}class tl extends rt{constructor(e){super(),st(this,e,Bt,It,_t,{show_label:2,label:3,value:0,columns:4,rows:5,height:6,preview:26,allow_preview:7,object_fit:8,show_share_button:9,show_download_button:10,i18n:11,selected_index:1,interactive:12,_fetch:27,mode:13,show_fullscreen_button:14,display_icon_button_wrapper_top_corner:15},null,[-1,-1])}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),z()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),z()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get columns(){return this.$$.ctx[4]}set columns(e){this.$$set({columns:e}),z()}get rows(){return this.$$.ctx[5]}set rows(e){this.$$set({rows:e}),z()}get height(){return this.$$.ctx[6]}set height(e){this.$$set({height:e}),z()}get preview(){return this.$$.ctx[26]}set preview(e){this.$$set({preview:e}),z()}get allow_preview(){return this.$$.ctx[7]}set allow_preview(e){this.$$set({allow_preview:e}),z()}get object_fit(){return this.$$.ctx[8]}set object_fit(e){this.$$set({object_fit:e}),z()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),z()}get show_download_button(){return this.$$.ctx[10]}set show_download_button(e){this.$$set({show_download_button:e}),z()}get i18n(){return this.$$.ctx[11]}set i18n(e){this.$$set({i18n:e}),z()}get selected_index(){return this.$$.ctx[1]}set selected_index(e){this.$$set({selected_index:e}),z()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),z()}get _fetch(){return this.$$.ctx[27]}set _fetch(e){this.$$set({_fetch:e}),z()}get mode(){return this.$$.ctx[13]}set mode(e){this.$$set({mode:e}),z()}get show_fullscreen_button(){return this.$$.ctx[14]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),z()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[15]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),z()}}export{tl as default};
//# sourceMappingURL=Gallery-CqKzMhy6.js.map
