{"version": 3, "file": "context-TgWPFwN2.js", "sources": ["../../../../js/wasm/svelte/context.ts"], "sourcesContent": ["import { setContext, getContext } from \"svelte\";\nimport type { WorkerProxy } from \"../src/worker-proxy\";\n\nconst WORKER_PROXY_CONTEXT_KEY = \"WORKER_PROXY_CONTEXT_KEY\";\n\nexport function setWorkerProxyContext(workerProxy: WorkerProxy): void {\n\tsetContext(WORKER_PROXY_CONTEXT_KEY, workerProxy);\n}\n\nexport function getWorkerProxyContext(): WorkerProxy | undefined {\n\treturn getContext(WORKER_PROXY_CONTEXT_KEY);\n}\n"], "names": ["setContext", "WORKER_PROXY_CONTEXT_KEY", "setWorkerProxyContext", "workerProxy", "getWorkerProxyContext", "getContext"], "mappings": "AAAA,KAAA,CAAA,WAAAA,gBAAuC,OAAA,2BAGjCC,EAA2B,2BAE1B,SAASC,EAAsBC,EAAgC,CACrEH,EAAWC,EAA0BE,CAAW,CACjD,CAEO,SAASC,GAAiD,CAChE,OAAOC,EAAWJ,CAAwB,CAC3C"}