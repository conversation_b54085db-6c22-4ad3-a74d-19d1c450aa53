import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{I as M}from"./IconButton-B3BI2i6T.js";const{SvelteComponent:z,append:E,attr:c,detach:F,init:j,insert:V,noop:m,safe_not_equal:H,svg_element:g}=window.__gradio__svelte__internal;function N(s){let e,n;return{c(){e=g("svg"),n=g("path"),c(n,"d","M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"),c(e,"xmlns","http://www.w3.org/2000/svg"),c(e,"viewBox","0 0 24 24"),c(e,"fill","none"),c(e,"stroke","currentColor"),c(e,"stroke-width","2"),c(e,"stroke-linecap","round"),c(e,"stroke-linejoin","round"),c(e,"class","feather feather-maximize"),c(e,"width","100%"),c(e,"height","100%")},m(t,o){V(t,e,o),E(e,n)},p:m,i:m,o:m,d(t){t&&F(e)}}}class D extends z{constructor(e){super(),j(this,e,null,N,H,{})}}const{SvelteComponent:L,append:d,attr:l,detach:A,init:G,insert:J,noop:h,safe_not_equal:K,svg_element:p}=window.__gradio__svelte__internal;function O(s){let e,n,t,o;return{c(){e=p("svg"),n=p("rect"),t=p("circle"),o=p("polyline"),l(n,"x","3"),l(n,"y","3"),l(n,"width","18"),l(n,"height","18"),l(n,"rx","2"),l(n,"ry","2"),l(t,"cx","8.5"),l(t,"cy","8.5"),l(t,"r","1.5"),l(o,"points","21 15 16 10 5 21"),l(e,"xmlns","http://www.w3.org/2000/svg"),l(e,"width","100%"),l(e,"height","100%"),l(e,"viewBox","0 0 24 24"),l(e,"fill","none"),l(e,"stroke","currentColor"),l(e,"stroke-width","1.5"),l(e,"stroke-linecap","round"),l(e,"stroke-linejoin","round"),l(e,"class","feather feather-image")},m(r,i){J(r,e,i),d(e,n),d(e,t),d(e,o)},p:h,i:h,o:h,d(r){r&&A(e)}}}class _e extends L{constructor(e){super(),G(this,e,null,O,K,{})}}const{SvelteComponent:P,append:Q,attr:u,detach:R,init:T,insert:U,noop:$,safe_not_equal:W,svg_element:w}=window.__gradio__svelte__internal;function X(s){let e,n;return{c(){e=w("svg"),n=w("path"),u(n,"d","M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"),u(e,"xmlns","http://www.w3.org/2000/svg"),u(e,"viewBox","0 0 24 24"),u(e,"fill","none"),u(e,"stroke","currentColor"),u(e,"stroke-width","2"),u(e,"stroke-linecap","round"),u(e,"stroke-linejoin","round"),u(e,"class","feather feather-minimize"),u(e,"width","100%"),u(e,"height","100%")},m(t,o){U(t,e,o),Q(e,n)},p:$,i:$,o:$,d(t){t&&R(e)}}}class Y extends P{constructor(e){super(),T(this,e,null,X,W,{})}}const{SvelteComponent:Z,check_outros:v,create_component:S,destroy_component:I,detach:k,empty:ee,flush:te,group_outros:x,init:ne,insert:C,mount_component:y,noop:B,safe_not_equal:oe,space:re,transition_in:f,transition_out:_}=window.__gradio__svelte__internal,{onMount:ie,createEventDispatcher:le}=window.__gradio__svelte__internal;function b(s){let e,n;return e=new M({props:{Icon:D,label:"View in full screen"}}),e.$on("click",s[1]),{c(){S(e.$$.fragment)},m(t,o){y(e,t,o),n=!0},p:B,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){_(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function q(s){let e,n;return e=new M({props:{Icon:Y,label:"Exit full screen"}}),e.$on("click",s[1]),{c(){S(e.$$.fragment)},m(t,o){y(e,t,o),n=!0},p:B,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){_(e.$$.fragment,t),n=!1},d(t){I(e,t)}}}function se(s){let e,n,t,o=!s[0]&&b(s),r=s[0]&&q(s);return{c(){o&&o.c(),e=re(),r&&r.c(),n=ee()},m(i,a){o&&o.m(i,a),C(i,e,a),r&&r.m(i,a),C(i,n,a),t=!0},p(i,[a]){i[0]?o&&(x(),_(o,1,1,()=>{o=null}),v()):o?(o.p(i,a),a&1&&f(o,1)):(o=b(i),o.c(),f(o,1),o.m(e.parentNode,e)),i[0]?r?(r.p(i,a),a&1&&f(r,1)):(r=q(i),r.c(),f(r,1),r.m(n.parentNode,n)):r&&(x(),_(r,1,1,()=>{r=null}),v())},i(i){t||(f(o),f(r),t=!0)},o(i){_(o),_(r),t=!1},d(i){i&&(k(e),k(n)),o&&o.d(i),r&&r.d(i)}}}function ae(s,e,n){let{container:t=void 0}=e;const o=le();let r=!1;ie(()=>{document.addEventListener("fullscreenchange",()=>{n(0,r=!!document.fullscreenElement),o("fullscreenchange",r)})});const i=async()=>{t&&(r?(await document.exitFullscreen(),n(0,r=!r)):await t.requestFullscreen())};return s.$$set=a=>{"container"in a&&n(2,t=a.container)},[r,i,t]}class pe extends Z{constructor(e){super(),ne(this,e,ae,se,oe,{container:2})}get container(){return this.$$.ctx[2]}set container(e){this.$$set({container:e}),te()}}export{pe as F,_e as I};
//# sourceMappingURL=FullscreenButton-BWloBRD_.js.map
