import"./index-BkoKOheB.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{B as ht}from"./BlockTitle-IUerRYnJ.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{D as dt}from"./DropdownArrow-CRmyeEUc.js";import{a as Qe}from"./index-C30MtwUc.js";import{B as St}from"./Block-CB3nIXHA.js";import{S as yt}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{default as ln}from"./Example-BFOhuzTJ.js";import"./svelte/svelte.js";import"./Info-QztxykAE.js";import"./MarkdownCode-DYQlapxH.js";import"./prism-python-B8dcvKZU.js";import"./IconButton-B3BI2i6T.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:At,append:Et,attr:ae,detach:Dt,init:Ct,insert:qt,noop:Je,safe_not_equal:Bt,svg_element:Ve}=window.__gradio__svelte__internal;function Nt(l){let e,t;return{c(){e=Ve("svg"),t=Ve("path"),ae(t,"d","M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"),ae(e,"xmlns","http://www.w3.org/2000/svg"),ae(e,"viewBox","0 0 24 24"),ae(e,"width","100%"),ae(e,"height","100%")},m(s,u){qt(s,e,u),Et(e,t)},p:Je,i:Je,o:Je,d(s){s&&Dt(e)}}}class mt extends At{constructor(e){super(),Ct(this,e,null,Nt,Bt,{})}}const{SvelteComponent:Jt,add_render_callback:gt,append:ve,attr:I,binding_callbacks:We,check_outros:Tt,create_bidirectional_transition:Xe,destroy_each:Ut,detach:me,element:Oe,empty:zt,ensure_array_like:Ye,flush:ee,group_outros:Mt,init:It,insert:ge,listen:ze,prevent_default:Lt,run_all:Rt,safe_not_equal:Ht,set_data:jt,set_style:G,space:Me,text:Ft,toggle_class:F,transition_in:Te,transition_out:Ze}=window.__gradio__svelte__internal,{createEventDispatcher:Gt}=window.__gradio__svelte__internal;function xe(l,e,t){const s=l.slice();return s[24]=e[t],s}function $e(l){let e,t,s,u,o,_=Ye(l[1]),i=[];for(let n=0;n<_.length;n+=1)i[n]=et(xe(l,_,n));return{c(){e=Oe("ul");for(let n=0;n<i.length;n+=1)i[n].c();I(e,"class","options svelte-y6qw75"),I(e,"role","listbox"),G(e,"top",l[9]),G(e,"bottom",l[10]),G(e,"max-height",`calc(${l[11]}px - var(--window-padding))`),G(e,"width",l[8]+"px")},m(n,f){ge(n,e,f);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(e,null);l[21](e),s=!0,u||(o=ze(e,"mousedown",Lt(l[20])),u=!0)},p(n,f){if(f&307){_=Ye(n[1]);let r;for(r=0;r<_.length;r+=1){const p=xe(n,_,r);i[r]?i[r].p(p,f):(i[r]=et(p),i[r].c(),i[r].m(e,null))}for(;r<i.length;r+=1)i[r].d(1);i.length=_.length}f&512&&G(e,"top",n[9]),f&1024&&G(e,"bottom",n[10]),f&2048&&G(e,"max-height",`calc(${n[11]}px - var(--window-padding))`),f&256&&G(e,"width",n[8]+"px")},i(n){s||(n&&gt(()=>{s&&(t||(t=Xe(e,Qe,{duration:200,y:5},!0)),t.run(1))}),s=!0)},o(n){n&&(t||(t=Xe(e,Qe,{duration:200,y:5},!1)),t.run(0)),s=!1},d(n){n&&me(e),Ut(i,n),l[21](null),n&&t&&t.end(),u=!1,o()}}}function et(l){let e,t,s,u=l[0][l[24]][0]+"",o,_,i,n,f;return{c(){e=Oe("li"),t=Oe("span"),t.textContent="✓",s=Me(),o=Ft(u),_=Me(),I(t,"class","inner-item svelte-y6qw75"),F(t,"hide",!l[4].includes(l[24])),I(e,"class","item svelte-y6qw75"),I(e,"data-index",i=l[24]),I(e,"aria-label",n=l[0][l[24]][0]),I(e,"data-testid","dropdown-option"),I(e,"role","option"),I(e,"aria-selected",f=l[4].includes(l[24])),F(e,"selected",l[4].includes(l[24])),F(e,"active",l[24]===l[5]),F(e,"bg-gray-100",l[24]===l[5]),F(e,"dark:bg-gray-600",l[24]===l[5]),G(e,"width",l[8]+"px")},m(r,p){ge(r,e,p),ve(e,t),ve(e,s),ve(e,o),ve(e,_)},p(r,p){p&18&&F(t,"hide",!r[4].includes(r[24])),p&3&&u!==(u=r[0][r[24]][0]+"")&&jt(o,u),p&2&&i!==(i=r[24])&&I(e,"data-index",i),p&3&&n!==(n=r[0][r[24]][0])&&I(e,"aria-label",n),p&18&&f!==(f=r[4].includes(r[24]))&&I(e,"aria-selected",f),p&18&&F(e,"selected",r[4].includes(r[24])),p&34&&F(e,"active",r[24]===r[5]),p&34&&F(e,"bg-gray-100",r[24]===r[5]),p&34&&F(e,"dark:bg-gray-600",r[24]===r[5]),p&256&&G(e,"width",r[8]+"px")},d(r){r&&me(e)}}}function Kt(l){let e,t,s,u,o;gt(l[18]);let _=l[2]&&!l[3]&&$e(l);return{c(){e=Oe("div"),t=Me(),_&&_.c(),s=zt(),I(e,"class","reference")},m(i,n){ge(i,e,n),l[19](e),ge(i,t,n),_&&_.m(i,n),ge(i,s,n),u||(o=[ze(window,"scroll",l[13]),ze(window,"resize",l[18])],u=!0)},p(i,[n]){i[2]&&!i[3]?_?(_.p(i,n),n&12&&Te(_,1)):(_=$e(i),_.c(),Te(_,1),_.m(s.parentNode,s)):_&&(Mt(),Ze(_,1,1,()=>{_=null}),Tt())},i(i){Te(_)},o(i){Ze(_)},d(i){i&&(me(e),me(t),me(s)),l[19](null),_&&_.d(i),u=!1,Rt(o)}}}function Pt(l,e,t){let{choices:s}=e,{filtered_indices:u}=e,{show_options:o=!1}=e,{disabled:_=!1}=e,{selected_indices:i=[]}=e,{active_index:n=null}=e,f,r,p,b,k,D,A,m,h,q;function g(){const{top:y,bottom:U}=k.getBoundingClientRect();t(15,f=y),t(16,r=q-U)}let c=null;function d(){o&&(c!==null&&clearTimeout(c),c=setTimeout(()=>{g(),c=null},10))}const B=Gt();function C(){t(12,q=window.innerHeight)}function S(y){We[y?"unshift":"push"](()=>{k=y,t(6,k)})}const v=y=>B("change",y);function E(y){We[y?"unshift":"push"](()=>{D=y,t(7,D)})}return l.$$set=y=>{"choices"in y&&t(0,s=y.choices),"filtered_indices"in y&&t(1,u=y.filtered_indices),"show_options"in y&&t(2,o=y.show_options),"disabled"in y&&t(3,_=y.disabled),"selected_indices"in y&&t(4,i=y.selected_indices),"active_index"in y&&t(5,n=y.active_index)},l.$$.update=()=>{if(l.$$.dirty&229588){if(o&&k){if(D&&i.length>0){let U=D.querySelectorAll("li");for(const X of Array.from(U))if(X.getAttribute("data-index")===i[0].toString()){D?.scrollTo?.(0,X.offsetTop);break}}g();const y=k.parentElement?.getBoundingClientRect();t(17,p=y?.height||0),t(8,b=y?.width||0)}r>f?(t(9,A=`${f}px`),t(11,h=r),t(10,m=null)):(t(10,m=`${r+p}px`),t(11,h=f-p),t(9,A=null))}},[s,u,o,_,i,n,k,D,b,A,m,h,q,d,B,f,r,p,C,S,v,E]}class bt extends Jt{constructor(e){super(),It(this,e,Pt,Kt,Ht,{choices:0,filtered_indices:1,show_options:2,disabled:3,selected_indices:4,active_index:5})}get choices(){return this.$$.ctx[0]}set choices(e){this.$$set({choices:e}),ee()}get filtered_indices(){return this.$$.ctx[1]}set filtered_indices(e){this.$$set({filtered_indices:e}),ee()}get show_options(){return this.$$.ctx[2]}set show_options(e){this.$$set({show_options:e}),ee()}get disabled(){return this.$$.ctx[3]}set disabled(e){this.$$set({disabled:e}),ee()}get selected_indices(){return this.$$.ctx[4]}set selected_indices(e){this.$$set({selected_indices:e}),ee()}get active_index(){return this.$$.ctx[5]}set active_index(e){this.$$set({active_index:e}),ee()}}function Qt(l,e){return(l%e+e)%e}function Ie(l,e){return l.reduce((t,s,u)=>((!e||s[0].toLowerCase().includes(e.toLowerCase()))&&t.push(u),t),[])}function wt(l,e,t){l("change",e),t||l("input")}function kt(l,e,t){if(l.key==="Escape")return[!1,e];if((l.key==="ArrowDown"||l.key==="ArrowUp")&&t.length>=0)if(e===null)e=l.key==="ArrowDown"?t[0]:t[t.length-1];else{const s=t.indexOf(e),u=l.key==="ArrowUp"?-1:1;e=t[Qt(s+u,t.length)]}return[!0,e]}const{SvelteComponent:Vt,append:K,attr:J,binding_callbacks:Wt,check_outros:Se,create_component:be,destroy_component:we,destroy_each:Xt,detach:V,element:P,ensure_array_like:tt,flush:z,group_outros:ye,init:Yt,insert:W,listen:Q,mount_component:ke,prevent_default:lt,run_all:je,safe_not_equal:Zt,set_data:Fe,set_input_value:nt,space:ie,text:Ge,toggle_class:te,transition_in:T,transition_out:L}=window.__gradio__svelte__internal,{afterUpdate:xt,createEventDispatcher:$t}=window.__gradio__svelte__internal;function st(l,e,t){const s=l.slice();return s[41]=e[t],s}function el(l){let e;return{c(){e=Ge(l[0])},m(t,s){W(t,e,s)},p(t,s){s[0]&1&&Fe(e,t[0])},d(t){t&&V(e)}}}function tl(l){let e=l[41]+"",t;return{c(){t=Ge(e)},m(s,u){W(s,t,u)},p(s,u){u[0]&8192&&e!==(e=s[41]+"")&&Fe(t,e)},d(s){s&&V(t)}}}function ll(l){let e=l[16][l[41]]+"",t;return{c(){t=Ge(e)},m(s,u){W(s,t,u)},p(s,u){u[0]&73728&&e!==(e=s[16][s[41]]+"")&&Fe(t,e)},d(s){s&&V(t)}}}function it(l){let e,t,s,u,o,_;t=new mt({});function i(){return l[32](l[41])}function n(...f){return l[33](l[41],...f)}return{c(){e=P("div"),be(t.$$.fragment),J(e,"class","token-remove svelte-1scun43"),J(e,"role","button"),J(e,"tabindex","0"),J(e,"title",s=l[9]("common.remove")+" "+l[41])},m(f,r){W(f,e,r),ke(t,e,null),u=!0,o||(_=[Q(e,"click",lt(i)),Q(e,"keydown",lt(n))],o=!0)},p(f,r){l=f,(!u||r[0]&8704&&s!==(s=l[9]("common.remove")+" "+l[41]))&&J(e,"title",s)},i(f){u||(T(t.$$.fragment,f),u=!0)},o(f){L(t.$$.fragment,f),u=!1},d(f){f&&V(e),we(t),o=!1,je(_)}}}function ot(l){let e,t,s,u;function o(f,r){return typeof f[41]=="number"?ll:tl}let _=o(l),i=_(l),n=!l[4]&&it(l);return{c(){e=P("div"),t=P("span"),i.c(),s=ie(),n&&n.c(),J(t,"class","svelte-1scun43"),J(e,"class","token svelte-1scun43")},m(f,r){W(f,e,r),K(e,t),i.m(t,null),K(e,s),n&&n.m(e,null),u=!0},p(f,r){_===(_=o(f))&&i?i.p(f,r):(i.d(1),i=_(f),i&&(i.c(),i.m(t,null))),f[4]?n&&(ye(),L(n,1,1,()=>{n=null}),Se()):n?(n.p(f,r),r[0]&16&&T(n,1)):(n=it(f),n.c(),T(n,1),n.m(e,null))},i(f){u||(T(n),u=!0)},o(f){L(n),u=!1},d(f){f&&V(e),i.d(),n&&n.d()}}}function ut(l){let e,t,s,u,o=l[13].length>0&&rt(l);return s=new dt({}),{c(){o&&o.c(),e=ie(),t=P("span"),be(s.$$.fragment),J(t,"class","icon-wrap svelte-1scun43")},m(_,i){o&&o.m(_,i),W(_,e,i),W(_,t,i),ke(s,t,null),u=!0},p(_,i){_[13].length>0?o?(o.p(_,i),i[0]&8192&&T(o,1)):(o=rt(_),o.c(),T(o,1),o.m(e.parentNode,e)):o&&(ye(),L(o,1,1,()=>{o=null}),Se())},i(_){u||(T(o),T(s.$$.fragment,_),u=!0)},o(_){L(o),L(s.$$.fragment,_),u=!1},d(_){_&&(V(e),V(t)),o&&o.d(_),we(s)}}}function rt(l){let e,t,s,u,o,_;return t=new mt({}),{c(){e=P("div"),be(t.$$.fragment),J(e,"role","button"),J(e,"tabindex","0"),J(e,"class","token-remove remove-all svelte-1scun43"),J(e,"title",s=l[9]("common.clear"))},m(i,n){W(i,e,n),ke(t,e,null),u=!0,o||(_=[Q(e,"click",l[22]),Q(e,"keydown",l[37])],o=!0)},p(i,n){(!u||n[0]&512&&s!==(s=i[9]("common.clear")))&&J(e,"title",s)},i(i){u||(T(t.$$.fragment,i),u=!0)},o(i){L(t.$$.fragment,i),u=!1},d(i){i&&V(e),we(t),o=!1,je(_)}}}function nl(l){let e,t,s,u,o,_,i,n,f,r,p,b,k,D,A;t=new ht({props:{root:l[10],show_label:l[5],info:l[1],$$slots:{default:[el]},$$scope:{ctx:l}}});let m=tt(l[13]),h=[];for(let c=0;c<m.length;c+=1)h[c]=ot(st(l,m,c));const q=c=>L(h[c],1,1,()=>{h[c]=null});let g=!l[4]&&ut(l);return b=new bt({props:{show_options:l[15],choices:l[3],filtered_indices:l[12],disabled:l[4],selected_indices:l[13],active_index:l[17]}}),b.$on("change",l[21]),{c(){e=P("label"),be(t.$$.fragment),s=ie(),u=P("div"),o=P("div");for(let c=0;c<h.length;c+=1)h[c].c();_=ie(),i=P("div"),n=P("input"),r=ie(),g&&g.c(),p=ie(),be(b.$$.fragment),J(n,"class","border-none svelte-1scun43"),n.disabled=l[4],J(n,"autocomplete","off"),n.readOnly=f=!l[8],te(n,"subdued",!l[16].includes(l[11])&&!l[7]||l[13].length===l[2]),J(i,"class","secondary-wrap svelte-1scun43"),J(o,"class","wrap-inner svelte-1scun43"),te(o,"show_options",l[15]),J(u,"class","wrap svelte-1scun43"),J(e,"class","svelte-1scun43"),te(e,"container",l[6])},m(c,d){W(c,e,d),ke(t,e,null),K(e,s),K(e,u),K(u,o);for(let B=0;B<h.length;B+=1)h[B]&&h[B].m(o,null);K(o,_),K(o,i),K(i,n),nt(n,l[11]),l[35](n),K(i,r),g&&g.m(i,null),K(u,p),ke(b,u,null),k=!0,D||(A=[Q(n,"input",l[34]),Q(n,"keydown",l[24]),Q(n,"keyup",l[36]),Q(n,"blur",l[19]),Q(n,"focus",l[23])],D=!0)},p(c,d){const B={};if(d[0]&1024&&(B.root=c[10]),d[0]&32&&(B.show_label=c[5]),d[0]&2&&(B.info=c[1]),d[0]&1|d[1]&8192&&(B.$$scope={dirty:d,ctx:c}),t.$set(B),d[0]&1122832){m=tt(c[13]);let S;for(S=0;S<m.length;S+=1){const v=st(c,m,S);h[S]?(h[S].p(v,d),T(h[S],1)):(h[S]=ot(v),h[S].c(),T(h[S],1),h[S].m(o,_))}for(ye(),S=m.length;S<h.length;S+=1)q(S);Se()}(!k||d[0]&16)&&(n.disabled=c[4]),(!k||d[0]&256&&f!==(f=!c[8]))&&(n.readOnly=f),d[0]&2048&&n.value!==c[11]&&nt(n,c[11]),(!k||d[0]&75908)&&te(n,"subdued",!c[16].includes(c[11])&&!c[7]||c[13].length===c[2]),c[4]?g&&(ye(),L(g,1,1,()=>{g=null}),Se()):g?(g.p(c,d),d[0]&16&&T(g,1)):(g=ut(c),g.c(),T(g,1),g.m(i,null)),(!k||d[0]&32768)&&te(o,"show_options",c[15]);const C={};d[0]&32768&&(C.show_options=c[15]),d[0]&8&&(C.choices=c[3]),d[0]&4096&&(C.filtered_indices=c[12]),d[0]&16&&(C.disabled=c[4]),d[0]&8192&&(C.selected_indices=c[13]),d[0]&131072&&(C.active_index=c[17]),b.$set(C),(!k||d[0]&64)&&te(e,"container",c[6])},i(c){if(!k){T(t.$$.fragment,c);for(let d=0;d<m.length;d+=1)T(h[d]);T(g),T(b.$$.fragment,c),k=!0}},o(c){L(t.$$.fragment,c),h=h.filter(Boolean);for(let d=0;d<h.length;d+=1)L(h[d]);L(g),L(b.$$.fragment,c),k=!1},d(c){c&&V(e),we(t),Xt(h,c),l[35](null),g&&g.d(),we(b),D=!1,je(A)}}}function sl(l,e,t){let{label:s}=e,{info:u=void 0}=e,{value:o=[]}=e,_=[],{value_is_output:i=!1}=e,{max_choices:n=null}=e,{choices:f}=e,r,{disabled:p=!1}=e,{show_label:b}=e,{container:k=!0}=e,{allow_custom_value:D=!1}=e,{filterable:A=!0}=e,{i18n:m}=e,{root:h}=e,q,g="",c="",d=!1,B,C,S=[],v=null,E=[],y=[];const U=$t();Array.isArray(o)&&o.forEach(a=>{const R=f.map(Ne=>Ne[1]).indexOf(a);R!==-1?E.push(R):E.push(a)});function X(){D||t(11,g=""),D&&g!==""&&(Y(g),t(11,g="")),t(15,d=!1),t(17,v=null),U("blur")}function j(a){t(13,E=E.filter(R=>R!==a)),U("select",{index:typeof a=="number"?a:-1,value:typeof a=="number"?C[a]:a,selected:!1})}function Y(a){(n===null||E.length<n)&&(t(13,E=[...E,a]),U("select",{index:typeof a=="number"?a:-1,value:typeof a=="number"?C[a]:a,selected:!0})),E.length===n&&(t(15,d=!1),t(17,v=null),q.blur())}function re(a){const R=parseInt(a.detail.target.dataset.index);x(R)}function x(a){E.includes(a)?j(a):Y(a),t(11,g="")}function $(a){t(13,E=[]),t(11,g=""),a.preventDefault()}function fe(a){t(12,S=f.map((R,Ne)=>Ne)),(n===null||E.length<n)&&t(15,d=!0),U("focus")}function _e(a){t(15,[d,v]=kt(a,v,S),d,(t(17,v),t(3,f),t(28,r),t(11,g),t(29,c),t(7,D),t(12,S))),a.key==="Enter"&&(v!==null?x(v):D&&(Y(g),t(11,g=""))),a.key==="Backspace"&&g===""&&t(13,E=[...E.slice(0,-1)]),E.length===n&&(t(15,d=!1),t(17,v=null))}function ce(){o===void 0?t(13,E=[]):Array.isArray(o)&&t(13,E=o.map(a=>{const R=C.indexOf(a);if(R!==-1)return R;if(D)return a}).filter(a=>a!==void 0))}xt(()=>{t(26,i=!1)});const O=a=>j(a),pe=(a,R)=>{R.key==="Enter"&&j(a)};function w(){g=this.value,t(11,g)}function pt(a){Wt[a?"unshift":"push"](()=>{q=a,t(14,q)})}const vt=a=>U("key_up",{key:a.key,input_value:g}),Ot=a=>{a.key==="Enter"&&$(a)};return l.$$set=a=>{"label"in a&&t(0,s=a.label),"info"in a&&t(1,u=a.info),"value"in a&&t(25,o=a.value),"value_is_output"in a&&t(26,i=a.value_is_output),"max_choices"in a&&t(2,n=a.max_choices),"choices"in a&&t(3,f=a.choices),"disabled"in a&&t(4,p=a.disabled),"show_label"in a&&t(5,b=a.show_label),"container"in a&&t(6,k=a.container),"allow_custom_value"in a&&t(7,D=a.allow_custom_value),"filterable"in a&&t(8,A=a.filterable),"i18n"in a&&t(9,m=a.i18n),"root"in a&&t(10,h=a.root)},l.$$.update=()=>{l.$$.dirty[0]&8&&(t(16,B=f.map(a=>a[0])),t(30,C=f.map(a=>a[1]))),l.$$.dirty[0]&805312648&&(f!==r||g!==c)&&(t(12,S=Ie(f,g)),t(28,r=f),t(29,c=g),D||t(17,v=S[0])),l.$$.dirty[0]&1073750016|l.$$.dirty[1]&1&&JSON.stringify(E)!=JSON.stringify(y)&&(t(25,o=E.map(a=>typeof a=="number"?C[a]:a)),t(31,y=E.slice())),l.$$.dirty[0]&234881024&&JSON.stringify(o)!=JSON.stringify(_)&&(wt(U,o,i),t(27,_=Array.isArray(o)?o.slice():o)),l.$$.dirty[0]&33554432&&ce()},[s,u,n,f,p,b,k,D,A,m,h,g,S,E,q,d,B,v,U,X,j,re,$,fe,_e,o,i,_,r,c,C,y,O,pe,w,pt,vt,Ot]}class il extends Vt{constructor(e){super(),Yt(this,e,sl,nl,Zt,{label:0,info:1,value:25,value_is_output:26,max_choices:2,choices:3,disabled:4,show_label:5,container:6,allow_custom_value:7,filterable:8,i18n:9,root:10},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),z()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),z()}get value(){return this.$$.ctx[25]}set value(e){this.$$set({value:e}),z()}get value_is_output(){return this.$$.ctx[26]}set value_is_output(e){this.$$set({value_is_output:e}),z()}get max_choices(){return this.$$.ctx[2]}set max_choices(e){this.$$set({max_choices:e}),z()}get choices(){return this.$$.ctx[3]}set choices(e){this.$$set({choices:e}),z()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),z()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),z()}get container(){return this.$$.ctx[6]}set container(e){this.$$set({container:e}),z()}get allow_custom_value(){return this.$$.ctx[7]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),z()}get filterable(){return this.$$.ctx[8]}set filterable(e){this.$$set({filterable:e}),z()}get i18n(){return this.$$.ctx[9]}set i18n(e){this.$$set({i18n:e}),z()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),z()}}const ol=il,{SvelteComponent:ul,append:Z,attr:M,binding_callbacks:rl,check_outros:fl,create_component:Le,destroy_component:Re,detach:Ke,element:ne,flush:H,group_outros:_l,init:cl,insert:Pe,listen:he,mount_component:He,run_all:al,safe_not_equal:hl,set_data:dl,set_input_value:ft,space:Ue,text:ml,toggle_class:le,transition_in:se,transition_out:de}=window.__gradio__svelte__internal,{createEventDispatcher:gl,afterUpdate:bl}=window.__gradio__svelte__internal;function wl(l){let e;return{c(){e=ml(l[0])},m(t,s){Pe(t,e,s)},p(t,s){s[0]&1&&dl(e,t[0])},d(t){t&&Ke(e)}}}function _t(l){let e,t,s;return t=new dt({}),{c(){e=ne("div"),Le(t.$$.fragment),M(e,"class","icon-wrap svelte-1hfxrpf")},m(u,o){Pe(u,e,o),He(t,e,null),s=!0},i(u){s||(se(t.$$.fragment,u),s=!0)},o(u){de(t.$$.fragment,u),s=!1},d(u){u&&Ke(e),Re(t)}}}function kl(l){let e,t,s,u,o,_,i,n,f,r,p,b,k,D;t=new ht({props:{root:l[8],show_label:l[4],info:l[1],$$slots:{default:[wl]},$$scope:{ctx:l}}});let A=!l[3]&&_t();return p=new bt({props:{show_options:l[13],choices:l[2],filtered_indices:l[11],disabled:l[3],selected_indices:l[12]===null?[]:[l[12]],active_index:l[15]}}),p.$on("change",l[17]),{c(){e=ne("div"),Le(t.$$.fragment),s=Ue(),u=ne("div"),o=ne("div"),_=ne("div"),i=ne("input"),f=Ue(),A&&A.c(),r=Ue(),Le(p.$$.fragment),M(i,"role","listbox"),M(i,"aria-controls","dropdown-options"),M(i,"aria-expanded",l[13]),M(i,"aria-label",l[0]),M(i,"class","border-none svelte-1hfxrpf"),i.disabled=l[3],M(i,"autocomplete","off"),i.readOnly=n=!l[7],le(i,"subdued",!l[14].includes(l[10])&&!l[6]),M(_,"class","secondary-wrap svelte-1hfxrpf"),M(o,"class","wrap-inner svelte-1hfxrpf"),le(o,"show_options",l[13]),M(u,"class","wrap svelte-1hfxrpf"),M(e,"class","svelte-1hfxrpf"),le(e,"container",l[5])},m(m,h){Pe(m,e,h),He(t,e,null),Z(e,s),Z(e,u),Z(u,o),Z(o,_),Z(_,i),ft(i,l[10]),l[30](i),Z(_,f),A&&A.m(_,null),Z(u,r),He(p,u,null),b=!0,k||(D=[he(i,"input",l[29]),he(i,"keydown",l[20]),he(i,"keyup",l[31]),he(i,"blur",l[19]),he(i,"focus",l[18])],k=!0)},p(m,h){const q={};h[0]&256&&(q.root=m[8]),h[0]&16&&(q.show_label=m[4]),h[0]&2&&(q.info=m[1]),h[0]&1|h[1]&16&&(q.$$scope={dirty:h,ctx:m}),t.$set(q),(!b||h[0]&8192)&&M(i,"aria-expanded",m[13]),(!b||h[0]&1)&&M(i,"aria-label",m[0]),(!b||h[0]&8)&&(i.disabled=m[3]),(!b||h[0]&128&&n!==(n=!m[7]))&&(i.readOnly=n),h[0]&1024&&i.value!==m[10]&&ft(i,m[10]),(!b||h[0]&17472)&&le(i,"subdued",!m[14].includes(m[10])&&!m[6]),m[3]?A&&(_l(),de(A,1,1,()=>{A=null}),fl()):A?h[0]&8&&se(A,1):(A=_t(),A.c(),se(A,1),A.m(_,null)),(!b||h[0]&8192)&&le(o,"show_options",m[13]);const g={};h[0]&8192&&(g.show_options=m[13]),h[0]&4&&(g.choices=m[2]),h[0]&2048&&(g.filtered_indices=m[11]),h[0]&8&&(g.disabled=m[3]),h[0]&4096&&(g.selected_indices=m[12]===null?[]:[m[12]]),h[0]&32768&&(g.active_index=m[15]),p.$set(g),(!b||h[0]&32)&&le(e,"container",m[5])},i(m){b||(se(t.$$.fragment,m),se(A),se(p.$$.fragment,m),b=!0)},o(m){de(t.$$.fragment,m),de(A),de(p.$$.fragment,m),b=!1},d(m){m&&Ke(e),Re(t),l[30](null),A&&A.d(),Re(p),k=!1,al(D)}}}function pl(l,e,t){let{label:s}=e,{info:u=void 0}=e,{value:o=void 0}=e,_,{value_is_output:i=!1}=e,{choices:n}=e,f,{disabled:r=!1}=e,{show_label:p}=e,{container:b=!0}=e,{allow_custom_value:k=!1}=e,{filterable:D=!0}=e,{root:A}=e,m,h=!1,q,g,c="",d="",B=!1,C=[],S=null,v=null,E;const y=gl();o&&(E=n.map(O=>O[1]).indexOf(o),v=E,v===-1?(_=o,v=null):([c,_]=n[v],d=c),j());function U(){t(14,q=n.map(O=>O[0])),t(25,g=n.map(O=>O[1]))}const X=typeof window<"u";function j(){U(),o===void 0||Array.isArray(o)&&o.length===0?(t(10,c=""),t(12,v=null)):g.includes(o)?(t(10,c=q[g.indexOf(o)]),t(12,v=g.indexOf(o))):k?(t(10,c=o),t(12,v=null)):(t(10,c=""),t(12,v=null)),t(28,E=v)}function Y(O){if(t(12,v=parseInt(O.detail.target.dataset.index)),isNaN(v)){t(12,v=null);return}t(13,h=!1),t(15,S=null),m.blur()}function re(O){t(11,C=n.map((pe,w)=>w)),t(13,h=!0),y("focus")}function x(){k?t(21,o=c):t(10,c=q[g.indexOf(o)]),t(13,h=!1),t(15,S=null),y("blur")}function $(O){t(13,[h,S]=kt(O,S,C),h,(t(15,S),t(2,n),t(24,f),t(6,k),t(10,c),t(11,C),t(9,m),t(26,d),t(12,v),t(28,E),t(27,B),t(25,g))),O.key==="Enter"&&(S!==null?(t(12,v=S),t(13,h=!1),m.blur(),t(15,S=null)):q.includes(c)?(t(12,v=q.indexOf(c)),t(13,h=!1),t(15,S=null),m.blur()):k&&(t(21,o=c),t(12,v=null),t(13,h=!1),t(15,S=null),m.blur()))}bl(()=>{t(22,i=!1),t(27,B=!0)});function fe(){c=this.value,t(10,c),t(12,v),t(28,E),t(27,B),t(2,n),t(25,g)}function _e(O){rl[O?"unshift":"push"](()=>{m=O,t(9,m)})}const ce=O=>y("key_up",{key:O.key,input_value:c});return l.$$set=O=>{"label"in O&&t(0,s=O.label),"info"in O&&t(1,u=O.info),"value"in O&&t(21,o=O.value),"value_is_output"in O&&t(22,i=O.value_is_output),"choices"in O&&t(2,n=O.choices),"disabled"in O&&t(3,r=O.disabled),"show_label"in O&&t(4,p=O.show_label),"container"in O&&t(5,b=O.container),"allow_custom_value"in O&&t(6,k=O.allow_custom_value),"filterable"in O&&t(7,D=O.filterable),"root"in O&&t(8,A=O.root)},l.$$.update=()=>{l.$$.dirty[0]&436211716&&v!==E&&v!==null&&B&&(t(10,[c,o]=n[v],c,(t(21,o),t(12,v),t(28,E),t(27,B),t(2,n),t(25,g))),t(28,E=v),y("select",{index:v,value:g[v],selected:!0})),l.$$.dirty[0]&14680064&&JSON.stringify(_)!==JSON.stringify(o)&&(j(),wt(y,o,i),t(23,_=o)),l.$$.dirty[0]&4&&U(),l.$$.dirty[0]&16780868&&n!==f&&(k||j(),t(24,f=n),t(11,C=Ie(n,c)),!k&&C.length>0&&t(15,S=C[0]),X&&m===document.activeElement&&t(13,h=!0)),l.$$.dirty[0]&67112004&&c!==d&&(t(11,C=Ie(n,c)),t(26,d=c),!k&&C.length>0&&t(15,S=C[0]))},[s,u,n,r,p,b,k,D,A,m,c,C,v,h,q,S,y,Y,re,x,$,o,i,_,f,g,d,B,E,fe,_e,ce]}class vl extends ul{constructor(e){super(),cl(this,e,pl,kl,hl,{label:0,info:1,value:21,value_is_output:22,choices:2,disabled:3,show_label:4,container:5,allow_custom_value:6,filterable:7,root:8},null,[-1,-1])}get label(){return this.$$.ctx[0]}set label(e){this.$$set({label:e}),H()}get info(){return this.$$.ctx[1]}set info(e){this.$$set({info:e}),H()}get value(){return this.$$.ctx[21]}set value(e){this.$$set({value:e}),H()}get value_is_output(){return this.$$.ctx[22]}set value_is_output(e){this.$$set({value_is_output:e}),H()}get choices(){return this.$$.ctx[2]}set choices(e){this.$$set({choices:e}),H()}get disabled(){return this.$$.ctx[3]}set disabled(e){this.$$set({disabled:e}),H()}get show_label(){return this.$$.ctx[4]}set show_label(e){this.$$set({show_label:e}),H()}get container(){return this.$$.ctx[5]}set container(e){this.$$set({container:e}),H()}get allow_custom_value(){return this.$$.ctx[6]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),H()}get filterable(){return this.$$.ctx[7]}set filterable(e){this.$$set({filterable:e}),H()}get root(){return this.$$.ctx[8]}set root(e){this.$$set({root:e}),H()}}const Ol=vl,{SvelteComponent:Sl,add_flush_callback:Ae,assign:yl,bind:Ee,binding_callbacks:De,check_outros:Al,create_component:Ce,destroy_component:qe,detach:ct,empty:El,flush:N,get_spread_object:Dl,get_spread_update:Cl,group_outros:ql,init:Bl,insert:at,mount_component:Be,safe_not_equal:Nl,space:Jl,transition_in:oe,transition_out:ue}=window.__gradio__svelte__internal;function Tl(l){let e,t,s,u;function o(n){l[29](n)}function _(n){l[30](n)}let i={choices:l[9],label:l[2],root:l[17],info:l[3],show_label:l[10],filterable:l[11],allow_custom_value:l[16],container:l[12],disabled:!l[19]};return l[0]!==void 0&&(i.value=l[0]),l[1]!==void 0&&(i.value_is_output=l[1]),e=new Ol({props:i}),De.push(()=>Ee(e,"value",o)),De.push(()=>Ee(e,"value_is_output",_)),e.$on("change",l[31]),e.$on("input",l[32]),e.$on("select",l[33]),e.$on("blur",l[34]),e.$on("focus",l[35]),e.$on("key_up",l[36]),{c(){Ce(e.$$.fragment)},m(n,f){Be(e,n,f),u=!0},p(n,f){const r={};f[0]&512&&(r.choices=n[9]),f[0]&4&&(r.label=n[2]),f[0]&131072&&(r.root=n[17]),f[0]&8&&(r.info=n[3]),f[0]&1024&&(r.show_label=n[10]),f[0]&2048&&(r.filterable=n[11]),f[0]&65536&&(r.allow_custom_value=n[16]),f[0]&4096&&(r.container=n[12]),f[0]&524288&&(r.disabled=!n[19]),!t&&f[0]&1&&(t=!0,r.value=n[0],Ae(()=>t=!1)),!s&&f[0]&2&&(s=!0,r.value_is_output=n[1],Ae(()=>s=!1)),e.$set(r)},i(n){u||(oe(e.$$.fragment,n),u=!0)},o(n){ue(e.$$.fragment,n),u=!1},d(n){qe(e,n)}}}function Ul(l){let e,t,s,u;function o(n){l[21](n)}function _(n){l[22](n)}let i={choices:l[9],max_choices:l[8],root:l[17],label:l[2],info:l[3],show_label:l[10],allow_custom_value:l[16],filterable:l[11],container:l[12],i18n:l[18].i18n,disabled:!l[19]};return l[0]!==void 0&&(i.value=l[0]),l[1]!==void 0&&(i.value_is_output=l[1]),e=new ol({props:i}),De.push(()=>Ee(e,"value",o)),De.push(()=>Ee(e,"value_is_output",_)),e.$on("change",l[23]),e.$on("input",l[24]),e.$on("select",l[25]),e.$on("blur",l[26]),e.$on("focus",l[27]),e.$on("key_up",l[28]),{c(){Ce(e.$$.fragment)},m(n,f){Be(e,n,f),u=!0},p(n,f){const r={};f[0]&512&&(r.choices=n[9]),f[0]&256&&(r.max_choices=n[8]),f[0]&131072&&(r.root=n[17]),f[0]&4&&(r.label=n[2]),f[0]&8&&(r.info=n[3]),f[0]&1024&&(r.show_label=n[10]),f[0]&65536&&(r.allow_custom_value=n[16]),f[0]&2048&&(r.filterable=n[11]),f[0]&4096&&(r.container=n[12]),f[0]&262144&&(r.i18n=n[18].i18n),f[0]&524288&&(r.disabled=!n[19]),!t&&f[0]&1&&(t=!0,r.value=n[0],Ae(()=>t=!1)),!s&&f[0]&2&&(s=!0,r.value_is_output=n[1],Ae(()=>s=!1)),e.$set(r)},i(n){u||(oe(e.$$.fragment,n),u=!0)},o(n){ue(e.$$.fragment,n),u=!1},d(n){qe(e,n)}}}function zl(l){let e,t,s,u,o,_;const i=[{autoscroll:l[18].autoscroll},{i18n:l[18].i18n},l[15]];let n={};for(let b=0;b<i.length;b+=1)n=yl(n,i[b]);e=new yt({props:n}),e.$on("clear_status",l[20]);const f=[Ul,Tl],r=[];function p(b,k){return b[7]?0:1}return s=p(l),u=r[s]=f[s](l),{c(){Ce(e.$$.fragment),t=Jl(),u.c(),o=El()},m(b,k){Be(e,b,k),at(b,t,k),r[s].m(b,k),at(b,o,k),_=!0},p(b,k){const D=k[0]&294912?Cl(i,[k[0]&262144&&{autoscroll:b[18].autoscroll},k[0]&262144&&{i18n:b[18].i18n},k[0]&32768&&Dl(b[15])]):{};e.$set(D);let A=s;s=p(b),s===A?r[s].p(b,k):(ql(),ue(r[A],1,1,()=>{r[A]=null}),Al(),u=r[s],u?u.p(b,k):(u=r[s]=f[s](b),u.c()),oe(u,1),u.m(o.parentNode,o))},i(b){_||(oe(e.$$.fragment,b),oe(u),_=!0)},o(b){ue(e.$$.fragment,b),ue(u),_=!1},d(b){b&&(ct(t),ct(o)),qe(e,b),r[s].d(b)}}}function Ml(l){let e,t;return e=new St({props:{visible:l[6],elem_id:l[4],elem_classes:l[5],padding:l[12],allow_overflow:!1,scale:l[13],min_width:l[14],$$slots:{default:[zl]},$$scope:{ctx:l}}}),{c(){Ce(e.$$.fragment)},m(s,u){Be(e,s,u),t=!0},p(s,u){const o={};u[0]&64&&(o.visible=s[6]),u[0]&16&&(o.elem_id=s[4]),u[0]&32&&(o.elem_classes=s[5]),u[0]&4096&&(o.padding=s[12]),u[0]&8192&&(o.scale=s[13]),u[0]&16384&&(o.min_width=s[14]),u[0]&1023887|u[1]&64&&(o.$$scope={dirty:u,ctx:s}),e.$set(o)},i(s){t||(oe(e.$$.fragment,s),t=!0)},o(s){ue(e.$$.fragment,s),t=!1},d(s){qe(e,s)}}}function Il(l,e,t){let{label:s="Dropdown"}=e,{info:u=void 0}=e,{elem_id:o=""}=e,{elem_classes:_=[]}=e,{visible:i=!0}=e,{multiselect:n=!1}=e,{value:f=n?[]:void 0}=e,{value_is_output:r=!1}=e,{max_choices:p=null}=e,{choices:b}=e,{show_label:k}=e,{filterable:D}=e,{container:A=!0}=e,{scale:m=null}=e,{min_width:h=void 0}=e,{loading_status:q}=e,{allow_custom_value:g=!1}=e,{root:c}=e,{gradio:d}=e,{interactive:B}=e;const C=()=>d.dispatch("clear_status",q);function S(w){f=w,t(0,f)}function v(w){r=w,t(1,r)}const E=()=>d.dispatch("change"),y=()=>d.dispatch("input"),U=w=>d.dispatch("select",w.detail),X=()=>d.dispatch("blur"),j=()=>d.dispatch("focus"),Y=()=>d.dispatch("key_up");function re(w){f=w,t(0,f)}function x(w){r=w,t(1,r)}const $=()=>d.dispatch("change"),fe=()=>d.dispatch("input"),_e=w=>d.dispatch("select",w.detail),ce=()=>d.dispatch("blur"),O=()=>d.dispatch("focus"),pe=w=>d.dispatch("key_up",w.detail);return l.$$set=w=>{"label"in w&&t(2,s=w.label),"info"in w&&t(3,u=w.info),"elem_id"in w&&t(4,o=w.elem_id),"elem_classes"in w&&t(5,_=w.elem_classes),"visible"in w&&t(6,i=w.visible),"multiselect"in w&&t(7,n=w.multiselect),"value"in w&&t(0,f=w.value),"value_is_output"in w&&t(1,r=w.value_is_output),"max_choices"in w&&t(8,p=w.max_choices),"choices"in w&&t(9,b=w.choices),"show_label"in w&&t(10,k=w.show_label),"filterable"in w&&t(11,D=w.filterable),"container"in w&&t(12,A=w.container),"scale"in w&&t(13,m=w.scale),"min_width"in w&&t(14,h=w.min_width),"loading_status"in w&&t(15,q=w.loading_status),"allow_custom_value"in w&&t(16,g=w.allow_custom_value),"root"in w&&t(17,c=w.root),"gradio"in w&&t(18,d=w.gradio),"interactive"in w&&t(19,B=w.interactive)},[f,r,s,u,o,_,i,n,p,b,k,D,A,m,h,q,g,c,d,B,C,S,v,E,y,U,X,j,Y,re,x,$,fe,_e,ce,O,pe]}class $l extends Sl{constructor(e){super(),Bl(this,e,Il,Ml,Nl,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,multiselect:7,value:0,value_is_output:1,max_choices:8,choices:9,show_label:10,filterable:11,container:12,scale:13,min_width:14,loading_status:15,allow_custom_value:16,root:17,gradio:18,interactive:19},null,[-1,-1])}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),N()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),N()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),N()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),N()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),N()}get multiselect(){return this.$$.ctx[7]}set multiselect(e){this.$$set({multiselect:e}),N()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),N()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),N()}get max_choices(){return this.$$.ctx[8]}set max_choices(e){this.$$set({max_choices:e}),N()}get choices(){return this.$$.ctx[9]}set choices(e){this.$$set({choices:e}),N()}get show_label(){return this.$$.ctx[10]}set show_label(e){this.$$set({show_label:e}),N()}get filterable(){return this.$$.ctx[11]}set filterable(e){this.$$set({filterable:e}),N()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),N()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),N()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),N()}get loading_status(){return this.$$.ctx[15]}set loading_status(e){this.$$set({loading_status:e}),N()}get allow_custom_value(){return this.$$.ctx[16]}set allow_custom_value(e){this.$$set({allow_custom_value:e}),N()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),N()}get gradio(){return this.$$.ctx[18]}set gradio(e){this.$$set({gradio:e}),N()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),N()}}export{Ol as BaseDropdown,ln as BaseExample,ol as BaseMultiselect,$l as default};
//# sourceMappingURL=Index-B5KsLQ-K.js.map
