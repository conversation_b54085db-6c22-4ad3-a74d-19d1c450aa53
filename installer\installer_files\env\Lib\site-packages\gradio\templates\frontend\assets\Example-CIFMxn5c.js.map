{"version": 3, "file": "Example-CIFMxn5c.js", "sources": ["../../../../js/fileexplorer/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string[] | string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<ul\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{#if value}\n\t\t{#each Array.isArray(value) ? value.slice(0, 3) : [value] as path}\n\t\t\t<li><code>./{path}</code></li>\n\t\t{/each}\n\t\t{#if Array.isArray(value) && value.length > 3}\n\t\t\t<li class=\"extra\">...</li>\n\t\t{/if}\n\t{/if}\n</ul>\n\n<style>\n\tul {\n\t\twhite-space: nowrap;\n\t\tmax-height: 100px;\n\t\tlist-style: none;\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t}\n\n\t.extra {\n\t\ttext-align: center;\n\t}\n\n\t.gallery {\n\t\talign-items: center;\n\t\tcursor: pointer;\n\t\tpadding: var(--size-1) var(--size-2);\n\t\ttext-align: left;\n\t}\n</style>\n"], "names": ["ctx", "i", "insert", "target", "li", "anchor", "append", "code", "set_data", "t1", "t1_value", "create_if_block", "toggle_class", "ul", "value", "$$props", "type", "selected"], "mappings": "+SAeO,MAAM,QAAQA,OAAUA,EAAK,CAAA,EAAC,OAAS,QAHrC,MAAM,QAAQA,EAAK,CAAA,CAAA,EAAIA,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,GAAKA,EAAK,CAAA,CAAA,CAAA,uBAAtD,OAAIC,GAAA,6MAAC,MAAM,QAAQD,EAAK,CAAA,CAAA,EAAIA,EAAM,CAAA,EAAA,MAAM,EAAG,CAAC,GAAKA,EAAK,CAAA,CAAA,CAAA,oBAAtD,OAAIC,GAAA,EAAA,yHAAJ,eAGG,MAAM,QAAQD,OAAUA,EAAK,CAAA,EAAC,OAAS,+HAF9BA,EAAI,CAAA,EAAA,0CAAP,IAAE,iBAAZE,EAA6BC,EAAAC,EAAAC,CAAA,EAAzBC,EAAqBF,EAAAG,CAAA,oCAAZP,EAAI,CAAA,EAAA,KAAAQ,EAAAC,EAAAC,CAAA,yHAGjBR,EAAyBC,EAAAC,EAAAC,CAAA,wCALtBL,EAAK,CAAA,GAAAW,EAAAX,CAAA,4DAJGY,EAAAC,EAAA,QAAAb,OAAS,OAAO,EACdY,EAAAC,EAAA,UAAAb,OAAS,SAAS,+BAFlCE,EAaIC,EAAAU,EAAAR,CAAA,2BAREL,EAAK,CAAA,gEAJGY,EAAAC,EAAA,QAAAb,OAAS,OAAO,OACdY,EAAAC,EAAA,UAAAb,OAAS,SAAS,+EAPtB,GAAA,CAAA,MAAAc,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}