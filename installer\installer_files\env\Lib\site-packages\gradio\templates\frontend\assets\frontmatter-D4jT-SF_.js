import{s as n,t as a,f as i,c as p,p as s,S as l}from"./Index-oRSdz4id.js";import{yaml as f}from"./yaml-DsCXHVTH.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./Check-BiRlaMNo.js";import"./Copy-CxQ9EyK2.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import"./prism-python-B8dcvKZU.js";import"./IconButton-B3BI2i6T.js";import"./Download-DVtk-Jv3.js";import"./DownloadLink-IzUam-rM.js";import"./context-TgWPFwN2.js";import"./file-url-DgijyRSD.js";/* empty css                                                   */import"./IconButtonWrapper-BMUxHqmv.js";import"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./Clear-By3xiIwg.js";import"./Code-DGNrTu_I.js";import"./Block-CB3nIXHA.js";import"./BlockLabel-CCoHIDM7.js";import"./Empty-B_fwEKaS.js";import"./Example-Wp-_4AVX.js";const m=/^---\s*$/m,$={defineNodes:[{name:"Frontmatter",block:!0},"FrontmatterMark"],props:[n({Frontmatter:[a.documentMeta,a.monospace],FrontmatterMark:a.processingInstruction}),i.add({Frontmatter:p,FrontmatterMark:()=>null})],wrap:s(t=>{const{parser:e}=l.define(f);return t.type.name==="Frontmatter"?{parser:e,overlay:[{from:t.from+4,to:t.to-4}]}:null}),parseBlock:[{name:"Frontmatter",before:"HorizontalRule",parse:(t,e)=>{let r;const o=new Array;if(t.lineStart===0&&m.test(e.text)){for(o.push(t.elt("FrontmatterMark",0,4));t.nextLine();)if(m.test(e.text)){r=t.lineStart+4;break}return r!==void 0&&(o.push(t.elt("FrontmatterMark",r-4,r)),t.addElement(t.elt("Frontmatter",0,r,o))),!0}return!1}}]};export{$ as frontmatter};
//# sourceMappingURL=frontmatter-D4jT-SF_.js.map
