{"version": 3, "file": "Index-Bqum3Bsx.js", "sources": ["../../../../node_modules/.pnpm/prismjs@1.29.0/node_modules/prismjs/components/prism-typescript.js", "../../../../js/paramviewer/ParamViewer.svelte", "../../../../js/paramviewer/Index.svelte"], "sourcesContent": ["(function (Prism) {\n\n\tPrism.languages.typescript = Prism.languages.extend('javascript', {\n\t\t'class-name': {\n\t\t\tpattern: /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n\t\t\tlookbehind: true,\n\t\t\tgreedy: true,\n\t\t\tinside: null // see below\n\t\t},\n\t\t'builtin': /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/,\n\t});\n\n\t// The keywords TypeScript adds to JavaScript\n\tPrism.languages.typescript.keyword.push(\n\t\t/\\b(?:abstract|declare|is|keyof|readonly|require)\\b/,\n\t\t// keywords that have to be followed by an identifier\n\t\t/\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/,\n\t\t// This is for `import type *, {}`\n\t\t/\\btype\\b(?=\\s*(?:[\\{*]|$))/\n\t);\n\n\t// doesn't work with TS because TS is too complex\n\tdelete Prism.languages.typescript['parameter'];\n\tdelete Prism.languages.typescript['literal-property'];\n\n\t// a version of typescript specifically for highlighting types\n\tvar typeInside = Prism.languages.extend('typescript', {});\n\tdelete typeInside['class-name'];\n\n\tPrism.languages.typescript['class-name'].inside = typeInside;\n\n\tPrism.languages.insertBefore('typescript', 'function', {\n\t\t'decorator': {\n\t\t\tpattern: /@[$\\w\\xA0-\\uFFFF]+/,\n\t\t\tinside: {\n\t\t\t\t'at': {\n\t\t\t\t\tpattern: /^@/,\n\t\t\t\t\talias: 'operator'\n\t\t\t\t},\n\t\t\t\t'function': /^[\\s\\S]+/\n\t\t\t}\n\t\t},\n\t\t'generic-function': {\n\t\t\t// e.g. foo<T extends \"bar\" | \"baz\">( ...\n\t\t\tpattern: /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n\t\t\tgreedy: true,\n\t\t\tinside: {\n\t\t\t\t'function': /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n\t\t\t\t'generic': {\n\t\t\t\t\tpattern: /<[\\s\\S]+/, // everything after the first <\n\t\t\t\t\talias: 'class-name',\n\t\t\t\t\tinside: typeInside\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n\n\tPrism.languages.ts = Prism.languages.typescript;\n\n}(Prism));\n", "<script lang=\"ts\">\n\timport \"./prism.css\";\n\n\timport Prism from \"prismjs\";\n\timport \"prismjs/components/prism-python\";\n\timport \"prismjs/components/prism-typescript\";\n\n\tinterface Param {\n\t\ttype: string | null;\n\t\tdescription: string;\n\t\tdefault: string | null;\n\t\tname?: string;\n\t}\n\n\texport let docs: Record<string, Param>;\n\texport let lang: \"python\" | \"typescript\" = \"python\";\n\texport let linkify: string[] = [];\n\texport let header: string | null;\n\n\tlet component_root: HTMLElement;\n\tlet _docs: Param[];\n\tlet all_open = false;\n\n\t$: _docs = highlight_code(docs, lang);\n\n\tfunction highlight(code: string, lang: \"python\" | \"typescript\"): string {\n\t\tlet highlighted = Prism.highlight(code, Prism.languages[lang], lang);\n\n\t\tfor (const link of linkify) {\n\t\t\thighlighted = highlighted.replace(\n\t\t\t\tnew RegExp(link, \"g\"),\n\t\t\t\t`<a href=\"#h-${link.toLocaleLowerCase()}\">${link}</a>`\n\t\t\t);\n\t\t}\n\n\t\treturn highlighted;\n\t}\n\n\tfunction highlight_code(\n\t\t_docs: typeof docs,\n\t\tlang: \"python\" | \"typescript\"\n\t): Param[] {\n\t\tif (!_docs) {\n\t\t\treturn [];\n\t\t}\n\t\treturn Object.entries(_docs).map(\n\t\t\t([name, { type, description, default: _default }]) => {\n\t\t\t\tlet highlighted_type = type ? highlight(type, lang) : null;\n\n\t\t\t\treturn {\n\t\t\t\t\tname: name,\n\t\t\t\t\ttype: highlighted_type,\n\t\t\t\t\tdescription: description,\n\t\t\t\t\tdefault: _default ? highlight(_default, lang) : null\n\t\t\t\t};\n\t\t\t}\n\t\t);\n\t}\n\n\tfunction toggle_all(): void {\n\t\tall_open = !all_open;\n\t\tconst details = component_root.querySelectorAll(\".param\");\n\t\tdetails.forEach((detail) => {\n\t\t\tif (detail instanceof HTMLDetailsElement) {\n\t\t\t\tdetail.open = all_open;\n\t\t\t}\n\t\t});\n\t}\n\n\tfunction render_links(description: string): string {\n\t\tconst escaped = description\n\t\t\t.replace(/&/g, \"&amp;\")\n\t\t\t.replace(/</g, \"&lt;\")\n\t\t\t.replace(/>/g, \"&gt;\")\n\t\t\t.replace(/\"/g, \"&quot;\")\n\t\t\t.replace(/'/g, \"&#039;\");\n\n\t\tconst markdown_links = escaped.replace(\n\t\t\t/\\[([^\\]]+)\\]\\(([^)]+)\\)/g,\n\t\t\t'<a href=\"$2\" target=\"_blank\">$1</a>'\n\t\t);\n\t\treturn markdown_links;\n\t}\n</script>\n\n<div class=\"wrap\" bind:this={component_root}>\n\t{#if header !== null}\n\t\t<div class=\"header\">\n\t\t\t<span class=\"title\">{header}</span>\n\t\t\t<button\n\t\t\t\tclass=\"toggle-all\"\n\t\t\t\ton:click={toggle_all}\n\t\t\t\ttitle={all_open ? \"Close All\" : \"Open All\"}\n\t\t\t>\n\t\t\t\t▼\n\t\t\t</button>\n\t\t</div>\n\t{/if}\n\t{#if _docs}\n\t\t{#each _docs as { type, description, default: _default, name } (name)}\n\t\t\t<details class=\"param md\">\n\t\t\t\t<summary class=\"type\">\n\t\t\t\t\t<pre class=\"language-{lang}\"><code\n\t\t\t\t\t\t\t>{name}{#if type}: {@html type}{/if}</code\n\t\t\t\t\t\t></pre>\n\t\t\t\t</summary>\n\t\t\t\t{#if _default}\n\t\t\t\t\t<div class=\"default\" class:last={!description}>\n\t\t\t\t\t\t<span style:padding-right={\"4px\"}>default</span>\n\t\t\t\t\t\t<code>= {@html _default}</code>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t\t{#if description}\n\t\t\t\t\t<div class=\"description\">\n\t\t\t\t\t\t<p>{@html render_links(description)}</p>\n\t\t\t\t\t</div>\n\t\t\t\t{/if}\n\t\t\t</details>\n\t\t{/each}\n\t{/if}\n</div>\n\n<style>\n\t.header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 0.7rem 1rem;\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t}\n\n\t.title {\n\t\tfont-size: var(--scale-0);\n\t\tfont-weight: 600;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.toggle-all {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcursor: pointer;\n\t\tpadding: 0;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: 0.7em;\n\t\tline-height: 1;\n\t\topacity: 0.7;\n\t\ttransition:\n\t\t\topacity 0.2s ease,\n\t\t\ttransform 0.3s ease;\n\t}\n\n\t.toggle-all:hover {\n\t\topacity: 1;\n\t}\n\n\t:global(.wrap[data-all-open=\"true\"]) .toggle-all {\n\t\ttransform: rotate(180deg);\n\t}\n\n\t.default :global(pre),\n\t.default :global(.highlight) {\n\t\tdisplay: inline-block;\n\t}\n\n\t.wrap :global(pre),\n\t.wrap :global(.highlight) {\n\t\tmargin: 0 !important;\n\t\tbackground: transparent !important;\n\t\tfont-family: var(--font-mono);\n\t\tfont-weight: 400;\n\t\tpadding: 0 !important;\n\t}\n\n\t.wrap :global(pre a) {\n\t\tcolor: var(--link-text-color-hover);\n\t\ttext-decoration: underline;\n\t}\n\n\t.wrap :global(pre a:hover) {\n\t\tcolor: var(--link-text-color-hover);\n\t}\n\n\t.default > span {\n\t\ttext-transform: uppercase;\n\t\tfont-size: 0.7rem;\n\t\tfont-weight: 600;\n\t}\n\n\t.default > code {\n\t\tborder: none;\n\t}\n\tcode {\n\t\tbackground: none;\n\t\tfont-family: var(--font-mono);\n\t}\n\n\t.wrap {\n\t\tpadding: 0rem;\n\t\tborder-radius: 5px;\n\t\tborder: 1px solid #eee;\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin: 0;\n\t\tbox-shadow: var(--block-shadow);\n\t\tborder-width: var(--block-border-width);\n\t\tborder-color: var(--block-border-color);\n\t\tborder-radius: var(--block-radius);\n\t\twidth: 100%;\n\t\tline-height: var(--line-sm);\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.type {\n\t\tposition: relative;\n\t\tpadding: 0.7rem 1rem;\n\t\tbackground: var(--table-odd-background-fill);\n\t\tborder-bottom: 0px solid var(--table-border-color);\n\t\tlist-style: none;\n\t}\n\n\t.type::after {\n\t\tcontent: \"▼\";\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tright: 15px;\n\t\ttransform: translateY(-50%);\n\t\ttransition: transform 0.3s ease;\n\t\tfont-size: 0.7em;\n\t\topacity: 0.7;\n\t}\n\n\tdetails[open] .type::after {\n\t\ttransform: translateY(-50%) rotate(180deg);\n\t}\n\n\t.default {\n\t\tpadding: 0.2rem 1rem 0.3rem 1rem;\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.default.last {\n\t\tborder-bottom: none;\n\t}\n\n\t.description {\n\t\tpadding: 0.7rem 1rem;\n\t\tfont-size: var(--scale-00);\n\t\tfont-family: var(--font-sans);\n\t\tbackground: var(--block-background-fill);\n\t}\n\n\t.param {\n\t\tborder-bottom: 1px solid var(--table-border-color);\n\t}\n\n\t.param:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\tdetails[open] .type {\n\t\tborder-bottom-width: 1px;\n\t}\n\n\t.param.md code {\n\t\tbackground: none;\n\t}\n\n\tdetails > summary {\n\t\tcursor: pointer;\n\t}\n\n\tdetails > summary::-webkit-details-marker {\n\t\tdisplay: none;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport ParamViewer from \"./ParamViewer.svelte\";\n\n\texport let value: Record<\n\t\tstring,\n\t\t{\n\t\t\ttype: string;\n\t\t\tdescription: string;\n\t\t\tdefault: string;\n\t\t}\n\t>;\n\n\texport let linkify: string[] = [];\n\texport let header: string | null = null;\n</script>\n\n<ParamViewer docs={value} {linkify} {header} />\n"], "names": ["Prism", "typeInside", "ctx", "insert", "target", "div", "anchor", "append", "span", "button", "i", "html_tag", "raw_value", "code", "render_links", "p", "dirty", "create_if_block_3", "create_if_block_2", "create_if_block_1", "details", "summary", "pre", "set_data", "t0", "t0_value", "if_block0", "create_if_block_4", "create_if_block", "description", "docs", "$$props", "lang", "linkify", "header", "component_root", "_docs", "all_open", "highlight", "highlighted", "link", "highlight_code", "name", "type", "_default", "highlighted_type", "toggle_all", "detail", "$$value", "$$invalidate", "value"], "mappings": "uGAAC,SAAUA,EAAO,CAEjBA,EAAM,UAAU,WAAaA,EAAM,UAAU,OAAO,aAAc,CACjE,aAAc,CACb,QAAS,+KACT,WAAY,GACZ,OAAQ,GACR,OAAQ,IACR,EACD,QAAW,uFACb,CAAE,EAGDA,EAAM,UAAU,WAAW,QAAQ,KAClC,qDAEA,2FAEA,4BACF,EAGC,OAAOA,EAAM,UAAU,WAAW,UAClC,OAAOA,EAAM,UAAU,WAAW,kBAAkB,EAGpD,IAAIC,EAAaD,EAAM,UAAU,OAAO,aAAc,CAAA,CAAE,EACxD,OAAOC,EAAW,YAAY,EAE9BD,EAAM,UAAU,WAAW,YAAY,EAAE,OAASC,EAElDD,EAAM,UAAU,aAAa,aAAc,WAAY,CACtD,UAAa,CACZ,QAAS,qBACT,OAAQ,CACP,GAAM,CACL,QAAS,KACT,MAAO,UACP,EACD,SAAY,UACZ,CACD,EACD,mBAAoB,CAEnB,QAAS,yGACT,OAAQ,GACR,OAAQ,CACP,SAAY,4DACZ,QAAW,CACV,QAAS,WACT,MAAO,aACP,OAAQC,CACR,CACD,CACD,CACH,CAAE,EAEDD,EAAM,UAAU,GAAKA,EAAM,UAAU,UAEtC,GAAE,KAAK,6eC6BiBE,EAAM,CAAA,CAAA,0BAK3B,GAEA,6FAHQA,EAAQ,CAAA,EAAG,YAAc,UAAU,+CAL5CC,EASKC,EAAAC,EAAAC,CAAA,EARJC,EAAkCF,EAAAG,CAAA,gBAClCD,EAMQF,EAAAI,CAAA,2BAJGP,EAAU,CAAA,CAAA,yBAHAA,EAAM,CAAA,CAAA,eAInBA,EAAQ,CAAA,EAAG,YAAc,4FAO3BA,EAAK,CAAA,CAAA,aAAoDA,EAAI,EAAA,kBAAlE,OAAIQ,GAAA,EAAA,mLAACR,EAAK,CAAA,CAAA,yHAImBA,EAAI,EAAA,EAAA,oBAAb,IAAE,mFAAOA,EAAI,EAAA,EAAA,KAAAS,EAAA,EAAAC,CAAA,+DAMhBV,EAAQ,EAAA,EAAA,mFAAjB,IAAE,gEADmB,KAAK,2FADCA,EAAW,EAAA,CAAA,UAA7CC,EAGKC,EAAAC,EAAAC,CAAA,EAFJC,EAA+CF,EAAAG,CAAA,SAC/CD,EAA8BF,EAAAQ,CAAA,sCAAfX,EAAQ,EAAA,EAAA,KAAAS,EAAA,EAAAC,CAAA,mBAFUV,EAAW,EAAA,CAAA,wCAOlCU,EAAAE,EAAaZ,EAAW,EAAA,CAAA,EAAA,oFADnCC,EAEKC,EAAAC,EAAAC,CAAA,EADJC,EAAuCF,EAAAU,CAAA,wBAA7BC,EAAA,GAAAJ,KAAAA,EAAAE,EAAaZ,EAAW,EAAA,CAAA,EAAA,MAAAa,EAAA,UAAAH,iDAX/BV,EAAI,EAAA,EAAA,eAAMA,EAAI,EAAA,GAAAe,EAAAf,CAAA,IAGdA,EAAQ,EAAA,GAAAgB,EAAAhB,CAAA,IAMRA,EAAW,EAAA,GAAAiB,EAAAjB,CAAA,+LAVOA,EAAI,CAAA,EAAA,iBAAA,iGAF5BC,EAiBSC,EAAAgB,EAAAd,CAAA,EAhBRC,EAISa,EAAAC,CAAA,EAHRd,EAEOc,EAAAC,CAAA,EAFsBf,EAE3Be,EAAAT,CAAA,mGADEX,EAAI,EAAA,EAAA,KAAAqB,EAAAC,EAAAC,CAAA,EAAMvB,EAAI,EAAA,mFADIA,EAAI,CAAA,EAAA,mCAItBA,EAAQ,EAAA,wDAMRA,EAAW,EAAA,yHA1BdwB,EAAAxB,OAAW,MAAIyB,EAAAzB,CAAA,IAYfA,EAAK,CAAA,GAAA0B,EAAA1B,CAAA,2FAbXC,EAmCKC,EAAAC,EAAAC,CAAA,yDAlCCJ,OAAW,2DAYXA,EAAK,CAAA,yHA7BDY,EAAae,EAAA,CAYd,OAXSA,EACd,QAAQ,KAAM,OAAO,EACrB,QAAQ,KAAM,MAAM,EACpB,QAAQ,KAAM,MAAM,EACpB,QAAQ,KAAM,QAAQ,EACtB,QAAQ,KAAM,QAAQ,EAEO,QAC9B,2BACA,qCAAA,qBAjES,GAAA,CAAA,KAAAC,CAAA,EAAAC,GACA,KAAAC,EAAgC,QAAA,EAAAD,EAChC,CAAA,QAAAE,EAAA,EAAA,EAAAF,EACA,CAAA,OAAAG,CAAA,EAAAH,EAEPI,EACAC,EACAC,EAAW,GAIN,SAAAC,EAAUzB,EAAcmB,EAAAA,CAC5B,IAAAO,EAAcvC,EAAM,UAAUa,EAAMb,EAAM,UAAUgC,CAAI,EAAGA,CAAI,YAExDQ,KAAQP,EAClBM,EAAcA,EAAY,YACrB,OAAOC,EAAM,GAAG,EACL,eAAAA,EAAK,wBAAwBA,CAAI,MAAA,EAI3C,OAAAD,EAGC,SAAAE,EACRL,EACAJ,EAAAA,CAEKI,OAAAA,EAGE,OAAO,QAAQA,CAAK,EAAE,MAC1BM,EAAQ,CAAA,KAAAC,EAAM,YAAAd,EAAa,QAASe,CAAA,CAAA,IAAA,CACjC,IAAAC,EAAmBF,EAAOL,EAAUK,EAAMX,CAAI,EAAI,YAGrD,KAAAU,EACA,KAAMG,EACN,YAAAhB,EACA,QAASe,EAAWN,EAAUM,EAAUZ,CAAI,EAAI,WAM3C,SAAAc,GAAA,KACRT,EAAY,CAAAA,CAAA,EACIF,EAAe,iBAAiB,QAAQ,EAChD,QAASY,GAAA,CACZA,aAAkB,qBACrBA,EAAO,KAAOV,8CAqBWF,EAAca,iLA9DvCC,EAAA,EAAAb,EAAQK,EAAeX,EAAME,CAAI,CAAA,8oBCPlB9B,EAAK,CAAA,8GAALA,EAAK,CAAA,8JAbZ,GAAA,CAAA,MAAAgD,CAAA,EAAAnB,EASA,CAAA,QAAAE,EAAA,EAAA,EAAAF,GACA,OAAAG,EAAwB,IAAA,EAAAH", "x_google_ignoreList": [0]}