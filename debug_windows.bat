@echo off
REM Debug Windows startup script for Roop Floyd
REM This script provides detailed error information and debugging

echo ========================================
echo Roop Floyd Windows Debug Launcher
echo ========================================

REM Set error handling
setlocal enabledelayedexpansion

REM Check if Python is available
echo Checking Python installation...
py --version >nul 2>&1
if %errorlevel% neq 0 (
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: Python is not installed or not in PATH.
        echo Please install Python 3.9+ and try again.
        echo.
        echo You can download Python from: https://www.python.org/downloads/
        pause
        exit /b 1
    )
    set PYTHON_CMD=python
    echo Using: python
) else (
    set PYTHON_CMD=py
    echo Using: py
)

REM Show Python version
echo Python version:
%PYTHON_CMD% --version

REM Check if we're in the correct directory
echo.
echo Checking directory structure...
if not exist "run.py" (
    echo ERROR: run.py not found. 
    echo Current directory: %CD%
    echo Please run this script from the roop-floyd directory.
    echo.
    dir /b *.py
    pause
    exit /b 1
)
echo ✓ Found run.py

REM Check for config file
if not exist "config.yaml" (
    echo WARNING: config.yaml not found, will use defaults
) else (
    echo ✓ Found config.yaml
)

REM Check critical Python packages
echo.
echo Checking Python packages...
%PYTHON_CMD% -c "import sys; print('Python path:', sys.executable)"

echo Checking torch...
%PYTHON_CMD% -c "import torch; print('✓ torch version:', torch.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ✗ torch not found or error importing
    set MISSING_PACKAGES=1
)

echo Checking cv2...
%PYTHON_CMD% -c "import cv2; print('✓ opencv version:', cv2.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ✗ opencv not found or error importing
    set MISSING_PACKAGES=1
)

echo Checking gradio...
%PYTHON_CMD% -c "import gradio; print('✓ gradio version:', gradio.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ✗ gradio not found or error importing
    set MISSING_PACKAGES=1
)

if defined MISSING_PACKAGES (
    echo.
    echo Some required packages are missing. Installing requirements...
    %PYTHON_CMD% -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install requirements.
        echo Try running as Administrator or check your internet connection.
        pause
        exit /b 1
    )
)

REM Check CUDA availability
echo.
echo Checking CUDA availability...
%PYTHON_CMD% -c "import torch; print('CUDA available:', torch.cuda.is_available()); print('CUDA devices:', torch.cuda.device_count() if torch.cuda.is_available() else 'None')" 2>nul

REM Check FFmpeg
echo.
echo Checking FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: FFmpeg not found in PATH. Video processing may not work.
    echo You can install FFmpeg using the main installer: installer\windows_run.bat
) else (
    echo ✓ FFmpeg is available
)

echo.
echo ========================================
echo Starting Roop Floyd...
echo ========================================
echo.

REM Run with detailed error output
%PYTHON_CMD% run.py %* 2>&1

set EXIT_CODE=%errorlevel%

echo.
echo ========================================
if %EXIT_CODE% neq 0 (
    echo Application exited with error code %EXIT_CODE%
    echo.
    echo Common solutions:
    echo 1. Run as Administrator
    echo 2. Check antivirus software
    echo 3. Ensure all dependencies are installed
    echo 4. Try using CPU mode if GPU issues
    echo 5. Check the error messages above
) else (
    echo Application exited normally
)
echo ========================================

pause
