import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{B as ft}from"./BlockTitle-IUerRYnJ.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{C as _t}from"./Clear-By3xiIwg.js";import{F as ct}from"./File-BQ_9P3Ye.js";import{M as gt}from"./Music-CDm0RGMk.js";import{S as ht}from"./Send-DyoOovnk.js";import{S as mt}from"./Square-oAGqOwsh.js";import{V as dt}from"./Video-fsmLZWjA.js";import{U as bt}from"./Upload-CRdEJrCZ.js";/* empty css                                                   */import{I as pt}from"./Image-BRVH1yXn.js";/* empty css                                                   */import{B as kt}from"./Block-CB3nIXHA.js";import{S as wt}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{default as Nl}from"./Example-DGmT8jeC.js";import"./Info-QztxykAE.js";import"./MarkdownCode-DYQlapxH.js";import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";/* empty css                                             */import"./file-url-DgijyRSD.js";import"./context-TgWPFwN2.js";import"./IconButton-B3BI2i6T.js";import"./Video-DiLYgEjl.js";import"./hls-CnVhpNcu.js";const{SvelteComponent:vt,append:me,attr:R,detach:zt,init:Ct,insert:St,noop:Ce,safe_not_equal:Tt,svg_element:_e}=window.__gradio__svelte__internal;function Dt(l){let e,t,i,u,o;return{c(){e=_e("svg"),t=_e("g"),i=_e("g"),u=_e("g"),o=_e("path"),R(t,"id","SVGRepo_bgCarrier"),R(t,"stroke-width","0"),R(i,"id","SVGRepo_tracerCarrier"),R(i,"stroke-linecap","round"),R(i,"stroke-linejoin","round"),R(o,"d","M1752.768 221.109C1532.646.986 1174.283.986 954.161 221.109l-838.588 838.588c-154.052 154.165-154.052 404.894 0 558.946 149.534 149.421 409.976 149.308 559.059 0l758.738-758.626c87.982-88.094 87.982-231.417 0-319.51-88.32-88.208-231.642-87.982-319.51 0l-638.796 638.908 79.85 79.849 638.795-638.908c43.934-43.821 115.539-43.934 159.812 0 43.934 44.047 43.934 115.877 0 159.812l-758.739 758.625c-110.23 110.118-289.355 110.005-399.36 0-110.118-110.117-110.005-289.242 0-399.247l838.588-838.588c175.963-175.962 462.382-176.188 638.909 0 176.075 176.188 176.075 462.833 0 638.908l-798.607 798.72 79.849 79.85 798.607-798.72c220.01-220.123 220.01-578.485 0-798.607"),R(o,"fill-rule","evenodd"),R(u,"id","SVGRepo_iconCarrier"),R(e,"fill","currentColor"),R(e,"width","100%"),R(e,"height","100%"),R(e,"viewBox","0 0 1920 1920"),R(e,"xmlns","http://www.w3.org/2000/svg")},m(c,f){St(c,e,f),me(e,t),me(e,i),me(e,u),me(u,o)},p:Ce,i:Ce,o:Ce,d(c){c&&zt(e)}}}class Bt extends vt{constructor(e){super(),Ct(this,e,null,Dt,Tt,{})}}const{tick:Mt}=window.__gradio__svelte__internal;async function He(l,e,t){if(await Mt(),e===t)return;const i=window.getComputedStyle(l),u=parseFloat(i.paddingTop),o=parseFloat(i.paddingBottom),c=parseFloat(i.lineHeight);let f=t===void 0?!1:u+o+c*t,s=u+o+e*c;l.style.height="1px";let g;f&&l.scrollHeight>f?g=f:l.scrollHeight<s?g=s:g=l.scrollHeight,l.style.height=`${g}px`}function Ht(l,e){if(e.lines===e.max_lines)return;l.style.overflowY="scroll";function t(i){He(i.target,e.lines,e.max_lines)}if(l.addEventListener("input",t),!!e.text.trim())return He(l,e.lines,e.max_lines),{destroy:()=>l.removeEventListener("input",t)}}const{SvelteComponent:Et,action_destroyer:Ft,add_flush_callback:Se,append:j,attr:D,bind:Te,binding_callbacks:oe,bubble:de,check_outros:x,create_component:X,destroy_component:J,destroy_each:qt,detach:P,element:G,ensure_array_like:Ae,flush:E,group_outros:y,init:Ut,insert:W,is_function:Vt,listen:U,mount_component:O,noop:Q,prevent_default:At,run_all:Rt,safe_not_equal:jt,set_data:Ee,set_input_value:Re,set_style:je,space:Z,text:Fe,toggle_class:L,transition_in:T,transition_out:M}=window.__gradio__svelte__internal,{onMount:Lt,beforeUpdate:Gt,afterUpdate:It,createEventDispatcher:Kt,tick:Le}=window.__gradio__svelte__internal;function Ge(l,e,t){const i=l.slice();return i[60]=e[t],i[62]=t,i}function Pt(l){let e;return{c(){e=Fe(l[5])},m(t,i){W(t,e,i)},p(t,i){i[0]&32&&Ee(e,t[5])},d(t){t&&P(e)}}}function Ie(l){let e,t,i,u=Ae(l[0].files),o=[];for(let s=0;s<u.length;s+=1)o[s]=Ke(Ge(l,u,s));const c=s=>M(o[s],1,1,()=>{o[s]=null});let f=l[24]&&Pe();return{c(){e=G("div");for(let s=0;s<o.length;s+=1)o[s].c();t=Z(),f&&f.c(),D(e,"class","thumbnails scroll-hide svelte-cgv3eu"),D(e,"aria-label","Uploaded files"),D(e,"data-testid","container_el"),je(e,"display",l[0].files.length>0||l[24]?"flex":"none")},m(s,g){W(s,e,g);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(e,null);j(e,t),f&&f.m(e,null),i=!0},p(s,g){if(g[0]&1073741841){u=Ae(s[0].files);let a;for(a=0;a<u.length;a+=1){const k=Ge(s,u,a);o[a]?(o[a].p(k,g),T(o[a],1)):(o[a]=Ke(k),o[a].c(),T(o[a],1),o[a].m(e,t))}for(y(),a=u.length;a<o.length;a+=1)c(a);x()}s[24]?f||(f=Pe(),f.c(),f.m(e,null)):f&&(f.d(1),f=null),(!i||g[0]&16777217)&&je(e,"display",s[0].files.length>0||s[24]?"flex":"none")},i(s){if(!i){for(let g=0;g<u.length;g+=1)T(o[g]);i=!0}},o(s){o=o.filter(Boolean);for(let g=0;g<o.length;g+=1)M(o[g]);i=!1},d(s){s&&P(e),qt(o,s),f&&f.d()}}}function Wt(l){let e,t;return e=new ct({}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p:Q,i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Yt(l){let e,t;return e=new dt({}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p:Q,i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Nt(l){let e,t;return e=new gt({}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p:Q,i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Xt(l){let e,t;return e=new pt({props:{src:l[60].url,title:null,alt:"",loading:"lazy",class:"thumbnail-image"}}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p(i,u){const o={};u[0]&1&&(o.src=i[60].url),e.$set(o)},i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Ke(l){let e,t,i,u,o,c,f,s,g,a,k,r,d;u=new _t({});function h(...b){return l[45](l[62],...b)}const p=[Xt,Nt,Yt,Wt],C=[];function w(b,v){return v[0]&1&&(c=null),v[0]&1&&(f=null),v[0]&1&&(s=null),c==null&&(c=!!(b[60].mime_type&&b[60].mime_type.includes("image"))),c?0:(f==null&&(f=!!(b[60].mime_type&&b[60].mime_type.includes("audio"))),f?1:(s==null&&(s=!!(b[60].mime_type&&b[60].mime_type.includes("video"))),s?2:3))}return g=w(l,[-1,-1,-1]),a=C[g]=p[g](l),{c(){e=G("span"),t=G("button"),i=G("button"),X(u.$$.fragment),o=Z(),a.c(),D(i,"class","delete-button svelte-cgv3eu"),L(i,"disabled",l[4]),D(t,"class","thumbnail-item thumbnail-small svelte-cgv3eu"),D(e,"role","listitem"),D(e,"aria-label","File thumbnail")},m(b,v){W(b,e,v),j(e,t),j(t,i),O(u,i,null),j(t,o),C[g].m(t,null),k=!0,r||(d=U(i,"click",h),r=!0)},p(b,v){l=b,(!k||v[0]&16)&&L(i,"disabled",l[4]);let S=g;g=w(l,v),g===S?C[g].p(l,v):(y(),M(C[S],1,1,()=>{C[S]=null}),x(),a=C[g],a?a.p(l,v):(a=C[g]=p[g](l),a.c()),T(a,1),a.m(t,null))},i(b){k||(T(u.$$.fragment,b),T(a),k=!0)},o(b){M(u.$$.fragment,b),M(a),k=!1},d(b){b&&P(e),J(u),C[g].d(),r=!1,d()}}}function Pe(l){let e;return{c(){e=G("div"),D(e,"class","loader svelte-cgv3eu"),D(e,"role","status"),D(e,"aria-label","Uploading")},m(t,i){W(t,e,i)},d(t){t&&P(e)}}}function We(l){let e,t,i,u,o,c,f,s,g,a;function k(p){l[47](p)}function r(p){l[48](p)}function d(p){l[49](p)}let h={file_count:l[20],filetype:l[16],root:l[15],max_file_size:l[17],show_progress:!1,disable_click:!0,hidden:!0,upload:l[18],stream_handler:l[19]};return l[1]!==void 0&&(h.dragging=l[1]),l[24]!==void 0&&(h.uploading=l[24]),l[23]!==void 0&&(h.hidden_upload=l[23]),e=new bt({props:h}),l[46](e),oe.push(()=>Te(e,"dragging",k)),oe.push(()=>Te(e,"uploading",r)),oe.push(()=>Te(e,"hidden_upload",d)),e.$on("load",l[29]),e.$on("error",l[50]),f=new Bt({}),{c(){X(e.$$.fragment),o=Z(),c=G("button"),X(f.$$.fragment),D(c,"data-testid","upload-button"),D(c,"class","upload-button svelte-cgv3eu")},m(p,C){O(e,p,C),W(p,o,C),W(p,c,C),O(f,c,null),s=!0,g||(a=U(c,"click",l[31]),g=!0)},p(p,C){const w={};C[0]&1048576&&(w.file_count=p[20]),C[0]&65536&&(w.filetype=p[16]),C[0]&32768&&(w.root=p[15]),C[0]&131072&&(w.max_file_size=p[17]),C[0]&262144&&(w.upload=p[18]),C[0]&524288&&(w.stream_handler=p[19]),!t&&C[0]&2&&(t=!0,w.dragging=p[1],Se(()=>t=!1)),!i&&C[0]&16777216&&(i=!0,w.uploading=p[24],Se(()=>i=!1)),!u&&C[0]&8388608&&(u=!0,w.hidden_upload=p[23],Se(()=>u=!1)),e.$set(w)},i(p){s||(T(e.$$.fragment,p),T(f.$$.fragment,p),s=!0)},o(p){M(e.$$.fragment,p),M(f.$$.fragment,p),s=!1},d(p){p&&(P(o),P(c)),l[46](null),J(e,p),J(f),g=!1,a()}}}function Ye(l){let e,t,i,u,o,c;const f=[Ot,Jt],s=[];function g(a,k){return a[10]===!0?0:1}return t=g(l),i=s[t]=f[t](l),{c(){e=G("button"),i.c(),D(e,"class","submit-button svelte-cgv3eu"),L(e,"padded-button",l[10]!==!0)},m(a,k){W(a,e,k),s[t].m(e,null),u=!0,o||(c=U(e,"click",l[33]),o=!0)},p(a,k){let r=t;t=g(a),t===r?s[t].p(a,k):(y(),M(s[r],1,1,()=>{s[r]=null}),x(),i=s[t],i?i.p(a,k):(i=s[t]=f[t](a),i.c()),T(i,1),i.m(e,null)),(!u||k[0]&1024)&&L(e,"padded-button",a[10]!==!0)},i(a){u||(T(i),u=!0)},o(a){M(i),u=!1},d(a){a&&P(e),s[t].d(),o=!1,c()}}}function Jt(l){let e;return{c(){e=Fe(l[10])},m(t,i){W(t,e,i)},p(t,i){i[0]&1024&&Ee(e,t[10])},i:Q,o:Q,d(t){t&&P(e)}}}function Ot(l){let e,t;return e=new ht({}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p:Q,i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function Ne(l){let e,t,i,u,o,c;const f=[Zt,Qt],s=[];function g(a,k){return a[11]===!0?0:1}return t=g(l),i=s[t]=f[t](l),{c(){e=G("button"),i.c(),D(e,"class","stop-button svelte-cgv3eu"),L(e,"padded-button",l[11]!==!0)},m(a,k){W(a,e,k),s[t].m(e,null),u=!0,o||(c=U(e,"click",l[32]),o=!0)},p(a,k){let r=t;t=g(a),t===r?s[t].p(a,k):(y(),M(s[r],1,1,()=>{s[r]=null}),x(),i=s[t],i?i.p(a,k):(i=s[t]=f[t](a),i.c()),T(i,1),i.m(e,null)),(!u||k[0]&2048)&&L(e,"padded-button",a[11]!==!0)},i(a){u||(T(i),u=!0)},o(a){M(i),u=!1},d(a){a&&P(e),s[t].d(),o=!1,c()}}}function Qt(l){let e;return{c(){e=Fe(l[11])},m(t,i){W(t,e,i)},p(t,i){i[0]&2048&&Ee(e,t[11])},i:Q,o:Q,d(t){t&&P(e)}}}function Zt(l){let e,t;return e=new mt({props:{fill:"none",stroke_width:2.5}}),{c(){X(e.$$.fragment)},m(i,u){O(e,i,u),t=!0},p:Q,i(i){t||(T(e.$$.fragment,i),t=!0)},o(i){M(e.$$.fragment,i),t=!1},d(i){J(e,i)}}}function xt(l){let e,t,i,u,o,c,f,s,g,a,k,r,d,h,p,C;i=new ft({props:{root:l[15],show_label:l[7],info:l[6],$$slots:{default:[Pt]},$$scope:{ctx:l}}});let w=(l[0].files.length>0||l[24])&&Ie(l),b=!l[4]&&!(l[20]==="single"&&l[0].files.length>0)&&We(l),v=l[10]&&Ye(l),S=l[11]&&Ne(l);return{c(){e=G("div"),t=G("label"),X(i.$$.fragment),u=Z(),w&&w.c(),o=Z(),c=G("div"),b&&b.c(),f=Z(),s=G("textarea"),r=Z(),v&&v.c(),d=Z(),S&&S.c(),D(s,"data-testid","textbox"),D(s,"class","scroll-hide svelte-cgv3eu"),D(s,"dir",g=l[12]?"rtl":"ltr"),D(s,"placeholder",l[3]),D(s,"rows",l[2]),s.disabled=l[4],s.autofocus=l[13],D(s,"style",a=l[14]?"text-align: "+l[14]:""),L(s,"no-label",!l[7]),D(c,"class","input-container svelte-cgv3eu"),L(t,"container",l[8]),D(e,"class","full-container svelte-cgv3eu"),D(e,"role","group"),D(e,"aria-label","Multimedia input field"),L(e,"dragging",l[1])},m(m,z){W(m,e,z),j(e,t),O(i,t,null),j(t,u),w&&w.m(t,null),j(t,o),j(t,c),b&&b.m(c,null),j(c,f),j(c,s),Re(s,l[0].text),l[52](s),j(c,r),v&&v.m(c,null),j(c,d),S&&S.m(c,null),l[53](e),h=!0,l[13]&&s.focus(),p||(C=[Ft(k=Ht.call(null,s,{text:l[0].text,lines:l[2],max_lines:l[9]})),U(s,"input",l[51]),U(s,"keypress",l[27]),U(s,"blur",l[43]),U(s,"select",l[26]),U(s,"focus",l[44]),U(s,"scroll",l[28]),U(s,"paste",l[34]),U(e,"dragenter",l[35]),U(e,"dragleave",l[36]),U(e,"dragover",At(l[42])),U(e,"drop",l[37])],p=!0)},p(m,z){const I={};z[0]&32768&&(I.root=m[15]),z[0]&128&&(I.show_label=m[7]),z[0]&64&&(I.info=m[6]),z[0]&32|z[2]&2&&(I.$$scope={dirty:z,ctx:m}),i.$set(I),m[0].files.length>0||m[24]?w?(w.p(m,z),z[0]&16777217&&T(w,1)):(w=Ie(m),w.c(),T(w,1),w.m(t,o)):w&&(y(),M(w,1,1,()=>{w=null}),x()),!m[4]&&!(m[20]==="single"&&m[0].files.length>0)?b?(b.p(m,z),z[0]&1048593&&T(b,1)):(b=We(m),b.c(),T(b,1),b.m(c,f)):b&&(y(),M(b,1,1,()=>{b=null}),x()),(!h||z[0]&4096&&g!==(g=m[12]?"rtl":"ltr"))&&D(s,"dir",g),(!h||z[0]&8)&&D(s,"placeholder",m[3]),(!h||z[0]&4)&&D(s,"rows",m[2]),(!h||z[0]&16)&&(s.disabled=m[4]),(!h||z[0]&8192)&&(s.autofocus=m[13]),(!h||z[0]&16384&&a!==(a=m[14]?"text-align: "+m[14]:""))&&D(s,"style",a),k&&Vt(k.update)&&z[0]&517&&k.update.call(null,{text:m[0].text,lines:m[2],max_lines:m[9]}),z[0]&1&&Re(s,m[0].text),(!h||z[0]&128)&&L(s,"no-label",!m[7]),m[10]?v?(v.p(m,z),z[0]&1024&&T(v,1)):(v=Ye(m),v.c(),T(v,1),v.m(c,d)):v&&(y(),M(v,1,1,()=>{v=null}),x()),m[11]?S?(S.p(m,z),z[0]&2048&&T(S,1)):(S=Ne(m),S.c(),T(S,1),S.m(c,null)):S&&(y(),M(S,1,1,()=>{S=null}),x()),(!h||z[0]&256)&&L(t,"container",m[8]),(!h||z[0]&2)&&L(e,"dragging",m[1])},i(m){h||(T(i.$$.fragment,m),T(w),T(b),T(v),T(S),h=!0)},o(m){M(i.$$.fragment,m),M(w),M(b),M(v),M(S),h=!1},d(m){m&&P(e),J(i),w&&w.d(),b&&b.d(),l[52](null),v&&v.d(),S&&S.d(),l[53](null),p=!1,Rt(C)}}}function yt(l,e,t){let{value:i={text:"",files:[]}}=e,{value_is_output:u=!1}=e,{lines:o=1}=e,{placeholder:c="Type here..."}=e,{disabled:f=!1}=e,{label:s}=e,{info:g=void 0}=e,{show_label:a=!0}=e,{container:k=!0}=e,{max_lines:r}=e,{submit_btn:d=null}=e,{stop_btn:h=null}=e,{rtl:p=!1}=e,{autofocus:C=!1}=e,{text_align:w=void 0}=e,{autoscroll:b=!0}=e,{root:v}=e,{file_types:S=null}=e,{max_file_size:m=null}=e,{upload:z}=e,{stream_handler:I}=e,{file_count:ue="multiple"}=e,{max_plain_text_length:ee=1e3}=e,K,Y,F,te,le=0,ae=!1,{dragging:N=!1}=e,re=!1,fe=i.text,ie;const V=Kt();Gt(()=>{te=F&&F.offsetHeight+F.scrollTop>F.scrollHeight-100});const be=()=>{te&&b&&!ae&&F.scrollTo(0,F.scrollHeight)};async function ge(){V("change",i),u||V("input")}Lt(()=>{C&&F!==null&&F.focus()}),It(()=>{te&&b&&be(),t(38,u=!1)});function pe(n){const H=n.target,q=H.value,A=[H.selectionStart,H.selectionEnd];V("select",{value:q.substring(...A),index:A})}async function ke(n){await Le(),(n.key==="Enter"&&n.shiftKey&&o>1||n.key==="Enter"&&!n.shiftKey&&o===1&&r>=1)&&(n.preventDefault(),V("submit"))}function we(n){const H=n.target,q=H.scrollTop;q<le&&(ae=!0),le=q;const A=H.scrollHeight-H.clientHeight;q>=A&&(ae=!1)}async function ve({detail:n}){if(ge(),Array.isArray(n)){for(let H of n)i.files.push(H);t(0,i)}else i.files.push(n),t(0,i);await Le(),V("change",i),V("upload",n)}function he(n,H){ge(),n.stopPropagation(),i.files.splice(H,1),t(0,i)}function ze(){Y&&(t(23,Y.value="",Y),Y.click())}function _(){V("stop")}function Je(){V("submit")}async function Oe(n){if(!n.clipboardData)return;const H=n.clipboardData.items,q=n.clipboardData.getData("text");if(q&&q.length>ee){n.preventDefault();const A=new window.File([q],"pasted_text.txt",{type:"text/plain",lastModified:Date.now()});K&&K.load_files([A]);return}for(let A in H){const $=H[A];if($.kind==="file"&&$.type.includes("image")){const ne=$.getAsFile();ne&&K.load_files([ne])}}}function Qe(n){n.preventDefault(),t(1,N=!0)}function Ze(n){n.preventDefault();const H=ie.getBoundingClientRect(),{clientX:q,clientY:A}=n;(q<=H.left||q>=H.right||A<=H.top||A>=H.bottom)&&t(1,N=!1)}function xe(n){if(n.preventDefault(),t(1,N=!1),n.dataTransfer&&n.dataTransfer.files){const H=Array.from(n.dataTransfer.files);if(S){const q=H.filter($=>S.some(ne=>ne.startsWith(".")?$.name.toLowerCase().endsWith(ne.toLowerCase()):$.type.match(new RegExp(ne.replace("*",".*"))))),A=H.length-q.length;A>0&&V("error",`${A} file(s) were rejected. Accepted formats: ${S.join(", ")}`),q.length>0&&K.load_files(q)}else K.load_files(H)}}function ye(n){de.call(this,l,n)}function $e(n){de.call(this,l,n)}function et(n){de.call(this,l,n)}const tt=(n,H)=>he(H,n);function lt(n){oe[n?"unshift":"push"](()=>{K=n,t(22,K)})}function it(n){N=n,t(1,N)}function nt(n){re=n,t(24,re)}function st(n){Y=n,t(23,Y)}function ot(n){de.call(this,l,n)}function ut(){i.text=this.value,t(0,i)}function at(n){oe[n?"unshift":"push"](()=>{F=n,t(21,F)})}function rt(n){oe[n?"unshift":"push"](()=>{ie=n,t(25,ie)})}return l.$$set=n=>{"value"in n&&t(0,i=n.value),"value_is_output"in n&&t(38,u=n.value_is_output),"lines"in n&&t(2,o=n.lines),"placeholder"in n&&t(3,c=n.placeholder),"disabled"in n&&t(4,f=n.disabled),"label"in n&&t(5,s=n.label),"info"in n&&t(6,g=n.info),"show_label"in n&&t(7,a=n.show_label),"container"in n&&t(8,k=n.container),"max_lines"in n&&t(9,r=n.max_lines),"submit_btn"in n&&t(10,d=n.submit_btn),"stop_btn"in n&&t(11,h=n.stop_btn),"rtl"in n&&t(12,p=n.rtl),"autofocus"in n&&t(13,C=n.autofocus),"text_align"in n&&t(14,w=n.text_align),"autoscroll"in n&&t(39,b=n.autoscroll),"root"in n&&t(15,v=n.root),"file_types"in n&&t(16,S=n.file_types),"max_file_size"in n&&t(17,m=n.max_file_size),"upload"in n&&t(18,z=n.upload),"stream_handler"in n&&t(19,I=n.stream_handler),"file_count"in n&&t(20,ue=n.file_count),"max_plain_text_length"in n&&t(40,ee=n.max_plain_text_length),"dragging"in n&&t(1,N=n.dragging)},l.$$.update=()=>{l.$$.dirty[0]&2&&V("drag",N),l.$$.dirty[0]&1&&i===null&&t(0,i={text:"",files:[]}),l.$$.dirty[0]&1|l.$$.dirty[1]&1024&&fe!==i.text&&(V("change",i),t(41,fe=i.text)),l.$$.dirty[0]&2097669&&F&&o!==r&&He(F,o,r)},[i,N,o,c,f,s,g,a,k,r,d,h,p,C,w,v,S,m,z,I,ue,F,K,Y,re,ie,pe,ke,we,ve,he,ze,_,Je,Oe,Qe,Ze,xe,u,b,ee,fe,ye,$e,et,tt,lt,it,nt,st,ot,ut,at,rt]}class $t extends Et{constructor(e){super(),Ut(this,e,yt,xt,jt,{value:0,value_is_output:38,lines:2,placeholder:3,disabled:4,label:5,info:6,show_label:7,container:8,max_lines:9,submit_btn:10,stop_btn:11,rtl:12,autofocus:13,text_align:14,autoscroll:39,root:15,file_types:16,max_file_size:17,upload:18,stream_handler:19,file_count:20,max_plain_text_length:40,dragging:1},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),E()}get value_is_output(){return this.$$.ctx[38]}set value_is_output(e){this.$$set({value_is_output:e}),E()}get lines(){return this.$$.ctx[2]}set lines(e){this.$$set({lines:e}),E()}get placeholder(){return this.$$.ctx[3]}set placeholder(e){this.$$set({placeholder:e}),E()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),E()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),E()}get info(){return this.$$.ctx[6]}set info(e){this.$$set({info:e}),E()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),E()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),E()}get max_lines(){return this.$$.ctx[9]}set max_lines(e){this.$$set({max_lines:e}),E()}get submit_btn(){return this.$$.ctx[10]}set submit_btn(e){this.$$set({submit_btn:e}),E()}get stop_btn(){return this.$$.ctx[11]}set stop_btn(e){this.$$set({stop_btn:e}),E()}get rtl(){return this.$$.ctx[12]}set rtl(e){this.$$set({rtl:e}),E()}get autofocus(){return this.$$.ctx[13]}set autofocus(e){this.$$set({autofocus:e}),E()}get text_align(){return this.$$.ctx[14]}set text_align(e){this.$$set({text_align:e}),E()}get autoscroll(){return this.$$.ctx[39]}set autoscroll(e){this.$$set({autoscroll:e}),E()}get root(){return this.$$.ctx[15]}set root(e){this.$$set({root:e}),E()}get file_types(){return this.$$.ctx[16]}set file_types(e){this.$$set({file_types:e}),E()}get max_file_size(){return this.$$.ctx[17]}set max_file_size(e){this.$$set({max_file_size:e}),E()}get upload(){return this.$$.ctx[18]}set upload(e){this.$$set({upload:e}),E()}get stream_handler(){return this.$$.ctx[19]}set stream_handler(e){this.$$set({stream_handler:e}),E()}get file_count(){return this.$$.ctx[20]}set file_count(e){this.$$set({file_count:e}),E()}get max_plain_text_length(){return this.$$.ctx[40]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),E()}get dragging(){return this.$$.ctx[1]}set dragging(e){this.$$set({dragging:e}),E()}}const el=$t,{SvelteComponent:tl,add_flush_callback:De,assign:ll,bind:Be,binding_callbacks:Me,check_outros:il,create_component:qe,destroy_component:Ue,detach:nl,flush:B,get_spread_object:sl,get_spread_update:ol,group_outros:ul,init:al,insert:rl,mount_component:Ve,safe_not_equal:fl,space:_l,transition_in:se,transition_out:ce}=window.__gradio__svelte__internal;function Xe(l){let e,t;const i=[{autoscroll:l[2].autoscroll},{i18n:l[2].i18n},l[18]];let u={};for(let o=0;o<i.length;o+=1)u=ll(u,i[o]);return e=new wt({props:u}),e.$on("clear_status",l[28]),{c(){qe(e.$$.fragment)},m(o,c){Ve(e,o,c),t=!0},p(o,c){const f=c[0]&262148?ol(i,[c[0]&4&&{autoscroll:o[2].autoscroll},c[0]&4&&{i18n:o[2].i18n},c[0]&262144&&sl(o[18])]):{};e.$set(f)},i(o){t||(se(e.$$.fragment,o),t=!0)},o(o){ce(e.$$.fragment,o),t=!1},d(o){Ue(e,o)}}}function cl(l){let e,t,i,u,o,c,f=l[18]&&Xe(l);function s(r){l[31](r)}function g(r){l[32](r)}function a(r){l[33](r)}let k={file_types:l[6],root:l[24],label:l[9],info:l[10],show_label:l[11],lines:l[7],rtl:l[19],text_align:l[20],max_lines:l[12]?l[12]:l[7]+1,placeholder:l[8],submit_btn:l[16],stop_btn:l[17],autofocus:l[21],container:l[13],autoscroll:l[22],file_count:l[25],max_file_size:l[2].max_file_size,disabled:!l[23],upload:l[29],stream_handler:l[30],max_plain_text_length:l[26]};return l[0]!==void 0&&(k.value=l[0]),l[1]!==void 0&&(k.value_is_output=l[1]),l[27]!==void 0&&(k.dragging=l[27]),t=new el({props:k}),Me.push(()=>Be(t,"value",s)),Me.push(()=>Be(t,"value_is_output",g)),Me.push(()=>Be(t,"dragging",a)),t.$on("change",l[34]),t.$on("input",l[35]),t.$on("submit",l[36]),t.$on("stop",l[37]),t.$on("blur",l[38]),t.$on("select",l[39]),t.$on("focus",l[40]),t.$on("error",l[41]),{c(){f&&f.c(),e=_l(),qe(t.$$.fragment)},m(r,d){f&&f.m(r,d),rl(r,e,d),Ve(t,r,d),c=!0},p(r,d){r[18]?f?(f.p(r,d),d[0]&262144&&se(f,1)):(f=Xe(r),f.c(),se(f,1),f.m(e.parentNode,e)):f&&(ul(),ce(f,1,1,()=>{f=null}),il());const h={};d[0]&64&&(h.file_types=r[6]),d[0]&16777216&&(h.root=r[24]),d[0]&512&&(h.label=r[9]),d[0]&1024&&(h.info=r[10]),d[0]&2048&&(h.show_label=r[11]),d[0]&128&&(h.lines=r[7]),d[0]&524288&&(h.rtl=r[19]),d[0]&1048576&&(h.text_align=r[20]),d[0]&4224&&(h.max_lines=r[12]?r[12]:r[7]+1),d[0]&256&&(h.placeholder=r[8]),d[0]&65536&&(h.submit_btn=r[16]),d[0]&131072&&(h.stop_btn=r[17]),d[0]&2097152&&(h.autofocus=r[21]),d[0]&8192&&(h.container=r[13]),d[0]&4194304&&(h.autoscroll=r[22]),d[0]&33554432&&(h.file_count=r[25]),d[0]&4&&(h.max_file_size=r[2].max_file_size),d[0]&8388608&&(h.disabled=!r[23]),d[0]&4&&(h.upload=r[29]),d[0]&4&&(h.stream_handler=r[30]),d[0]&67108864&&(h.max_plain_text_length=r[26]),!i&&d[0]&1&&(i=!0,h.value=r[0],De(()=>i=!1)),!u&&d[0]&2&&(u=!0,h.value_is_output=r[1],De(()=>u=!1)),!o&&d[0]&134217728&&(o=!0,h.dragging=r[27],De(()=>o=!1)),t.$set(h)},i(r){c||(se(f),se(t.$$.fragment,r),c=!0)},o(r){ce(f),ce(t.$$.fragment,r),c=!1},d(r){r&&nl(e),f&&f.d(r),Ue(t,r)}}}function gl(l){let e,t;return e=new kt({props:{visible:l[5],elem_id:l[3],elem_classes:[...l[4],"multimodal-textbox"],scale:l[14],min_width:l[15],allow_overflow:!1,padding:!1,border_mode:l[27]?"focus":"base",$$slots:{default:[cl]},$$scope:{ctx:l}}}),{c(){qe(e.$$.fragment)},m(i,u){Ve(e,i,u),t=!0},p(i,u){const o={};u[0]&32&&(o.visible=i[5]),u[0]&8&&(o.elem_id=i[3]),u[0]&16&&(o.elem_classes=[...i[4],"multimodal-textbox"]),u[0]&16384&&(o.scale=i[14]),u[0]&32768&&(o.min_width=i[15]),u[0]&134217728&&(o.border_mode=i[27]?"focus":"base"),u[0]&268386247|u[1]&2048&&(o.$$scope={dirty:u,ctx:i}),e.$set(o)},i(i){t||(se(e.$$.fragment,i),t=!0)},o(i){ce(e.$$.fragment,i),t=!1},d(i){Ue(e,i)}}}function hl(l,e,t){let{gradio:i}=e,{elem_id:u=""}=e,{elem_classes:o=[]}=e,{visible:c=!0}=e,{value:f={text:"",files:[]}}=e,{file_types:s=null}=e,{lines:g}=e,{placeholder:a=""}=e,{label:k="MultimodalTextbox"}=e,{info:r=void 0}=e,{show_label:d}=e,{max_lines:h}=e,{container:p=!0}=e,{scale:C=null}=e,{min_width:w=void 0}=e,{submit_btn:b=null}=e,{stop_btn:v=null}=e,{loading_status:S=void 0}=e,{value_is_output:m=!1}=e,{rtl:z=!1}=e,{text_align:I=void 0}=e,{autofocus:ue=!1}=e,{autoscroll:ee=!0}=e,{interactive:K}=e,{root:Y}=e,{file_count:F}=e,{max_plain_text_length:te}=e,le;const ae=()=>i.dispatch("clear_status",S),N=(..._)=>i.client.upload(..._),re=(..._)=>i.client.stream(..._);function fe(_){f=_,t(0,f)}function ie(_){m=_,t(1,m)}function V(_){le=_,t(27,le)}const be=()=>i.dispatch("change",f),ge=()=>i.dispatch("input"),pe=()=>i.dispatch("submit"),ke=()=>i.dispatch("stop"),we=()=>i.dispatch("blur"),ve=_=>i.dispatch("select",_.detail),he=()=>i.dispatch("focus"),ze=({detail:_})=>{i.dispatch("error",_)};return l.$$set=_=>{"gradio"in _&&t(2,i=_.gradio),"elem_id"in _&&t(3,u=_.elem_id),"elem_classes"in _&&t(4,o=_.elem_classes),"visible"in _&&t(5,c=_.visible),"value"in _&&t(0,f=_.value),"file_types"in _&&t(6,s=_.file_types),"lines"in _&&t(7,g=_.lines),"placeholder"in _&&t(8,a=_.placeholder),"label"in _&&t(9,k=_.label),"info"in _&&t(10,r=_.info),"show_label"in _&&t(11,d=_.show_label),"max_lines"in _&&t(12,h=_.max_lines),"container"in _&&t(13,p=_.container),"scale"in _&&t(14,C=_.scale),"min_width"in _&&t(15,w=_.min_width),"submit_btn"in _&&t(16,b=_.submit_btn),"stop_btn"in _&&t(17,v=_.stop_btn),"loading_status"in _&&t(18,S=_.loading_status),"value_is_output"in _&&t(1,m=_.value_is_output),"rtl"in _&&t(19,z=_.rtl),"text_align"in _&&t(20,I=_.text_align),"autofocus"in _&&t(21,ue=_.autofocus),"autoscroll"in _&&t(22,ee=_.autoscroll),"interactive"in _&&t(23,K=_.interactive),"root"in _&&t(24,Y=_.root),"file_count"in _&&t(25,F=_.file_count),"max_plain_text_length"in _&&t(26,te=_.max_plain_text_length)},[f,m,i,u,o,c,s,g,a,k,r,d,h,p,C,w,b,v,S,z,I,ue,ee,K,Y,F,te,le,ae,N,re,fe,ie,V,be,ge,pe,ke,we,ve,he,ze]}class Pl extends tl{constructor(e){super(),al(this,e,hl,gl,fl,{gradio:2,elem_id:3,elem_classes:4,visible:5,value:0,file_types:6,lines:7,placeholder:8,label:9,info:10,show_label:11,max_lines:12,container:13,scale:14,min_width:15,submit_btn:16,stop_btn:17,loading_status:18,value_is_output:1,rtl:19,text_align:20,autofocus:21,autoscroll:22,interactive:23,root:24,file_count:25,max_plain_text_length:26},null,[-1,-1])}get gradio(){return this.$$.ctx[2]}set gradio(e){this.$$set({gradio:e}),B()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),B()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),B()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),B()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),B()}get file_types(){return this.$$.ctx[6]}set file_types(e){this.$$set({file_types:e}),B()}get lines(){return this.$$.ctx[7]}set lines(e){this.$$set({lines:e}),B()}get placeholder(){return this.$$.ctx[8]}set placeholder(e){this.$$set({placeholder:e}),B()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),B()}get info(){return this.$$.ctx[10]}set info(e){this.$$set({info:e}),B()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),B()}get max_lines(){return this.$$.ctx[12]}set max_lines(e){this.$$set({max_lines:e}),B()}get container(){return this.$$.ctx[13]}set container(e){this.$$set({container:e}),B()}get scale(){return this.$$.ctx[14]}set scale(e){this.$$set({scale:e}),B()}get min_width(){return this.$$.ctx[15]}set min_width(e){this.$$set({min_width:e}),B()}get submit_btn(){return this.$$.ctx[16]}set submit_btn(e){this.$$set({submit_btn:e}),B()}get stop_btn(){return this.$$.ctx[17]}set stop_btn(e){this.$$set({stop_btn:e}),B()}get loading_status(){return this.$$.ctx[18]}set loading_status(e){this.$$set({loading_status:e}),B()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),B()}get rtl(){return this.$$.ctx[19]}set rtl(e){this.$$set({rtl:e}),B()}get text_align(){return this.$$.ctx[20]}set text_align(e){this.$$set({text_align:e}),B()}get autofocus(){return this.$$.ctx[21]}set autofocus(e){this.$$set({autofocus:e}),B()}get autoscroll(){return this.$$.ctx[22]}set autoscroll(e){this.$$set({autoscroll:e}),B()}get interactive(){return this.$$.ctx[23]}set interactive(e){this.$$set({interactive:e}),B()}get root(){return this.$$.ctx[24]}set root(e){this.$$set({root:e}),B()}get file_count(){return this.$$.ctx[25]}set file_count(e){this.$$set({file_count:e}),B()}get max_plain_text_length(){return this.$$.ctx[26]}set max_plain_text_length(e){this.$$set({max_plain_text_length:e}),B()}}export{Nl as BaseExample,el as BaseMultimodalTextbox,Pl as default};
//# sourceMappingURL=Index-BouwDkji.js.map
