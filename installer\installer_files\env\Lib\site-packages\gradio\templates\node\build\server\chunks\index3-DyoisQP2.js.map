{"version": 3, "file": "index3-DyoisQP2.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index3.js"], "sourcesContent": ["class HttpError {\n  /**\n   * @param {number} status\n   * @param {{message: string} extends App.Error ? (App.Error | string | undefined) : App.Error} body\n   */\n  constructor(status, body) {\n    this.status = status;\n    if (typeof body === \"string\") {\n      this.body = { message: body };\n    } else if (body) {\n      this.body = body;\n    } else {\n      this.body = { message: `Error: ${status}` };\n    }\n  }\n  toString() {\n    return JSON.stringify(this.body);\n  }\n}\nclass Redirect {\n  /**\n   * @param {300 | 301 | 302 | 303 | 304 | 305 | 306 | 307 | 308} status\n   * @param {string} location\n   */\n  constructor(status, location) {\n    this.status = status;\n    this.location = location;\n  }\n}\nclass SvelteKitError extends Error {\n  /**\n   * @param {number} status\n   * @param {string} text\n   * @param {string} message\n   */\n  constructor(status, text2, message) {\n    super(message);\n    this.status = status;\n    this.text = text2;\n  }\n}\nclass ActionFailure {\n  /**\n   * @param {number} status\n   * @param {T} data\n   */\n  constructor(status, data) {\n    this.status = status;\n    this.data = data;\n  }\n}\nfunction redirect(status, location) {\n  if (isNaN(status) || status < 300 || status > 308) {\n    throw new Error(\"Invalid status code\");\n  }\n  throw new Redirect(\n    // @ts-ignore\n    status,\n    location.toString()\n  );\n}\nfunction json(data, init) {\n  const body = JSON.stringify(data);\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    headers.set(\"content-length\", encoder.encode(body).byteLength.toString());\n  }\n  if (!headers.has(\"content-type\")) {\n    headers.set(\"content-type\", \"application/json\");\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nconst encoder = new TextEncoder();\nfunction text(body, init) {\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    const encoded = encoder.encode(body);\n    headers.set(\"content-length\", encoded.byteLength.toString());\n    return new Response(encoded, {\n      ...init,\n      headers\n    });\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nexport {\n  ActionFailure as A,\n  HttpError as H,\n  Redirect as R,\n  SvelteKitError as S,\n  json as j,\n  redirect as r,\n  text as t\n};\n"], "names": [], "mappings": "AAAA,MAAM,SAAS,CAAC;AAChB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACpC,KAAK,MAAM,IAAI,IAAI,EAAE;AACrB,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;AAClD,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrC,GAAG;AACH,CAAC;AACD,MAAM,QAAQ,CAAC;AACf;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH,CAAC;AACD,MAAM,cAAc,SAAS,KAAK,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE;AACtC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AACtB,GAAG;AACH,CAAC;AACD,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE;AACpC,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,EAAE;AACrD,IAAI,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,MAAM,IAAI,QAAQ;AACpB;AACA,IAAI,MAAM;AACV,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACvB,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9E,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AAClC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjE,IAAI,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;AACjC,MAAM,GAAG,IAAI;AACb,MAAM,OAAO;AACb,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL;;;;"}