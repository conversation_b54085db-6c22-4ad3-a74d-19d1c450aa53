{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "b1-c1oAXiIzN"}, "outputs": [], "source": []}, {"cell_type": "code", "source": ["import torch\n", "print(\"CUDA available:\", torch.cuda.is_available())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OB-9x7rPjMWk", "outputId": "9b1745aa-a18a-4219-be42-7d98e1854f73"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["CUDA available: True\n"]}]}, {"cell_type": "code", "source": ["import shutil\n", "import os\n", "from google.colab import drive\n", "\n", "# Check if the user wants to flush the existing repository folder\n", "flush_repo = input(\"Do you want to flush the existing repository and start fresh? (yes/no): \").strip().lower()\n", "\n", "if flush_repo == 'yes':\n", "    # Clear the repository folder to reset locally\n", "    print(\"Flushing the repository folder locally...\")\n", "    !rm -rf /content/ROOP-FLOYD\n", "\n", "    # Also check if Google Drive is mounted and remove the repo there\n", "    if os.path.exists('/content/drive'):\n", "        delete_drive_repo = input(\"Do you also want to delete the repository from Google Drive? (yes/no): \").strip().lower()\n", "        if delete_drive_repo == 'yes':\n", "            # Check if the folder exists in Google Drive and remove it\n", "            drive_repo_path = '/content/drive/MyDrive/ROOP-FLOYD'\n", "            if os.path.exists(drive_repo_path):\n", "                print(\"Deleting the repository from Google Drive...\")\n", "                shutil.rmtree(drive_repo_path)\n", "            else:\n", "                print(\"No repository found on Google Drive.\")\n", "else:\n", "    print(\"Keeping the existing repository folder.\")\n", "\n", "# Clone the repository and install dependencies (will redownload if flushed)\n", "!git clone https://codeberg.org/Cognibuild/ROOP-FLOYD.git\n", "%cd ROOP-FLOYD\n", "\n", "!pip install -r requirements.txt\n", "!pip install --upgrade gradio --force\n", "!pip install --upgrade fastapi pydantic\n", "!pip install \"numpy<2.0\"\n", "\n", "# Check if Google Drive is already mounted\n", "if not os.path.exists('/content/drive'):\n", "    # Prompt user to choose whether they want to save to Google Drive\n", "    save_to_drive = input(\"Do you want to save the repository to Google Drive? (yes/no): \").strip().lower()\n", "\n", "    if save_to_drive == 'yes':\n", "        drive.mount('/content/drive')\n", "\n", "        # Copy repository to Google Drive\n", "        !cp -r /content/ROOP-FLOYD /content/drive/MyDrive/ROOP-FLOYD\n", "        print(\"Repository saved to Google Drive.\")\n", "else:\n", "    print(\"Google Drive is already mounted.\")\n", "\n", "# Run the main script\n", "!python run.py\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "sMVmUDr3iUsK", "outputId": "288476a8-82ec-4b0d-fdc8-cb780855c9a3"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Do you want to flush the existing repository and start fresh? (yes/no): no\n", "Keeping the existing repository folder.\n", "fatal: destination path 'ROOP-FLOYD' already exists and is not an empty directory.\n", "/content/ROOP-FLOYD\n", "Looking in indexes: https://pypi.org/simple, https://download.pytorch.org/whl/cu124\n", "Ignoring torch: markers 'sys_platform == \"darwin\"' don't match your environment\n", "Ignoring torchvision: markers 'sys_platform == \"darwin\"' don't match your environment\n", "Ignoring onnxruntime: markers 'sys_platform == \"darwin\" and platform_machine != \"arm64\"' don't match your environment\n", "Ignoring onnxruntime-silicon: markers 'sys_platform == \"darwin\" and platform_machine == \"arm64\"' don't match your environment\n", "Requirement already satisfied: numpy==1.26.4 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 2)) (1.26.4)\n", "Collecting gradio==5.9.1 (from -r requirements.txt (line 3))\n", "  Using cached gradio-5.9.1-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: opencv-python-headless==4.10.0.84 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 4)) (4.10.0.84)\n", "Requirement already satisfied: onnx==1.16.1 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 5)) (1.16.1)\n", "Requirement already satisfied: insightface==0.7.3 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 6)) (0.7.3)\n", "Requirement already satisfied: albucore==0.0.16 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 7)) (0.0.16)\n", "Requirement already satisfied: psutil==5.9.6 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 8)) (5.9.6)\n", "Requirement already satisfied: torch==2.5.1+cu124 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 9)) (2.5.1+cu124)\n", "Requirement already satisfied: torchvision==0.20.1+cu124 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 11)) (0.20.1+cu124)\n", "Requirement already satisfied: onnxruntime-gpu==1.20.1 in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 15)) (1.20.1)\n", "Collecting tqdm==4.66.4 (from -r requirements.txt (line 16))\n", "  Using cached tqdm-4.66.4-py3-none-any.whl.metadata (57 kB)\n", "Requirement already satisfied: ftfy in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 17)) (6.3.1)\n", "Requirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 18)) (2024.11.6)\n", "Requirement already satisfied: pyvirtualcam in /usr/local/lib/python3.11/dist-packages (from -r requirements.txt (line 19)) (0.12.1)\n", "Requirement already satisfied: aiofiles<24.0,>=22.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (23.2.1)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (4.8.0)\n", "Requirement already satisfied: fastapi<1.0,>=0.115.2 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.115.7)\n", "Requirement already satisfied: ffmpy in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.5.0)\n", "Collecting gradio-client==1.5.2 (from gradio==5.9.1->-r requirements.txt (line 3))\n", "  Using cached gradio_client-1.5.2-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: httpx>=0.24.1 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.28.1)\n", "Requirement already satisfied: huggingface-hub>=0.25.1 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.27.1)\n", "Requirement already satisfied: jinja2<4.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (3.1.5)\n", "Requirement already satisfied: markupsafe~=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.1.5)\n", "Requirement already satisfied: orjson~=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (3.10.15)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (24.2)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.2.3)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (11.1.0)\n", "Requirement already satisfied: pydantic>=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.10.6)\n", "Requirement already satisfied: pydub in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.25.1)\n", "Requirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.0.20)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (6.0.2)\n", "Requirement already satisfied: ruff>=0.2.2 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.9.3)\n", "Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.1.6)\n", "Requirement already satisfied: semantic-version~=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (2.10.0)\n", "Requirement already satisfied: starlette<1.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.45.3)\n", "Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.13.2)\n", "Requirement already satisfied: typer<1.0,>=0.12 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.15.1)\n", "Requirement already satisfied: typing-extensions~=4.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (4.12.2)\n", "Requirement already satisfied: uvicorn>=0.14.0 in /usr/local/lib/python3.11/dist-packages (from gradio==5.9.1->-r requirements.txt (line 3)) (0.34.0)\n", "Requirement already satisfied: protobuf>=3.20.2 in /usr/local/lib/python3.11/dist-packages (from onnx==1.16.1->-r requirements.txt (line 5)) (4.25.5)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (2.32.3)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.10.0)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.13.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.6.1)\n", "Requirement already satisfied: scikit-image in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (0.25.0)\n", "Requirement already satisfied: easydict in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.13)\n", "Requirement already satisfied: cython in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.0.11)\n", "Requirement already satisfied: albumentations in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (1.4.15)\n", "Requirement already satisfied: prettytable in /usr/local/lib/python3.11/dist-packages (from insightface==0.7.3->-r requirements.txt (line 6)) (3.12.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.17.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.4.2)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (2024.12.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.3.1.170)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (12.4.127)\n", "Requirement already satisfied: triton==3.1.0 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (3.1.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch==2.5.1+cu124->-r requirements.txt (line 9)) (1.13.1)\n", "Requirement already satisfied: coloredlogs in /usr/local/lib/python3.11/dist-packages (from onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (15.0.1)\n", "Requirement already satisfied: flatbuffers in /usr/local/lib/python3.11/dist-packages (from onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (25.1.21)\n", "Requirement already satisfied: websockets<15.0,>=10.0 in /usr/local/lib/python3.11/dist-packages (from gradio-client==1.5.2->gradio==5.9.1->-r requirements.txt (line 3)) (14.2)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch==2.5.1+cu124->-r requirements.txt (line 9)) (1.3.0)\n", "Requirement already satisfied: wcwidth in /usr/local/lib/python3.11/dist-packages (from ftfy->-r requirements.txt (line 17)) (0.2.13)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio==5.9.1->-r requirements.txt (line 3)) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio==5.9.1->-r requirements.txt (line 3)) (1.3.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (2024.12.14)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.24.1->gradio==5.9.1->-r requirements.txt (line 3)) (0.14.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (2025.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->gradio==5.9.1->-r requirements.txt (line 3)) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.0->gradio==5.9.1->-r requirements.txt (line 3)) (2.27.2)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (13.9.4)\n", "Requirement already satisfied: eval-type-backport in /usr/local/lib/python3.11/dist-packages (from albumentations->insightface==0.7.3->-r requirements.txt (line 6)) (0.2.2)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (2.36.1)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (2025.1.10)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.11/dist-packages (from scikit-image->insightface==0.7.3->-r requirements.txt (line 6)) (0.4)\n", "Requirement already satisfied: humanfriendly>=9.1 in /usr/local/lib/python3.11/dist-packages (from coloredlogs->onnxruntime-gpu==1.20.1->-r requirements.txt (line 15)) (10.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (4.55.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (1.4.8)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->insightface==0.7.3->-r requirements.txt (line 6)) (3.2.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->insightface==0.7.3->-r requirements.txt (line 6)) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->insightface==0.7.3->-r requirements.txt (line 6)) (2.3.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->insightface==0.7.3->-r requirements.txt (line 6)) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->insightface==0.7.3->-r requirements.txt (line 6)) (3.5.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio==5.9.1->-r requirements.txt (line 3)) (1.17.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (2.19.1)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio==5.9.1->-r requirements.txt (line 3)) (0.1.2)\n", "Using cached gradio-5.9.1-py3-none-any.whl (57.2 MB)\n", "Using cached tqdm-4.66.4-py3-none-any.whl (78 kB)\n", "Using cached gradio_client-1.5.2-py3-none-any.whl (320 kB)\n", "Installing collected packages: tqdm, gradio-client, gradio\n", "  Attempting uninstall: tqdm\n", "    Found existing installation: tqdm 4.67.1\n", "    Uninstalling tqdm-4.67.1:\n", "      Successfully uninstalled tqdm-4.67.1\n", "  Attempting uninstall: gradio-client\n", "    Found existing installation: gradio_client 1.6.0\n", "    Uninstalling gradio_client-1.6.0:\n", "      Successfully uninstalled gradio_client-1.6.0\n", "  Attempting uninstall: gradio\n", "    Found existing installation: gradio 5.13.1\n", "    Uninstalling gradio-5.13.1:\n", "      Successfully uninstalled gradio-5.13.1\n", "Successfully installed gradio-5.9.1 gradio-client-1.5.2 tqdm-4.66.4\n", "Collecting gradio\n", "  Using cached gradio-5.13.1-py3-none-any.whl.metadata (16 kB)\n", "Collecting aiofiles<24.0,>=22.0 (from gradio)\n", "  Using cached aiofiles-23.2.1-py3-none-any.whl.metadata (9.7 kB)\n", "Collecting anyio<5.0,>=3.0 (from gradio)\n", "  Using cached anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting fastapi<1.0,>=0.115.2 (from gradio)\n", "  Using cached fastapi-0.115.7-py3-none-any.whl.metadata (27 kB)\n", "Collecting ffmpy (from gradio)\n", "  Using cached ffmpy-0.5.0-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting gradio-client==1.6.0 (from gradio)\n", "  Using cached gradio_client-1.6.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting httpx>=0.24.1 (from gradio)\n", "  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting huggingface-hub>=0.25.1 (from gradio)\n", "  Using cached huggingface_hub-0.27.1-py3-none-any.whl.metadata (13 kB)\n", "Collecting jinja2<4.0 (from gradio)\n", "  Using cached jinja2-3.1.5-py3-none-any.whl.metadata (2.6 kB)\n", "Collecting markupsafe~=2.0 (from gradio)\n", "  Using cached MarkupSafe-2.1.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.0 kB)\n", "Collecting numpy<3.0,>=1.0 (from gradio)\n", "  Using cached numpy-2.2.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)\n", "Collecting orjson~=3.0 (from gradio)\n", "  Using cached orjson-3.10.15-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (41 kB)\n", "Collecting packaging (from gradio)\n", "  Using cached packaging-24.2-py3-none-any.whl.metadata (3.2 kB)\n", "Collecting pandas<3.0,>=1.0 (from gradio)\n", "  Using cached pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (89 kB)\n", "Collecting pillow<12.0,>=8.0 (from gradio)\n", "  Using cached pillow-11.1.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (9.1 kB)\n", "Collecting pydantic>=2.0 (from gradio)\n", "  Using cached pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)\n", "Collecting pydub (from gradio)\n", "  Using cached pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting python-multipart>=0.0.18 (from gradio)\n", "  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting pyyaml<7.0,>=5.0 (from gradio)\n", "  Using cached PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)\n", "Collecting ruff>=0.2.2 (from gradio)\n", "  Using cached ruff-0.9.3-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (25 kB)\n", "Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)\n", "  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting semantic-version~=2.0 (from gradio)\n", "  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Collecting starlette<1.0,>=0.40.0 (from gradio)\n", "  Using cached starlette-0.45.3-py3-none-any.whl.metadata (6.3 kB)\n", "Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)\n", "  Using cached tomlkit-0.13.2-py3-none-any.whl.metadata (2.7 kB)\n", "Collecting typer<1.0,>=0.12 (from gradio)\n", "  Using cached typer-0.15.1-py3-none-any.whl.metadata (15 kB)\n", "Collecting typing-extensions~=4.0 (from gradio)\n", "  Using cached typing_extensions-4.12.2-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting uvicorn>=0.14.0 (from gradio)\n", "  Using cached uvicorn-0.34.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting fsspec (from gradio-client==1.6.0->gradio)\n", "  Using cached fsspec-2024.12.0-py3-none-any.whl.metadata (11 kB)\n", "Collecting websockets<15.0,>=10.0 (from gradio-client==1.6.0->gradio)\n", "  Using cached websockets-14.2-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Collecting idna>=2.8 (from anyio<5.0,>=3.0->gradio)\n", "  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting sniffio>=1.1 (from anyio<5.0,>=3.0->gradio)\n", "  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)\n", "Collecting certifi (from httpx>=0.24.1->gradio)\n", "  Using cached certifi-2024.12.14-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting httpcore==1.* (from httpx>=0.24.1->gradio)\n", "  Using cached httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx>=0.24.1->gradio)\n", "  Using cached h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)\n", "Collecting filelock (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached filelock-3.17.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting requests (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached requests-2.32.3-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting tqdm>=4.42.1 (from huggingface-hub>=0.25.1->gradio)\n", "  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)\n", "Collecting python-dateutil>=2.8.2 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)\n", "Collecting pytz>=2020.1 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached pytz-2024.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas<3.0,>=1.0->gradio)\n", "  Using cached tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic>=2.0->gradio)\n", "  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting pydantic-core==2.27.2 (from pydantic>=2.0->gradio)\n", "  Using cached pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.6 kB)\n", "Collecting click>=8.0.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached click-8.1.8-py3-none-any.whl.metadata (2.3 kB)\n", "Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting rich>=10.11.0 (from typer<1.0,>=0.12->gradio)\n", "  Using cached rich-13.9.4-py3-none-any.whl.metadata (18 kB)\n", "Collecting six>=1.5 (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio)\n", "  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)\n", "Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio)\n", "  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Collecting pygments<3.0.0,>=2.13.0 (from rich>=10.11.0->typer<1.0,>=0.12->gradio)\n", "  Using cached pygments-2.19.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting charset-normalizer<4,>=2 (from requests->huggingface-hub>=0.25.1->gradio)\n", "  Using cached charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (35 kB)\n", "Collecting urllib3<3,>=1.21.1 (from requests->huggingface-hub>=0.25.1->gradio)\n", "  Using cached urllib3-2.3.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio)\n", "  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Using cached gradio-5.13.1-py3-none-any.whl (57.6 MB)\n", "Using cached gradio_client-1.6.0-py3-none-any.whl (321 kB)\n", "Using cached aiofiles-23.2.1-py3-none-any.whl (15 kB)\n", "Using cached anyio-4.8.0-py3-none-any.whl (96 kB)\n", "Using cached fastapi-0.115.7-py3-none-any.whl (94 kB)\n", "Using cached httpx-0.28.1-py3-none-any.whl (73 kB)\n", "Using cached httpcore-1.0.7-py3-none-any.whl (78 kB)\n", "Using cached huggingface_hub-0.27.1-py3-none-any.whl (450 kB)\n", "Using cached jinja2-3.1.5-py3-none-any.whl (134 kB)\n", "Using cached MarkupSafe-2.1.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (28 kB)\n", "Using cached numpy-2.2.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.4 MB)\n", "Using cached orjson-3.10.15-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (130 kB)\n", "Using cached packaging-24.2-py3-none-any.whl (65 kB)\n", "Using cached pandas-2.2.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (13.1 MB)\n", "Using cached pillow-11.1.0-cp311-cp311-manylinux_2_28_x86_64.whl (4.5 MB)\n", "Using cached pydantic-2.10.6-py3-none-any.whl (431 kB)\n", "Using cached pydantic_core-2.27.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)\n", "Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)\n", "Using cached PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)\n", "Using cached ruff-0.9.3-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (12.4 MB)\n", "Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)\n", "Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)\n", "Using cached starlette-0.45.3-py3-none-any.whl (71 kB)\n", "Using cached tomlkit-0.13.2-py3-none-any.whl (37 kB)\n", "Using cached typer-0.15.1-py3-none-any.whl (44 kB)\n", "Using cached typing_extensions-4.12.2-py3-none-any.whl (37 kB)\n", "Using cached uvicorn-0.34.0-py3-none-any.whl (62 kB)\n", "Using cached ffmpy-0.5.0-py3-none-any.whl (6.0 kB)\n", "Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Using cached click-8.1.8-py3-none-any.whl (98 kB)\n", "Using cached fsspec-2024.12.0-py3-none-any.whl (183 kB)\n", "Using cached h11-0.14.0-py3-none-any.whl (58 kB)\n", "Using cached idna-3.10-py3-none-any.whl (70 kB)\n", "Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)\n", "Using cached pytz-2024.2-py2.py3-none-any.whl (508 kB)\n", "Using cached rich-13.9.4-py3-none-any.whl (242 kB)\n", "Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)\n", "Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)\n", "Using cached tzdata-2025.1-py2.py3-none-any.whl (346 kB)\n", "Using cached websockets-14.2-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (169 kB)\n", "Using cached certifi-2024.12.14-py3-none-any.whl (164 kB)\n", "Using cached filelock-3.17.0-py3-none-any.whl (16 kB)\n", "Using cached requests-2.32.3-py3-none-any.whl (64 kB)\n", "Using cached charset_normalizer-3.4.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (143 kB)\n", "Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Using cached pygments-2.19.1-py3-none-any.whl (1.2 MB)\n", "Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)\n", "Using cached urllib3-2.3.0-py3-none-any.whl (128 kB)\n", "Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Installing collected packages: pytz, pydub, websockets, urllib3, tzdata, typing-extensions, tqdm, tomlkit, sniffio, six, shellingham, semantic-version, ruff, pyyaml, python-multipart, pygments, pillow, packaging, orjson, numpy, mdurl, markupsafe, idna, h11, fsspec, filelock, ffmpy, click, charset-normalizer, certifi, annotated-types, aiofiles, uvicorn, requests, python-dateutil, pydantic-core, markdown-it-py, jinja2, httpcore, anyio, starlette, rich, pydantic, pandas, huggingface-hub, httpx, typer, safehttpx, gradio-client, fastapi, gradio\n", "  Attempting uninstall: pytz\n", "    Found existing installation: pytz 2024.2\n", "    Uninstalling pytz-2024.2:\n", "      Successfully uninstalled pytz-2024.2\n", "  Attempting uninstall: pydub\n", "    Found existing installation: pydub 0.25.1\n", "    Uninstalling pydub-0.25.1:\n", "      Successfully uninstalled pydub-0.25.1\n", "  Attempting uninstall: websockets\n", "    Found existing installation: websockets 14.2\n", "    Uninstalling websockets-14.2:\n", "      Successfully uninstalled websockets-14.2\n", "  Attempting uninstall: urllib3\n", "    Found existing installation: urllib3 2.3.0\n", "    Uninstalling urllib3-2.3.0:\n", "      Successfully uninstalled urllib3-2.3.0\n", "  Attempting uninstall: t<PERSON><PERSON>\n", "    Found existing installation: tzdata 2025.1\n", "    Uninstalling tzdata-2025.1:\n", "      Successfully uninstalled tzdata-2025.1\n", "  Attempting uninstall: typing-extensions\n", "    Found existing installation: typing_extensions 4.12.2\n", "    Uninstalling typing_extensions-4.12.2:\n", "      Successfully uninstalled typing_extensions-4.12.2\n", "  Attempting uninstall: tqdm\n", "    Found existing installation: tqdm 4.66.4\n", "    Uninstalling tqdm-4.66.4:\n", "      Successfully uninstalled tqdm-4.66.4\n", "  Attempting uninstall: to<PERSON><PERSON><PERSON>\n", "    Found existing installation: tomlkit 0.13.2\n", "    Uninstalling tomlkit-0.13.2:\n", "      Successfully uninstalled tomlkit-0.13.2\n", "  Attempting uninstall: sniffio\n", "    Found existing installation: sniffio 1.3.1\n", "    Uninstalling sniffio-1.3.1:\n", "      Successfully uninstalled sniffio-1.3.1\n", "  Attempting uninstall: six\n", "    Found existing installation: six 1.17.0\n", "    Uninstalling six-1.17.0:\n", "      Successfully uninstalled six-1.17.0\n", "  Attempting uninstall: shellingham\n", "    Found existing installation: shellingham 1.5.4\n", "    Uninstalling shellingham-1.5.4:\n", "      Successfully uninstalled shellingham-1.5.4\n", "  Attempting uninstall: semantic-version\n", "    Found existing installation: semantic-version 2.10.0\n", "    Uninstalling semantic-version-2.10.0:\n", "      Successfully uninstalled semantic-version-2.10.0\n", "  Attempting uninstall: ruff\n", "    Found existing installation: ruff 0.9.3\n", "    Uninstalling ruff-0.9.3:\n", "      Successfully uninstalled ruff-0.9.3\n", "  Attempting uninstall: pyyaml\n", "    Found existing installation: PyYAML 6.0.2\n", "    Uninstalling PyYAML-6.0.2:\n", "      Successfully uninstalled PyYAML-6.0.2\n", "  Attempting uninstall: python-multipart\n", "    Found existing installation: python-multipart 0.0.20\n", "    Uninstalling python-multipart-0.0.20:\n", "      Successfully uninstalled python-multipart-0.0.20\n", "  Attempting uninstall: pygments\n", "    Found existing installation: Pygments 2.19.1\n", "    Uninstalling Pygments-2.19.1:\n", "      Successfully uninstalled Pygments-2.19.1\n", "  Attempting uninstall: pillow\n", "    Found existing installation: pillow 11.1.0\n", "    Uninstalling pillow-11.1.0:\n", "      Successfully uninstalled pillow-11.1.0\n", "  Attempting uninstall: packaging\n", "    Found existing installation: packaging 24.2\n", "    Uninstalling packaging-24.2:\n", "      Successfully uninstalled packaging-24.2\n", "  Attempting uninstall: <PERSON><PERSON><PERSON>\n", "    Found existing installation: <PERSON><PERSON><PERSON> 3.10.15\n", "    Uninstalling orjson-3.10.15:\n", "      Successfully uninstalled orjson-3.10.15\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 1.26.4\n", "    Uninstalling numpy-1.26.4:\n", "      Successfully uninstalled numpy-1.26.4\n", "  Attempting uninstall: mdurl\n", "    Found existing installation: mdurl 0.1.2\n", "    Uninstalling mdurl-0.1.2:\n", "      Successfully uninstalled mdurl-0.1.2\n", "  Attempting uninstall: markups<PERSON>e\n", "    Found existing installation: MarkupSafe 2.1.5\n", "    Uninstalling MarkupSafe-2.1.5:\n", "      Successfully uninstalled MarkupSafe-2.1.5\n", "  Attempting uninstall: idna\n", "    Found existing installation: idna 3.10\n", "    Uninstalling idna-3.10:\n", "      Successfully uninstalled idna-3.10\n", "  Attempting uninstall: h11\n", "    Found existing installation: h11 0.14.0\n", "    Uninstalling h11-0.14.0:\n", "      Successfully uninstalled h11-0.14.0\n", "  Attempting uninstall: fsspec\n", "    Found existing installation: fsspec 2024.12.0\n", "    Uninstalling fsspec-2024.12.0:\n", "      Successfully uninstalled fsspec-2024.12.0\n", "  Attempting uninstall: filelock\n", "    Found existing installation: filelock 3.17.0\n", "    Uninstalling filelock-3.17.0:\n", "      Successfully uninstalled filelock-3.17.0\n", "  Attempting uninstall: ffmpy\n", "    Found existing installation: ffmpy 0.5.0\n", "    Uninstalling ffmpy-0.5.0:\n", "      Successfully uninstalled ffmpy-0.5.0\n", "  Attempting uninstall: click\n", "    Found existing installation: click 8.1.8\n", "    Uninstalling click-8.1.8:\n", "      Successfully uninstalled click-8.1.8\n", "  Attempting uninstall: charset-normalizer\n", "    Found existing installation: charset-normalizer 3.4.1\n", "    Uninstalling charset-normalizer-3.4.1:\n", "      Successfully uninstalled charset-normalizer-3.4.1\n", "  Attempting uninstall: certifi\n", "    Found existing installation: certifi 2024.12.14\n", "    Uninstalling certifi-2024.12.14:\n", "      Successfully uninstalled certifi-2024.12.14\n", "  Attempting uninstall: annotated-types\n", "    Found existing installation: annotated-types 0.7.0\n", "    Uninstalling annotated-types-0.7.0:\n", "      Successfully uninstalled annotated-types-0.7.0\n", "  Attempting uninstall: aiofiles\n", "    Found existing installation: aiofiles 23.2.1\n", "    Uninstalling aiofiles-23.2.1:\n", "      Successfully uninstalled aiofiles-23.2.1\n", "  Attempting uninstall: uvic<PERSON>\n", "    Found existing installation: uvicorn 0.34.0\n", "    Uninstalling uvicorn-0.34.0:\n", "      Successfully uninstalled uvicorn-0.34.0\n", "  Attempting uninstall: requests\n", "    Found existing installation: requests 2.32.3\n", "    Uninstalling requests-2.32.3:\n", "      Successfully uninstalled requests-2.32.3\n", "  Attempting uninstall: python-dateutil\n", "    Found existing installation: python-dateutil 2.9.0.post0\n", "    Uninstalling python-dateutil-2.9.0.post0:\n", "      Successfully uninstalled python-dateutil-2.9.0.post0\n", "  Attempting uninstall: pydantic-core\n", "    Found existing installation: pydantic_core 2.27.2\n", "    Uninstalling pydantic_core-2.27.2:\n", "      Successfully uninstalled pydantic_core-2.27.2\n", "  Attempting uninstall: markdown-it-py\n", "    Found existing installation: markdown-it-py 3.0.0\n", "    Uninstalling markdown-it-py-3.0.0:\n", "      Successfully uninstalled markdown-it-py-3.0.0\n", "  Attempting uninstall: jinja2\n", "    Found existing installation: Jinja2 3.1.5\n", "    Uninstalling Jinja2-3.1.5:\n", "      Successfully uninstalled Jinja2-3.1.5\n", "  Attempting uninstall: httpcore\n", "    Found existing installation: httpcore 1.0.7\n", "    Uninstalling httpcore-1.0.7:\n", "      Successfully uninstalled httpcore-1.0.7\n", "  Attempting uninstall: anyio\n", "    Found existing installation: anyio 4.8.0\n", "    Uninstalling anyio-4.8.0:\n", "      Successfully uninstalled anyio-4.8.0\n", "  Attempting uninstall: starlette\n", "    Found existing installation: starlette 0.45.3\n", "    Uninstalling starlette-0.45.3:\n", "      Successfully uninstalled starlette-0.45.3\n", "  Attempting uninstall: rich\n", "    Found existing installation: rich 13.9.4\n", "    Uninstalling rich-13.9.4:\n", "      Successfully uninstalled rich-13.9.4\n", "  Attempting uninstall: pydantic\n", "    Found existing installation: pydantic 2.10.6\n", "    Uninstalling pydantic-2.10.6:\n", "      Successfully uninstalled pydantic-2.10.6\n", "  Attempting uninstall: pandas\n", "    Found existing installation: pandas 2.2.3\n", "    Uninstalling pandas-2.2.3:\n", "      Successfully uninstalled pandas-2.2.3\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.27.1\n", "    Uninstalling huggingface-hub-0.27.1:\n", "      Successfully uninstalled huggingface-hub-0.27.1\n", "  Attempting uninstall: httpx\n", "    Found existing installation: httpx 0.28.1\n", "    Uninstalling httpx-0.28.1:\n", "      Successfully uninstalled httpx-0.28.1\n", "  Attempting uninstall: typer\n", "    Found existing installation: typer 0.15.1\n", "    Uninstalling typer-0.15.1:\n", "      Successfully uninstalled typer-0.15.1\n", "  Attempting uninstall: safehttpx\n", "    Found existing installation: safehttpx 0.1.6\n", "    Uninstalling safehttpx-0.1.6:\n", "      Successfully uninstalled safehttpx-0.1.6\n", "  Attempting uninstall: gradio-client\n", "    Found existing installation: gradio_client 1.5.2\n", "    Uninstalling gradio_client-1.5.2:\n", "      Successfully uninstalled gradio_client-1.5.2\n", "  Attempting uninstall: <PERSON><PERSON><PERSON>\n", "    Found existing installation: fastapi 0.115.7\n", "    Uninstalling fastapi-0.115.7:\n", "      Successfully uninstalled fastapi-0.115.7\n", "  Attempting uninstall: gradio\n", "    Found existing installation: gradio 5.9.1\n", "    Uninstalling gradio-5.9.1:\n", "      Successfully uninstalled gradio-5.9.1\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "ipython 7.34.0 requires jedi>=0.16, which is not installed.\n", "google-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.2.3 which is incompatible.\n", "gcsfs 2024.10.0 requires fsspec==2024.10.0, but you have fsspec 2024.12.0 which is incompatible.\n", "thinc 8.2.5 requires numpy<2.0.0,>=1.19.0; python_version >= \"3.9\", but you have numpy 2.2.2 which is incompatible.\n", "gensim 4.3.3 requires numpy<2.0,>=1.18.5, but you have numpy 2.2.2 which is incompatible.\n", "cupy-cuda12x 12.2.0 requires numpy<1.27,>=1.20, but you have numpy 2.2.2 which is incompatible.\n", "cudf-cu12 24.10.1 requires pandas<2.2.3dev0,>=2.0, but you have pandas 2.2.3 which is incompatible.\n", "tensorflow 2.17.1 requires numpy<2.0.0,>=1.23.5; python_version <= \"3.11\", but you have numpy 2.2.2 which is incompatible.\n", "langchain 0.3.15 requires numpy<2,>=1.22.4; python_version < \"3.12\", but you have numpy 2.2.2 which is incompatible.\n", "numba 0.60.0 requires numpy<2.1,>=1.22, but you have numpy 2.2.2 which is incompatible.\n", "pytensor 2.26.4 requires numpy<2,>=1.17.0, but you have numpy 2.2.2 which is incompatible.\n", "jupyter-server 1.24.0 requires anyio<4,>=3.1.0, but you have anyio 4.8.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed aiofiles-23.2.1 annotated-types-0.7.0 anyio-4.8.0 certifi-2024.12.14 charset-normalizer-3.4.1 click-8.1.8 fastapi-0.115.7 ffmpy-0.5.0 filelock-3.17.0 fsspec-2024.12.0 gradio-5.13.1 gradio-client-1.6.0 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 huggingface-hub-0.27.1 idna-3.10 jinja2-3.1.5 markdown-it-py-3.0.0 markupsafe-2.1.5 mdurl-0.1.2 numpy-2.2.2 orjson-3.10.15 packaging-24.2 pandas-2.2.3 pillow-11.1.0 pydantic-2.10.6 pydantic-core-2.27.2 pydub-0.25.1 pygments-2.19.1 python-dateutil-2.9.0.post0 python-multipart-0.0.20 pytz-2024.2 pyyaml-6.0.2 requests-2.32.3 rich-13.9.4 ruff-0.9.3 safehttpx-0.1.6 semantic-version-2.10.0 shellingham-1.5.4 six-1.17.0 sniffio-1.3.1 starlette-0.45.3 tomlkit-0.13.2 tqdm-4.67.1 typer-0.15.1 typing-extensions-4.12.2 tzdata-2025.1 urllib3-2.3.0 uvicorn-0.34.0 websockets-14.2\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["PIL", "certifi", "dateutil", "six"]}, "id": "45a852ebfa3d4612bcb497b619848365"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: fastapi in /usr/local/lib/python3.11/dist-packages (0.115.7)\n", "Requirement already satisfied: pydantic in /usr/local/lib/python3.11/dist-packages (2.10.6)\n", "Requirement already satisfied: starlette<0.46.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi) (0.45.3)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.11/dist-packages (from fastapi) (4.12.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /usr/local/lib/python3.11/dist-packages (from pydantic) (2.27.2)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.46.0,>=0.40.0->fastapi) (4.8.0)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi) (1.3.1)\n", "Collecting numpy<2.0\n", "  Using cached numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (61 kB)\n", "Using cached numpy-1.26.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (18.3 MB)\n", "Installing collected packages: numpy\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 2.2.2\n", "    Uninstalling numpy-2.2.2:\n", "      Successfully uninstalled numpy-2.2.2\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "google-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.2.3 which is incompatible.\n", "cudf-cu12 24.10.1 requires pandas<2.2.3dev0,>=2.0, but you have pandas 2.2.3 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed numpy-1.26.4\n", "Do you want to save the repository to Google Drive? (yes/no): no\n", "/usr/local/lib/python3.11/dist-packages/albumentations/__init__.py:13: UserWarning: A new version of Albumentations is available: 2.0.1 (you have 1.4.15). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.\n", "  check_for_updates()\n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/inswapper_128.onnx: 529MB [00:02, 208MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/reswapper_128.onnx: 529MB [00:03, 172MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/reswapper_256.onnx: 529MB [00:02, 201MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/GFPGANv1.4.onnx: 325MB [00:01, 192MB/s]               \n", "Downloading https://github.com/csxmli2016/DMDNet/releases/download/v1/DMDNet.pth: 576MB [00:10, 58.6MB/s]              \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/GPEN-BFR-512.onnx: 271MB [00:01, 193MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/restoreformer_plus_plus.onnx: 281MB [00:01, 192MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/xseg.onnx: 67.1MB [00:00, 126MB/s]                \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/rd64-uni-refined.pth: 4.52MB [00:00, 14.9MB/s]                \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/CodeFormerv0.1.onnx: 359MB [00:02, 157MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/deoldify_artistic.onnx: 243MB [00:05, 49.7MB/s]              \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/deoldify_stable.onnx: 833MB [00:05, 169MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/isnet-general-use.onnx: 170MB [00:01, 146MB/s]               \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/real_esrgan_x4.onnx: 66.3MB [00:00, 122MB/s]                \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/real_esrgan_x2.onnx: 66.3MB [00:00, 125MB/s]                \n", "Downloading https://huggingface.co/countfloyd/deepfake/resolve/main/lsdir_x4.onnx: 63.9MB [00:00, 109MB/s]                \n", "Number of CUDA devices: 1 Currently used Id: 0 Device Name: Tesla T4\n", "Using provider [('CUDAExecutionProvider', {'device_id': 0})] - Device:cuda\n", "* Running on local URL:  http://127.0.0.1:7860\n", "* Running on public URL: https://091b76fb624df9e6af.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n", "download_path: /content/ROOP-FLOYD/models/buffalo_l\n", "Downloading /content/ROOP-FLOYD/models/buffalo_l.zip from https://github.com/deepinsight/insightface/releases/download/v0.7/buffalo_l.zip...\n", "100% 281857/281857 [00:02<00:00, 97739.48KB/s] \n", "\u001b[1;31m2025-01-27 20:20:57.640746601 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/1k3d68.onnx landmark_3d_68 ['None', 3, 192, 192] 0.0 1.0\n", "\u001b[1;31m2025-01-27 20:20:58.555289867 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/2d106det.onnx landmark_2d_106 ['None', 3, 192, 192] 0.0 1.0\n", "\u001b[1;31m2025-01-27 20:20:58.588587579 [E:onnxruntime:Default, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/det_10g.onnx detection [1, 3, '?', '?'] 127.5 128.0\n", "\u001b[1;31m2025-01-27 20:20:58.634481509 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/genderage.onnx genderage ['None', 3, 96, 96] 0.0 1.0\n", "\u001b[1;31m2025-01-27 20:20:58.781837204 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/w600k_r50.onnx recognition ['None', 3, 112, 112] 127.5 127.5\n", "set det-size: (640, 640)\n", "\u001b[1;31m2025-01-27 20:21:17.794260217 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "\u001b[1;31m2025-01-27 20:21:20.180217894 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/1k3d68.onnx landmark_3d_68 ['None', 3, 192, 192] 0.0 1.0\n", "\u001b[1;31m2025-01-27 20:21:20.730031745 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/2d106det.onnx landmark_2d_106 ['None', 3, 192, 192] 0.0 1.0\n", "\u001b[1;31m2025-01-27 20:21:20.754091370 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/det_10g.onnx detection [1, 3, '?', '?'] 127.5 128.0\n", "\u001b[1;31m2025-01-27 20:21:20.785555283 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "model ignore: /content/ROOP-FLOYD/models/buffalo_l/genderage.onnx genderage\n", "\u001b[1;31m2025-01-27 20:21:20.908655688 [E:onnxruntime:De<PERSON><PERSON>, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Applied providers: ['CPUExecutionProvider'], with options: {'CPUExecutionProvider': {}}\n", "find model: /content/ROOP-FLOYD/models/buffalo_l/w600k_r50.onnx recognition ['None', 3, 112, 112] 127.5 127.5\n", "set det-size: (640, 640)\n", "\u001b[1;31m2025-01-27 20:21:45.059933786 [E:onnxruntime:De<PERSON>ult, provider_bridge_ort.cc:1862 TryGetProviderInfo_CUDA] /onnxruntime_src/onnxruntime/core/session/provider_bridge_ort.cc:1539 onnxruntime::Provider& onnxruntime::ProviderLibrary::Get() [ONNXRuntimeError] : 1 : FAIL : Failed to load library libonnxruntime_providers_cuda.so with error: libcudnn_adv.so.9: cannot open shared object file: No such file or directory\n", "\u001b[m\n", "Sorting videos/images\n", "Creating a_valley_girl_pirate_with_a_swollen__temp.mp4 with 16.0 FPS...\n", "['ffmpeg', '-hide_banner', '-hwaccel', 'auto', '-y', '-loglevel', 'error', '-f', 'rawvideo', '-vcodec', 'rawvideo', '-s', '768x768', '-pix_fmt', 'bgr24', '-r', '16.0', '-an', '-i', '-', '-vcodec', 'libx264', '-crf', '14', '-vf', 'colorspace=bt709:iall=bt601-6-625:fast=1', '-pix_fmt', 'yuv420p', '/content/ROOP-FLOYD/output/a_valley_girl_pirate_with_a_swollen__temp.mp4']\n", "Processing:  98% 64/65 [04:04<00:03,  3.82s/frames, memory_usage=02.63GB, execution_threads=2]\n", "Running ffmpeg\n", "\n", "Processing a_valley_girl_pirate_with_a_swollen_20-25-55.mp4 took 248.40 secs, 0.26 frames/s\n", "Finished\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "7F_0v598jKRJ"}, "execution_count": 2, "outputs": []}]}