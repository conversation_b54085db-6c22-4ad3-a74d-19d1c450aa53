import{I as j}from"./Image-BRVH1yXn.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";/* empty css                                                   *//* empty css                                                   */import{V as A}from"./Video-DiLYgEjl.js";import"./file-url-DgijyRSD.js";import"./context-TgWPFwN2.js";import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./hls-CnVhpNcu.js";const{SvelteComponent:D,add_iframe_resize_listener:F,add_render_callback:G,append:k,attr:w,binding_callbacks:H,check_outros:B,create_component:C,destroy_component:E,destroy_each:J,detach:b,element:$,empty:K,ensure_array_like:q,flush:y,group_outros:M,init:L,insert:v,mount_component:N,noop:h,safe_not_equal:O,set_data:P,space:Q,src_url_equal:I,text:W,toggle_class:p,transition_in:d,transition_out:g}=window.__gradio__svelte__internal,{onMount:R}=window.__gradio__svelte__internal;function S(o,e,r){const t=o.slice();return t[7]=e[r],t}function T(o){let e=o[7].orig_name+"",r;return{c(){r=W(e)},m(t,n){v(t,r,n)},p(t,n){n&1&&e!==(e=t[7].orig_name+"")&&P(r,e)},i:h,o:h,d(t){t&&b(r)}}}function U(o){let e,r;return{c(){e=$("audio"),I(e.src,r=o[7].url)||w(e,"src",r),e.controls=!0},m(t,n){v(t,e,n)},p(t,n){n&1&&!I(e.src,r=t[7].url)&&w(e,"src",r)},i:h,o:h,d(t){t&&b(e)}}}function X(o){let e,r;return e=new A({props:{src:o[7].url,alt:"",loop:!0,is_stream:!1}}),{c(){C(e.$$.fragment)},m(t,n){N(e,t,n),r=!0},p(t,n){const c={};n&1&&(c.src=t[7].url),e.$set(c)},i(t){r||(d(e.$$.fragment,t),r=!0)},o(t){g(e.$$.fragment,t),r=!1},d(t){E(e,t)}}}function Y(o){let e,r;return e=new j({props:{src:o[7].url,alt:""}}),{c(){C(e.$$.fragment)},m(t,n){N(e,t,n),r=!0},p(t,n){const c={};n&1&&(c.src=t[7].url),e.$set(c)},i(t){r||(d(e.$$.fragment,t),r=!0)},o(t){g(e.$$.fragment,t),r=!1},d(t){E(e,t)}}}function V(o){let e,r,t,n,c,f,a;const m=[Y,X,U,T],i=[];function _(l,s){return s&1&&(e=null),s&1&&(r=null),s&1&&(t=null),e==null&&(e=!!(l[7].mime_type&&l[7].mime_type.includes("image"))),e?0:(r==null&&(r=!!(l[7].mime_type&&l[7].mime_type.includes("video"))),r?1:(t==null&&(t=!!(l[7].mime_type&&l[7].mime_type.includes("audio"))),t?2:3))}return n=_(o,-1),c=i[n]=m[n](o),{c(){c.c(),f=K()},m(l,s){i[n].m(l,s),v(l,f,s),a=!0},p(l,s){let u=n;n=_(l,s),n===u?i[n].p(l,s):(M(),g(i[u],1,1,()=>{i[u]=null}),B(),c=i[n],c?c.p(l,s):(c=i[n]=m[n](l),c.c()),d(c,1),c.m(f.parentNode,f))},i(l){a||(d(c),a=!0)},o(l){g(c),a=!1},d(l){l&&b(f),i[n].d(l)}}}function Z(o){let e,r,t=(o[0].text?o[0].text:"")+"",n,c,f,a,m=q(o[0].files),i=[];for(let l=0;l<m.length;l+=1)i[l]=V(S(o,m,l));const _=l=>g(i[l],1,1,()=>{i[l]=null});return{c(){e=$("div"),r=$("p"),n=W(t),c=Q();for(let l=0;l<i.length;l+=1)i[l].c();w(e,"class","container svelte-1cl8bqt"),G(()=>o[5].call(e)),p(e,"table",o[1]==="table"),p(e,"gallery",o[1]==="gallery"),p(e,"selected",o[2]),p(e,"border",o[0])},m(l,s){v(l,e,s),k(e,r),k(r,n),k(e,c);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(e,null);f=F(e,o[5].bind(e)),o[6](e),a=!0},p(l,[s]){if((!a||s&1)&&t!==(t=(l[0].text?l[0].text:"")+"")&&P(n,t),s&1){m=q(l[0].files);let u;for(u=0;u<m.length;u+=1){const z=S(l,m,u);i[u]?(i[u].p(z,s),d(i[u],1)):(i[u]=V(z),i[u].c(),d(i[u],1),i[u].m(e,null))}for(M(),u=m.length;u<i.length;u+=1)_(u);B()}(!a||s&2)&&p(e,"table",l[1]==="table"),(!a||s&2)&&p(e,"gallery",l[1]==="gallery"),(!a||s&4)&&p(e,"selected",l[2]),(!a||s&1)&&p(e,"border",l[0])},i(l){if(!a){for(let s=0;s<m.length;s+=1)d(i[s]);a=!0}},o(l){i=i.filter(Boolean);for(let s=0;s<i.length;s+=1)g(i[s]);a=!1},d(l){l&&b(e),J(i,l),f(),o[6](null)}}}function x(o,e){o.style.setProperty("--local-text-width",`${e&&e<150?e:200}px`),o.style.whiteSpace="unset"}function ee(o,e,r){let{value:t={text:"",files:[]}}=e,{type:n}=e,{selected:c=!1}=e,f,a;R(()=>{x(a,f)});function m(){f=this.clientWidth,r(3,f)}function i(_){H[_?"unshift":"push"](()=>{a=_,r(4,a)})}return o.$$set=_=>{"value"in _&&r(0,t=_.value),"type"in _&&r(1,n=_.type),"selected"in _&&r(2,c=_.selected)},[t,n,c,f,a,m,i]}class me extends D{constructor(e){super(),L(this,e,ee,Z,O,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),y()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),y()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),y()}}export{me as default};
//# sourceMappingURL=Example-DGmT8jeC.js.map
