const __vite__fileDeps=["./module-rlRbRtCG.js","./module-C-VadMaF.js","./index-BkoKOheB.js","./index-D_bOYnap.css","./module-BA06XY8_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{s as Ct,W as Bn,p as Pt,a as wt,A as Ln,S as zn}from"./StaticAudio-DJsYBhIx.js";import{_ as Bt,d as In}from"./index-BkoKOheB.js";import{U as Tn}from"./Upload-CRdEJrCZ.js";import{M as tn}from"./ModifyUpload-89NSLc0b.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{B as Un}from"./BlockLabel-CCoHIDM7.js";import{M as qn}from"./Music-CDm0RGMk.js";import{a as jn,S as On}from"./SelectSource-cjMqi4BC.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{S as Wn}from"./StreamingBar-CCUnT5tf.js";import{P as Nn}from"./Trim-JQYgj7Jd.js";import{f as he}from"./utils-BsGrhMNe.js";import{S as nn}from"./index-DrEzyPwM.js";import{B as on}from"./Block-CB3nIXHA.js";import{U as Fn}from"./UploadText-BVcQ46sk.js";import{default as ss}from"./Example-BQyGztrG.js";import"./IconButton-B3BI2i6T.js";import"./Empty-B_fwEKaS.js";import"./ShareButton-HkxSYutN.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./IconButtonWrapper-BMUxHqmv.js";import"./Play-B0Q0U1Qz.js";import"./Undo-DCjBnnSO.js";import"./file-url-DgijyRSD.js";import"./context-TgWPFwN2.js";/* empty css                                                   */import"./hls-CnVhpNcu.js";import"./DownloadLink-IzUam-rM.js";import"./svelte/svelte.js";/* empty css                                             */import"./Clear-By3xiIwg.js";import"./prism-python-B8dcvKZU.js";import"./Upload-DXgDHKDd.js";function pt(n,e,t,i){return new(t||(t=Promise))(function(o,l){function r(_){try{s(i.next(_))}catch(a){l(a)}}function u(_){try{s(i.throw(_))}catch(a){l(a)}}function s(_){var a;_.done?o(_.value):(a=_.value,a instanceof t?a:new t(function(c){c(a)})).then(r,u)}s((i=i.apply(n,[])).next())})}class Hn{constructor(){this.listeners={},this.on=this.addEventListener,this.un=this.removeEventListener}addEventListener(e,t,i){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),i?.once){const o=()=>{this.removeEventListener(e,o),this.removeEventListener(e,t)};return this.addEventListener(e,o),o}return()=>this.removeEventListener(e,t)}removeEventListener(e,t){var i;(i=this.listeners[e])===null||i===void 0||i.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach(i=>i(...t))}}class Vn extends Hn{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}}const Jn=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Ne extends Vn{constructor(e){var t;super(Object.assign(Object.assign({},e),{audioBitsPerSecond:(t=e.audioBitsPerSecond)!==null&&t!==void 0?t:128e3})),this.stream=null,this.mediaRecorder=null}static create(e){return new Ne(e||{})}renderMicStream(e){const t=new AudioContext,i=t.createMediaStreamSource(e),o=t.createAnalyser();i.connect(o);const l=o.frequencyBinCount,r=new Float32Array(l),u=l/t.sampleRate;let s;const _=()=>{o.getFloatTimeDomainData(r),this.wavesurfer&&(this.wavesurfer.options.cursorWidth=0,this.wavesurfer.options.interact=!1,this.wavesurfer.load("",[r],u)),s=requestAnimationFrame(_)};return _(),()=>{cancelAnimationFrame(s),i?.disconnect(),t?.close()}}startMic(e){return pt(this,void 0,void 0,function*(){let t;try{t=yield navigator.mediaDevices.getUserMedia({audio:!e?.deviceId||{deviceId:e.deviceId}})}catch(o){throw new Error("Error accessing the microphone: "+o.message)}const i=this.renderMicStream(t);return this.subscriptions.push(this.once("destroy",i)),this.stream=t,t})}stopMic(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(e){return pt(this,void 0,void 0,function*(){const t=this.stream||(yield this.startMic(e)),i=this.mediaRecorder||new MediaRecorder(t,{mimeType:this.options.mimeType||Jn.find(l=>MediaRecorder.isTypeSupported(l)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const o=[];i.ondataavailable=l=>{l.data.size>0&&o.push(l.data)},i.onstop=()=>{var l;const r=new Blob(o,{type:i.mimeType});this.emit("record-end",r),this.options.renderRecordedAudio!==!1&&((l=this.wavesurfer)===null||l===void 0||l.load(URL.createObjectURL(r)))},i.start(),this.emit("record-start")})}isRecording(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="recording"}isPaused(){var e;return((e=this.mediaRecorder)===null||e===void 0?void 0:e.state)==="paused"}stopRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.stop())}pauseRecording(){var e;this.isRecording()&&((e=this.mediaRecorder)===null||e===void 0||e.pause(),this.emit("record-pause"))}resumeRecording(){var e;this.isPaused()&&((e=this.mediaRecorder)===null||e===void 0||e.resume(),this.emit("record-resume"))}static getAvailableAudioDevices(){return pt(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(e=>e.filter(t=>t.kind==="audioinput"))})}destroy(){super.destroy(),this.stopRecording(),this.stopMic()}}const{SvelteComponent:Gn,append:sn,attr:Lt,destroy_each:Yn,detach:xe,element:kt,empty:Kn,ensure_array_like:zt,flush:It,init:Qn,insert:et,noop:Tt,safe_not_equal:Xn,set_data:rn,set_input_value:vt,text:ln}=window.__gradio__svelte__internal,{createEventDispatcher:Zn}=window.__gradio__svelte__internal;function Ut(n,e,t){const i=n.slice();return i[3]=e[t],i}function $n(n){let e,t=zt(n[0]),i=[];for(let o=0;o<t.length;o+=1)i[o]=qt(Ut(n,t,o));return{c(){for(let o=0;o<i.length;o+=1)i[o].c();e=Kn()},m(o,l){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(o,l);et(o,e,l)},p(o,l){if(l&1){t=zt(o[0]);let r;for(r=0;r<t.length;r+=1){const u=Ut(o,t,r);i[r]?i[r].p(u,l):(i[r]=qt(u),i[r].c(),i[r].m(e.parentNode,e))}for(;r<i.length;r+=1)i[r].d(1);i.length=t.length}},d(o){o&&xe(e),Yn(i,o)}}}function xn(n){let e,t=n[1]("audio.no_microphone")+"",i;return{c(){e=kt("option"),i=ln(t),e.__value="",vt(e,e.__value)},m(o,l){et(o,e,l),sn(e,i)},p(o,l){l&2&&t!==(t=o[1]("audio.no_microphone")+"")&&rn(i,t)},d(o){o&&xe(e)}}}function qt(n){let e,t=n[3].label+"",i,o;return{c(){e=kt("option"),i=ln(t),e.__value=o=n[3].deviceId,vt(e,e.__value)},m(l,r){et(l,e,r),sn(e,i)},p(l,r){r&1&&t!==(t=l[3].label+"")&&rn(i,t),r&1&&o!==(o=l[3].deviceId)&&(e.__value=o,vt(e,e.__value))},d(l){l&&xe(e)}}}function ei(n){let e,t;function i(r,u){return r[0].length===0?xn:$n}let o=i(n),l=o(n);return{c(){e=kt("select"),l.c(),Lt(e,"class","mic-select svelte-1ya9x7a"),Lt(e,"aria-label","Select input device"),e.disabled=t=n[0].length===0},m(r,u){et(r,e,u),l.m(e,null)},p(r,[u]){o===(o=i(r))&&l?l.p(r,u):(l.d(1),l=o(r),l&&(l.c(),l.m(e,null))),u&1&&t!==(t=r[0].length===0)&&(e.disabled=t)},i:Tt,o:Tt,d(r){r&&xe(e),l.d()}}}function ti(n,e,t){let{i18n:i}=e,{micDevices:o=[]}=e;const l=Zn();return n.$$set=r=>{"i18n"in r&&t(1,i=r.i18n),"micDevices"in r&&t(0,o=r.micDevices)},n.$$.update=()=>{if(n.$$.dirty&2&&typeof window<"u")try{let r=[];Ne.getAvailableAudioDevices().then(u=>{t(0,o=u),u.forEach(s=>{s.deviceId&&r.push(s)}),t(0,o=r)})}catch(r){throw r instanceof DOMException&&r.name=="NotAllowedError"&&l("error",i("audio.allow_recording_access")),r}},[o,i]}class an extends Gn{constructor(e){super(),Qn(this,e,ti,ei,Xn,{i18n:1,micDevices:0})}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),It()}get micDevices(){return this.$$.ctx[0]}set micDevices(e){this.$$set({micDevices:e}),It()}}const{SvelteComponent:ni,add_flush_callback:ii,append:U,attr:X,bind:oi,binding_callbacks:Se,create_component:jt,destroy_component:Ot,detach:un,element:de,flush:Re,init:si,insert:_n,listen:Pe,mount_component:Wt,run_all:ri,safe_not_equal:li,set_data:Ie,space:De,text:Te,transition_in:Nt,transition_out:Ft}=window.__gradio__svelte__internal;function Ht(n){let e,t;return{c(){e=de("time"),t=Te(n[2]),X(e,"class","duration-button duration svelte-11fum4b")},m(i,o){_n(i,e,o),U(e,t)},p(i,o){o&4&&Ie(t,i[2])},d(i){i&&un(e)}}}function ai(n){let e,t,i,o=n[1]("audio.record")+"",l,r,u,s=n[1]("audio.stop")+"",_,a,c,m,k=n[1]("audio.stop")+"",S,E,v,h,f,b,I=n[1]("audio.resume")+"",w,P,W,T,N,M,y,H;h=new Nn({});let B=n[4]&&!n[3]&&Ht(n);function K(R){n[23](R)}let _e={i18n:n[1]};return n[5]!==void 0&&(_e.micDevices=n[5]),T=new an({props:_e}),Se.push(()=>oi(T,"micDevices",K)),{c(){e=de("div"),t=de("div"),i=de("button"),l=Te(o),r=De(),u=de("button"),_=Te(s),c=De(),m=de("button"),S=Te(k),E=De(),v=de("button"),jt(h.$$.fragment),f=De(),b=de("button"),w=Te(I),P=De(),B&&B.c(),W=De(),jt(T.$$.fragment),X(i,"class","record record-button svelte-11fum4b"),X(u,"class",a="stop-button "+(n[0].isPaused()?"stop-button-paused":"")+" svelte-11fum4b"),X(m,"id","stop-paused"),X(m,"class","stop-button-paused svelte-11fum4b"),X(v,"aria-label","pause"),X(v,"class","pause-button svelte-11fum4b"),X(b,"class","resume-button svelte-11fum4b"),X(t,"class","wrapper svelte-11fum4b"),X(e,"class","controls svelte-11fum4b")},m(R,C){_n(R,e,C),U(e,t),U(t,i),U(i,l),n[13](i),U(t,r),U(t,u),U(u,_),n[15](u),U(t,c),U(t,m),U(m,S),n[17](m),U(t,E),U(t,v),Wt(h,v,null),n[19](v),U(t,f),U(t,b),U(b,w),n[21](b),U(t,P),B&&B.m(t,null),U(e,W),Wt(T,e,null),M=!0,y||(H=[Pe(i,"click",n[14]),Pe(u,"click",n[16]),Pe(m,"click",n[18]),Pe(v,"click",n[20]),Pe(b,"click",n[22])],y=!0)},p(R,[C]){(!M||C&2)&&o!==(o=R[1]("audio.record")+"")&&Ie(l,o),(!M||C&2)&&s!==(s=R[1]("audio.stop")+"")&&Ie(_,s),(!M||C&1&&a!==(a="stop-button "+(R[0].isPaused()?"stop-button-paused":"")+" svelte-11fum4b"))&&X(u,"class",a),(!M||C&2)&&k!==(k=R[1]("audio.stop")+"")&&Ie(S,k),(!M||C&2)&&I!==(I=R[1]("audio.resume")+"")&&Ie(w,I),R[4]&&!R[3]?B?B.p(R,C):(B=Ht(R),B.c(),B.m(t,null)):B&&(B.d(1),B=null);const D={};C&2&&(D.i18n=R[1]),!N&&C&32&&(N=!0,D.micDevices=R[5],ii(()=>N=!1)),T.$set(D)},i(R){M||(Nt(h.$$.fragment,R),Nt(T.$$.fragment,R),M=!0)},o(R){Ft(h.$$.fragment,R),Ft(T.$$.fragment,R),M=!1},d(R){R&&un(e),n[13](null),n[15](null),n[17](null),Ot(h),n[19](null),n[21](null),B&&B.d(),Ot(T),y=!1,ri(H)}}}function ui(n,e,t){let{record:i}=e,{i18n:o}=e,{recording:l=!1}=e,r=[],u,s,_,a,c,m=!1,{record_time:k}=e,{show_recording_waveform:S}=e,{timing:E=!1}=e;function v(y){Se[y?"unshift":"push"](()=>{u=y,t(6,u),t(0,i)})}const h=()=>i.startRecording();function f(y){Se[y?"unshift":"push"](()=>{a=y,t(9,a),t(0,i)})}const b=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function I(y){Se[y?"unshift":"push"](()=>{c=y,t(10,c),t(0,i)})}const w=()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopRecording()};function P(y){Se[y?"unshift":"push"](()=>{s=y,t(7,s),t(0,i)})}const W=()=>i.pauseRecording();function T(y){Se[y?"unshift":"push"](()=>{_=y,t(8,_),t(0,i)})}const N=()=>i.resumeRecording();function M(y){r=y,t(5,r)}return n.$$set=y=>{"record"in y&&t(0,i=y.record),"i18n"in y&&t(1,o=y.i18n),"recording"in y&&t(11,l=y.recording),"record_time"in y&&t(2,k=y.record_time),"show_recording_waveform"in y&&t(3,S=y.show_recording_waveform),"timing"in y&&t(4,E=y.timing)},n.$$.update=()=>{n.$$.dirty&1&&i.on("record-start",()=>{i.startMic(),t(6,u.style.display="none",u),t(9,a.style.display="flex",a),t(7,s.style.display="block",s)}),n.$$.dirty&1&&i.on("record-end",()=>{i.isPaused()&&(i.resumeRecording(),i.stopRecording()),i.stopMic(),t(6,u.style.display="flex",u),t(9,a.style.display="none",a),t(7,s.style.display="none",s),t(6,u.disabled=!1,u)}),n.$$.dirty&1&&i.on("record-pause",()=>{t(7,s.style.display="none",s),t(8,_.style.display="block",_),t(9,a.style.display="none",a),t(10,c.style.display="flex",c)}),n.$$.dirty&1&&i.on("record-resume",()=>{t(7,s.style.display="block",s),t(8,_.style.display="none",_),t(6,u.style.display="none",u),t(9,a.style.display="flex",a),t(10,c.style.display="none",c)}),n.$$.dirty&6145&&(l&&!m?(i.startRecording(),t(12,m=!0)):(i.stopRecording(),t(12,m=!1)))},[i,o,k,S,E,r,u,s,_,a,c,l,m,v,h,f,b,I,w,P,W,T,N,M]}class _i extends ni{constructor(e){super(),si(this,e,ui,ai,li,{record:0,i18n:1,recording:11,record_time:2,show_recording_waveform:3,timing:4})}get record(){return this.$$.ctx[0]}set record(e){this.$$set({record:e}),Re()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),Re()}get recording(){return this.$$.ctx[11]}set recording(e){this.$$set({recording:e}),Re()}get record_time(){return this.$$.ctx[2]}set record_time(e){this.$$set({record_time:e}),Re()}get show_recording_waveform(){return this.$$.ctx[3]}set show_recording_waveform(e){this.$$set({show_recording_waveform:e}),Re()}get timing(){return this.$$.ctx[4]}set timing(e){this.$$set({timing:e}),Re()}}const{SvelteComponent:ci,add_flush_callback:Ke,append:G,attr:te,bind:Qe,binding_callbacks:ge,check_outros:Vt,create_component:cn,destroy_component:fn,detach:Fe,element:ne,flush:ce,group_outros:Jt,init:fi,insert:He,mount_component:dn,noop:di,safe_not_equal:mi,set_data:mn,space:Ee,text:gn,transition_in:me,transition_out:Me}=window.__gradio__svelte__internal,{onMount:gi}=window.__gradio__svelte__internal,{createEventDispatcher:hi}=window.__gradio__svelte__internal;function Gt(n){let e,t,i,o,l,r=n[0]==="edit"&&n[17]>0&&Yt(n);function u(a,c){return a[16]?pi:bi}let s=u(n),_=s(n);return{c(){e=ne("div"),t=ne("time"),t.textContent="0:00",i=Ee(),o=ne("div"),r&&r.c(),l=Ee(),_.c(),te(t,"class","time svelte-9n45fh"),te(e,"class","timestamps svelte-9n45fh")},m(a,c){He(a,e,c),G(e,t),n[23](t),G(e,i),G(e,o),r&&r.m(o,null),G(o,l),_.m(o,null)},p(a,c){a[0]==="edit"&&a[17]>0?r?r.p(a,c):(r=Yt(a),r.c(),r.m(o,l)):r&&(r.d(1),r=null),s===(s=u(a))&&_?_.p(a,c):(_.d(1),_=s(a),_&&(_.c(),_.m(o,null)))},d(a){a&&Fe(e),n[23](null),r&&r.d(),_.d()}}}function Yt(n){let e,t=he(n[17])+"",i;return{c(){e=ne("time"),i=gn(t),te(e,"class","trim-duration svelte-9n45fh")},m(o,l){He(o,e,l),G(e,i)},p(o,l){l[0]&131072&&t!==(t=he(o[17])+"")&&mn(i,t)},d(o){o&&Fe(e)}}}function bi(n){let e;return{c(){e=ne("time"),e.textContent="0:00",te(e,"class","duration svelte-9n45fh")},m(t,i){He(t,e,i),n[24](e)},p:di,d(t){t&&Fe(e),n[24](null)}}}function pi(n){let e,t=he(n[15])+"",i;return{c(){e=ne("time"),i=gn(t),te(e,"class","duration svelte-9n45fh")},m(o,l){He(o,e,l),G(e,i)},p(o,l){l[0]&32768&&t!==(t=he(o[15])+"")&&mn(i,t)},d(o){o&&Fe(e)}}}function Kt(n){let e,t,i;function o(r){n[25](r)}let l={i18n:n[1],timing:n[16],recording:n[5],show_recording_waveform:n[2].show_recording_waveform,record_time:he(n[15])};return n[7]!==void 0&&(l.record=n[7]),e=new _i({props:l}),ge.push(()=>Qe(e,"record",o)),{c(){cn(e.$$.fragment)},m(r,u){dn(e,r,u),i=!0},p(r,u){const s={};u[0]&2&&(s.i18n=r[1]),u[0]&65536&&(s.timing=r[16]),u[0]&32&&(s.recording=r[5]),u[0]&4&&(s.show_recording_waveform=r[2].show_recording_waveform),u[0]&32768&&(s.record_time=he(r[15])),!t&&u[0]&128&&(t=!0,s.record=r[7],Ke(()=>t=!1)),e.$set(s)},i(r){i||(me(e.$$.fragment,r),i=!0)},o(r){Me(e.$$.fragment,r),i=!1},d(r){fn(e,r)}}}function Qt(n){let e,t,i,o,l;function r(a){n[26](a)}function u(a){n[27](a)}function s(a){n[28](a)}let _={container:n[11],playing:n[10],audio_duration:n[14],i18n:n[1],editable:n[4],interactive:!0,handle_trim_audio:n[18],show_redo:!0,handle_reset_value:n[3],waveform_options:n[2]};return n[6]!==void 0&&(_.waveform=n[6]),n[17]!==void 0&&(_.trimDuration=n[17]),n[0]!==void 0&&(_.mode=n[0]),e=new Bn({props:_}),ge.push(()=>Qe(e,"waveform",r)),ge.push(()=>Qe(e,"trimDuration",u)),ge.push(()=>Qe(e,"mode",s)),{c(){cn(e.$$.fragment)},m(a,c){dn(e,a,c),l=!0},p(a,c){const m={};c[0]&2048&&(m.container=a[11]),c[0]&1024&&(m.playing=a[10]),c[0]&16384&&(m.audio_duration=a[14]),c[0]&2&&(m.i18n=a[1]),c[0]&16&&(m.editable=a[4]),c[0]&8&&(m.handle_reset_value=a[3]),c[0]&4&&(m.waveform_options=a[2]),!t&&c[0]&64&&(t=!0,m.waveform=a[6],Ke(()=>t=!1)),!i&&c[0]&131072&&(i=!0,m.trimDuration=a[17],Ke(()=>i=!1)),!o&&c[0]&1&&(o=!0,m.mode=a[0],Ke(()=>o=!1)),e.$set(m)},i(a){l||(me(e.$$.fragment,a),l=!0)},o(a){Me(e.$$.fragment,a),l=!1},d(a){fn(e,a)}}}function wi(n){let e,t,i,o,l,r,u,s,_=(n[16]||n[13])&&n[2].show_recording_waveform&&Gt(n),a=n[12]&&!n[13]&&Kt(n),c=n[6]&&n[13]&&Qt(n);return{c(){e=ne("div"),t=ne("div"),i=Ee(),o=ne("div"),l=Ee(),_&&_.c(),r=Ee(),a&&a.c(),u=Ee(),c&&c.c(),te(t,"class","microphone svelte-9n45fh"),te(t,"data-testid","microphone-waveform"),te(o,"data-testid","recording-waveform"),te(e,"class","component-wrapper svelte-9n45fh")},m(m,k){He(m,e,k),G(e,t),n[21](t),G(e,i),G(e,o),n[22](o),G(e,l),_&&_.m(e,null),G(e,r),a&&a.m(e,null),G(e,u),c&&c.m(e,null),s=!0},p(m,k){(m[16]||m[13])&&m[2].show_recording_waveform?_?_.p(m,k):(_=Gt(m),_.c(),_.m(e,r)):_&&(_.d(1),_=null),m[12]&&!m[13]?a?(a.p(m,k),k[0]&12288&&me(a,1)):(a=Kt(m),a.c(),me(a,1),a.m(e,u)):a&&(Jt(),Me(a,1,1,()=>{a=null}),Vt()),m[6]&&m[13]?c?(c.p(m,k),k[0]&8256&&me(c,1)):(c=Qt(m),c.c(),me(c,1),c.m(e,null)):c&&(Jt(),Me(c,1,1,()=>{c=null}),Vt())},i(m){s||(me(a),me(c),s=!0)},o(m){Me(a),Me(c),s=!1},d(m){m&&Fe(e),n[21](null),n[22](null),_&&_.d(),a&&a.d(),c&&c.d()}}}function vi(n,e,t){let{mode:i}=e,{i18n:o}=e,{dispatch_blob:l}=e,{waveform_settings:r}=e,{waveform_options:u={show_recording_waveform:!0}}=e,{handle_reset_value:s}=e,{editable:_=!0}=e,{recording:a=!1}=e,c,m,k=!1,S,E,v,h=null,f,b,I,w=0,P,W=!1,T=0;const N=()=>{clearInterval(P),P=setInterval(()=>{t(15,w++,w)},1e3)},M=hi();function y(){if(N(),t(16,W=!0),M("start_recording"),u.show_recording_waveform){let p=E;p&&(p.style.display="block")}}async function H(p){t(15,w=0),t(16,W=!1),clearInterval(P);try{const J=await p.arrayBuffer(),F=await new AudioContext({sampleRate:r.sampleRate}).decodeAudioData(J);F&&await Pt(F).then(async ye=>{await l([ye],"change"),await l([ye],"stop_recording")})}catch(J){console.error(J)}}const B=()=>{E&&t(12,E.innerHTML="",E),c!==void 0&&c.destroy(),E&&(c=wt.create({...r,normalize:!1,container:E}),t(7,v=c.registerPlugin(Ne.create())),v.startMic(),v?.on("record-end",H),v?.on("record-start",y),v?.on("record-pause",()=>{M("pause_recording"),clearInterval(P)}),v?.on("record-end",p=>{t(13,h=URL.createObjectURL(p));const J=E,L=S;J&&(J.style.display="none"),L&&h&&(L.innerHTML="",K())}))},K=()=>{let p=S;!h||!p||t(6,m=wt.create({container:p,url:h,...r}))},_e=async(p,J)=>{t(0,i="edit");const L=m.getDecodedData();L&&await Pt(L,p,J).then(async F=>{await l([F],"change"),await l([F],"stop_recording"),m.destroy(),K()}),M("edit")};gi(()=>{B(),window.addEventListener("keydown",p=>{p.key==="ArrowRight"?Ct(m,.1):p.key==="ArrowLeft"&&Ct(m,-.1)})});function R(p){ge[p?"unshift":"push"](()=>{E=p,t(12,E)})}function C(p){ge[p?"unshift":"push"](()=>{S=p,t(11,S)})}function D(p){ge[p?"unshift":"push"](()=>{f=p,t(8,f),t(6,m)})}function V(p){ge[p?"unshift":"push"](()=>{b=p,t(9,b),t(6,m)})}function Z(p){v=p,t(7,v)}function Q(p){m=p,t(6,m)}function $(p){T=p,t(17,T)}function x(p){i=p,t(0,i)}return n.$$set=p=>{"mode"in p&&t(0,i=p.mode),"i18n"in p&&t(1,o=p.i18n),"dispatch_blob"in p&&t(19,l=p.dispatch_blob),"waveform_settings"in p&&t(20,r=p.waveform_settings),"waveform_options"in p&&t(2,u=p.waveform_options),"handle_reset_value"in p&&t(3,s=p.handle_reset_value),"editable"in p&&t(4,_=p.editable),"recording"in p&&t(5,a=p.recording)},n.$$.update=()=>{n.$$.dirty[0]&128&&v?.on("record-resume",()=>{N()}),n.$$.dirty[0]&576&&m?.on("decode",p=>{t(14,I=p),b&&t(9,b.textContent=he(p),b)}),n.$$.dirty[0]&320&&m?.on("timeupdate",p=>f&&t(8,f.textContent=he(p),f)),n.$$.dirty[0]&64&&m?.on("pause",()=>{M("pause"),t(10,k=!1)}),n.$$.dirty[0]&64&&m?.on("play",()=>{M("play"),t(10,k=!0)}),n.$$.dirty[0]&64&&m?.on("finish",()=>{M("stop"),t(10,k=!1)})},[i,o,u,s,_,a,m,v,f,b,k,S,E,h,I,w,W,T,_e,l,r,R,C,D,V,Z,Q,$,x]}class ki extends ci{constructor(e){super(),fi(this,e,vi,wi,mi,{mode:0,i18n:1,dispatch_blob:19,waveform_settings:20,waveform_options:2,handle_reset_value:3,editable:4,recording:5},null,[-1,-1])}get mode(){return this.$$.ctx[0]}set mode(e){this.$$set({mode:e}),ce()}get i18n(){return this.$$.ctx[1]}set i18n(e){this.$$set({i18n:e}),ce()}get dispatch_blob(){return this.$$.ctx[19]}set dispatch_blob(e){this.$$set({dispatch_blob:e}),ce()}get waveform_settings(){return this.$$.ctx[20]}set waveform_settings(e){this.$$set({waveform_settings:e}),ce()}get waveform_options(){return this.$$.ctx[2]}set waveform_options(e){this.$$set({waveform_options:e}),ce()}get handle_reset_value(){return this.$$.ctx[3]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),ce()}get editable(){return this.$$.ctx[4]}set editable(e){this.$$set({editable:e}),ce()}get recording(){return this.$$.ctx[5]}set recording(e){this.$$set({recording:e}),ce()}}const{SvelteComponent:yi,add_flush_callback:Ri,append:Y,attr:ie,bind:Di,binding_callbacks:hn,check_outros:Si,create_component:bn,destroy_component:pn,detach:Ve,element:le,flush:fe,group_outros:Ei,init:Mi,insert:Je,listen:yt,mount_component:wn,noop:$e,null_to_empty:Xt,safe_not_equal:Ai,set_data:Rt,set_style:Zt,space:qe,text:Dt,transition_in:Xe,transition_out:Ze}=window.__gradio__svelte__internal,{onMount:Ci}=window.__gradio__svelte__internal;function $t(n){let e;return{c(){e=le("div"),Zt(e,"display",n[0]?"block":"none")},m(t,i){Je(t,e,i),n[11](e)},p(t,i){i&1&&Zt(e,"display",t[0]?"block":"none")},d(t){t&&Ve(e),n[11](null)}}}function Pi(n){let e,t,i,o=n[4]("audio.record")+"",l,r,u;return{c(){e=le("button"),t=le("span"),t.innerHTML='<span class="dot"></span>',i=qe(),l=Dt(o),ie(t,"class","record-icon"),ie(e,"class","record-button svelte-1fz19cj")},m(s,_){Je(s,e,_),Y(e,t),Y(e,i),Y(e,l),r||(u=yt(e,"click",n[14]),r=!0)},p(s,_){_&16&&o!==(o=s[4]("audio.record")+"")&&Rt(l,o)},i:$e,o:$e,d(s){s&&Ve(e),r=!1,u()}}}function Bi(n){let e,t,i,o,l=n[4]("audio.waiting")+"",r,u,s,_;return i=new jn({}),{c(){e=le("button"),t=le("div"),bn(i.$$.fragment),o=qe(),r=Dt(l),ie(t,"class","icon svelte-1fz19cj"),ie(e,"class","spinner-button svelte-1fz19cj")},m(a,c){Je(a,e,c),Y(e,t),wn(i,t,null),Y(e,o),Y(e,r),u=!0,s||(_=yt(e,"click",n[13]),s=!0)},p(a,c){(!u||c&16)&&l!==(l=a[4]("audio.waiting")+"")&&Rt(r,l)},i(a){u||(Xe(i.$$.fragment,a),u=!0)},o(a){Ze(i.$$.fragment,a),u=!1},d(a){a&&Ve(e),pn(i),s=!1,_()}}}function Li(n){let e,t,i,o=(n[1]?n[4]("audio.pause"):n[4]("audio.stop"))+"",l,r,u,s;return{c(){e=le("button"),t=le("span"),t.innerHTML='<span class="pinger"></span> <span class="dot"></span>',i=qe(),l=Dt(o),ie(t,"class","record-icon"),ie(e,"class",r=Xt(n[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")},m(_,a){Je(_,e,a),Y(e,t),Y(e,i),Y(e,l),u||(s=yt(e,"click",n[12]),u=!0)},p(_,a){a&18&&o!==(o=(_[1]?_[4]("audio.pause"):_[4]("audio.stop"))+"")&&Rt(l,o),a&2&&r!==(r=Xt(_[1]?"stop-button-paused":"stop-button")+" svelte-1fz19cj")&&ie(e,"class",r)},i:$e,o:$e,d(_){_&&Ve(e),u=!1,s()}}}function zi(n){let e,t,i,o,l,r,u,s,_,a=n[5].show_recording_waveform&&$t(n);const c=[Li,Bi,Pi],m=[];function k(v,h){return v[0]&&!v[6]?0:v[0]&&v[6]?1:2}o=k(n),l=m[o]=c[o](n);function S(v){n[15](v)}let E={i18n:n[4]};return n[9]!==void 0&&(E.micDevices=n[9]),u=new an({props:E}),hn.push(()=>Di(u,"micDevices",S)),{c(){e=le("div"),a&&a.c(),t=qe(),i=le("div"),l.c(),r=qe(),bn(u.$$.fragment),ie(i,"class","controls svelte-1fz19cj"),ie(e,"class","mic-wrap svelte-1fz19cj")},m(v,h){Je(v,e,h),a&&a.m(e,null),Y(e,t),Y(e,i),m[o].m(i,null),Y(i,r),wn(u,i,null),_=!0},p(v,[h]){v[5].show_recording_waveform?a?a.p(v,h):(a=$t(v),a.c(),a.m(e,t)):a&&(a.d(1),a=null);let f=o;o=k(v),o===f?m[o].p(v,h):(Ei(),Ze(m[f],1,1,()=>{m[f]=null}),Si(),l=m[o],l?l.p(v,h):(l=m[o]=c[o](v),l.c()),Xe(l,1),l.m(i,r));const b={};h&16&&(b.i18n=v[4]),!s&&h&512&&(s=!0,b.micDevices=v[9],Ri(()=>s=!1)),u.$set(b)},i(v){_||(Xe(l),Xe(u.$$.fragment,v),_=!0)},o(v){Ze(l),Ze(u.$$.fragment,v),_=!1},d(v){v&&Ve(e),a&&a.d(),m[o].d(),pn(u)}}}function Ii(n,e,t){let{recording:i=!1}=e,{paused_recording:o=!1}=e,{stop:l}=e,{record:r}=e,{i18n:u}=e,{waveform_settings:s}=e,{waveform_options:_={show_recording_waveform:!0}}=e,{waiting:a=!1}=e,c,m,k,S=[];Ci(()=>{E()});const E=()=>{c!==void 0&&c.destroy(),k&&(c=wt.create({...s,height:100,container:k}),t(7,m=c.registerPlugin(Ne.create())))};function v(w){hn[w?"unshift":"push"](()=>{k=w,t(8,k)})}const h=()=>{m?.stopMic(),l()},f=()=>{l()},b=()=>{m?.startMic(),r()};function I(w){S=w,t(9,S)}return n.$$set=w=>{"recording"in w&&t(0,i=w.recording),"paused_recording"in w&&t(1,o=w.paused_recording),"stop"in w&&t(2,l=w.stop),"record"in w&&t(3,r=w.record),"i18n"in w&&t(4,u=w.i18n),"waveform_settings"in w&&t(10,s=w.waveform_settings),"waveform_options"in w&&t(5,_=w.waveform_options),"waiting"in w&&t(6,a=w.waiting)},[i,o,l,r,u,_,a,m,k,S,s,v,h,f,b,I]}class Ti extends yi{constructor(e){super(),Mi(this,e,Ii,zi,Ai,{recording:0,paused_recording:1,stop:2,record:3,i18n:4,waveform_settings:10,waveform_options:5,waiting:6})}get recording(){return this.$$.ctx[0]}set recording(e){this.$$set({recording:e}),fe()}get paused_recording(){return this.$$.ctx[1]}set paused_recording(e){this.$$set({paused_recording:e}),fe()}get stop(){return this.$$.ctx[2]}set stop(e){this.$$set({stop:e}),fe()}get record(){return this.$$.ctx[3]}set record(e){this.$$set({record:e}),fe()}get i18n(){return this.$$.ctx[4]}set i18n(e){this.$$set({i18n:e}),fe()}get waveform_settings(){return this.$$.ctx[10]}set waveform_settings(e){this.$$set({waveform_settings:e}),fe()}get waveform_options(){return this.$$.ctx[5]}set waveform_options(e){this.$$set({waveform_options:e}),fe()}get waiting(){return this.$$.ctx[6]}set waiting(e){this.$$set({waiting:e}),fe()}}const{SvelteComponent:Ui,add_flush_callback:je,append:xt,attr:qi,bind:Oe,binding_callbacks:We,bubble:pe,check_outros:St,create_component:oe,create_slot:ji,destroy_component:se,detach:Ae,element:Oi,empty:vn,flush:z,get_all_dirty_from_scope:Wi,get_slot_changes:Ni,group_outros:Et,init:Fi,insert:Ce,mount_component:re,safe_not_equal:Hi,space:Ue,transition_in:j,transition_out:O,update_slot_base:Vi}=window.__gradio__svelte__internal,{onDestroy:Ji,createEventDispatcher:Gi,tick:ts}=window.__gradio__svelte__internal;function Yi(n){let e,t,i,o,l;e=new tn({props:{i18n:n[12],download:n[9]?n[2].url:null}}),e.$on("clear",n[27]),e.$on("edit",n[46]);function r(s){n[47](s)}let u={value:n[2],label:n[5],i18n:n[12],dispatch_blob:n[25],waveform_settings:n[13],waveform_options:n[15],trim_region_settings:n[14],handle_reset_value:n[16],editable:n[17],loop:n[7],interactive:!0};return n[23]!==void 0&&(u.mode=n[23]),i=new Ln({props:u}),We.push(()=>Oe(i,"mode",r)),i.$on("stop",n[48]),i.$on("play",n[49]),i.$on("pause",n[50]),i.$on("edit",n[51]),{c(){oe(e.$$.fragment),t=Ue(),oe(i.$$.fragment)},m(s,_){re(e,s,_),Ce(s,t,_),re(i,s,_),l=!0},p(s,_){const a={};_[0]&4096&&(a.i18n=s[12]),_[0]&516&&(a.download=s[9]?s[2].url:null),e.$set(a);const c={};_[0]&4&&(c.value=s[2]),_[0]&32&&(c.label=s[5]),_[0]&4096&&(c.i18n=s[12]),_[0]&8192&&(c.waveform_settings=s[13]),_[0]&32768&&(c.waveform_options=s[15]),_[0]&16384&&(c.trim_region_settings=s[14]),_[0]&65536&&(c.handle_reset_value=s[16]),_[0]&131072&&(c.editable=s[17]),_[0]&128&&(c.loop=s[7]),!o&&_[0]&8388608&&(o=!0,c.mode=s[23],je(()=>o=!1)),i.$set(c)},i(s){l||(j(e.$$.fragment,s),j(i.$$.fragment,s),l=!0)},o(s){O(e.$$.fragment,s),O(i.$$.fragment,s),l=!1},d(s){s&&Ae(t),se(e,s),se(i,s)}}}function Ki(n){let e,t,i,o;const l=[Xi,Qi],r=[];function u(s,_){return s[3]==="microphone"?0:s[3]==="upload"?1:-1}return~(e=u(n))&&(t=r[e]=l[e](n)),{c(){t&&t.c(),i=vn()},m(s,_){~e&&r[e].m(s,_),Ce(s,i,_),o=!0},p(s,_){let a=e;e=u(s),e===a?~e&&r[e].p(s,_):(t&&(Et(),O(r[a],1,1,()=>{r[a]=null}),St()),~e?(t=r[e],t?t.p(s,_):(t=r[e]=l[e](s),t.c()),j(t,1),t.m(i.parentNode,i)):t=null)},i(s){o||(j(t),o=!0)},o(s){O(t),o=!1},d(s){s&&Ae(i),~e&&r[e].d(s)}}}function Qi(n){let e,t,i,o;function l(s){n[43](s)}function r(s){n[44](s)}let u={filetype:"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*",root:n[6],max_file_size:n[18],upload:n[19],stream_handler:n[20],$$slots:{default:[Zi]},$$scope:{ctx:n}};return n[0]!==void 0&&(u.dragging=n[0]),n[4]!==void 0&&(u.uploading=n[4]),e=new Tn({props:u}),We.push(()=>Oe(e,"dragging",l)),We.push(()=>Oe(e,"uploading",r)),e.$on("load",n[28]),e.$on("error",n[45]),{c(){oe(e.$$.fragment)},m(s,_){re(e,s,_),o=!0},p(s,_){const a={};_[0]&64&&(a.root=s[6]),_[0]&262144&&(a.max_file_size=s[18]),_[0]&524288&&(a.upload=s[19]),_[0]&1048576&&(a.stream_handler=s[20]),_[1]&4194304&&(a.$$scope={dirty:_,ctx:s}),!t&&_[0]&1&&(t=!0,a.dragging=s[0],je(()=>t=!1)),!i&&_[0]&16&&(i=!0,a.uploading=s[4],je(()=>i=!1)),e.$set(a)},i(s){o||(j(e.$$.fragment,s),o=!0)},o(s){O(e.$$.fragment,s),o=!1},d(s){se(e,s)}}}function Xi(n){let e,t,i,o,l,r;e=new tn({props:{i18n:n[12]}}),e.$on("clear",n[27]);const u=[xi,$i],s=[];function _(a,c){return a[11]?0:1}return i=_(n),o=s[i]=u[i](n),{c(){oe(e.$$.fragment),t=Ue(),o.c(),l=vn()},m(a,c){re(e,a,c),Ce(a,t,c),s[i].m(a,c),Ce(a,l,c),r=!0},p(a,c){const m={};c[0]&4096&&(m.i18n=a[12]),e.$set(m);let k=i;i=_(a),i===k?s[i].p(a,c):(Et(),O(s[k],1,1,()=>{s[k]=null}),St(),o=s[i],o?o.p(a,c):(o=s[i]=u[i](a),o.c()),j(o,1),o.m(l.parentNode,l))},i(a){r||(j(e.$$.fragment,a),j(o),r=!0)},o(a){O(e.$$.fragment,a),O(o),r=!1},d(a){a&&(Ae(t),Ae(l)),se(e,a),s[i].d(a)}}}function Zi(n){let e;const t=n[38].default,i=ji(t,n,n[53],null);return{c(){i&&i.c()},m(o,l){i&&i.m(o,l),e=!0},p(o,l){i&&i.p&&(!e||l[1]&4194304)&&Vi(i,t,o,o[53],e?Ni(t,o[53],l,null):Wi(o[53]),null)},i(o){e||(j(i,o),e=!0)},o(o){O(i,o),e=!1},d(o){i&&i.d(o)}}}function $i(n){let e,t,i;function o(r){n[39](r)}let l={i18n:n[12],editable:n[17],recording:n[1],dispatch_blob:n[25],waveform_settings:n[13],waveform_options:n[15],handle_reset_value:n[16]};return n[23]!==void 0&&(l.mode=n[23]),e=new ki({props:l}),We.push(()=>Oe(e,"mode",o)),e.$on("start_recording",n[40]),e.$on("pause_recording",n[41]),e.$on("stop_recording",n[42]),{c(){oe(e.$$.fragment)},m(r,u){re(e,r,u),i=!0},p(r,u){const s={};u[0]&4096&&(s.i18n=r[12]),u[0]&131072&&(s.editable=r[17]),u[0]&2&&(s.recording=r[1]),u[0]&8192&&(s.waveform_settings=r[13]),u[0]&32768&&(s.waveform_options=r[15]),u[0]&65536&&(s.handle_reset_value=r[16]),!t&&u[0]&8388608&&(t=!0,s.mode=r[23],je(()=>t=!1)),e.$set(s)},i(r){i||(j(e.$$.fragment,r),i=!0)},o(r){O(e.$$.fragment,r),i=!1},d(r){se(e,r)}}}function xi(n){let e,t;return e=new Ti({props:{record:n[26],recording:n[1],stop:n[29],i18n:n[12],waveform_settings:n[13],waveform_options:n[15],waiting:n[22]==="waiting"}}),{c(){oe(e.$$.fragment)},m(i,o){re(e,i,o),t=!0},p(i,o){const l={};o[0]&2&&(l.recording=i[1]),o[0]&4096&&(l.i18n=i[12]),o[0]&8192&&(l.waveform_settings=i[13]),o[0]&32768&&(l.waveform_options=i[15]),o[0]&4194304&&(l.waiting=i[22]==="waiting"),e.$set(l)},i(i){t||(j(e.$$.fragment,i),t=!0)},o(i){O(e.$$.fragment,i),t=!1},d(i){se(e,i)}}}function eo(n){let e,t,i,o,l,r,u,s,_,a,c;e=new Un({props:{show_label:n[8],Icon:qn,float:n[3]==="upload"&&n[2]===null,label:n[5]||n[12]("audio.audio")}}),o=new Wn({props:{time_limit:n[21]}});const m=[Ki,Yi],k=[];function S(h,f){return h[2]===null||h[11]?0:1}r=S(n),u=k[r]=m[r](n);function E(h){n[52](h)}let v={sources:n[10],handle_clear:n[27]};return n[3]!==void 0&&(v.active_source=n[3]),_=new On({props:v}),We.push(()=>Oe(_,"active_source",E)),{c(){oe(e.$$.fragment),t=Ue(),i=Oi("div"),oe(o.$$.fragment),l=Ue(),u.c(),s=Ue(),oe(_.$$.fragment),qi(i,"class","audio-container svelte-cbyffp")},m(h,f){re(e,h,f),Ce(h,t,f),Ce(h,i,f),re(o,i,null),xt(i,l),k[r].m(i,null),xt(i,s),re(_,i,null),c=!0},p(h,f){const b={};f[0]&256&&(b.show_label=h[8]),f[0]&12&&(b.float=h[3]==="upload"&&h[2]===null),f[0]&4128&&(b.label=h[5]||h[12]("audio.audio")),e.$set(b);const I={};f[0]&2097152&&(I.time_limit=h[21]),o.$set(I);let w=r;r=S(h),r===w?k[r].p(h,f):(Et(),O(k[w],1,1,()=>{k[w]=null}),St(),u=k[r],u?u.p(h,f):(u=k[r]=m[r](h),u.c()),j(u,1),u.m(i,s));const P={};f[0]&1024&&(P.sources=h[10]),!a&&f[0]&8&&(a=!0,P.active_source=h[3],je(()=>a=!1)),_.$set(P)},i(h){c||(j(e.$$.fragment,h),j(o.$$.fragment,h),j(u),j(_.$$.fragment,h),c=!0)},o(h){O(e.$$.fragment,h),O(o.$$.fragment,h),O(u),O(_.$$.fragment,h),c=!1},d(h){h&&(Ae(t),Ae(i)),se(e,h),se(o),k[r].d(),se(_)}}}const en=44;function to(n,e,t){let{$$slots:i={},$$scope:o}=e,{value:l=null}=e,{label:r}=e,{root:u}=e,{loop:s}=e,{show_label:_=!0}=e,{show_download_button:a=!1}=e,{sources:c=["microphone","upload"]}=e,{pending:m=!1}=e,{streaming:k=!1}=e,{i18n:S}=e,{waveform_settings:E}=e,{trim_region_settings:v={}}=e,{waveform_options:h={}}=e,{dragging:f}=e,{active_source:b}=e,{handle_reset_value:I=()=>{}}=e,{editable:w=!0}=e,{max_file_size:P=null}=e,{upload:W}=e,{stream_handler:T}=e,{stream_every:N}=e,{uploading:M=!1}=e,{recording:y=!1}=e,H=null,B="closed";const K=d=>{d==="closed"?(t(21,H=null),t(22,B="closed")):d==="waiting"?t(22,B="waiting"):t(22,B="open")},_e=d=>{y&&t(21,H=d)};let R,C="",D,V=[],Z=!1,Q=!1,$=[],x;function p(){x=[Bt(()=>import("./module-rlRbRtCG.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),Bt(()=>import("./module-BA06XY8_.js"),__vite__mapDeps([4,1]),import.meta.url)]}typeof window<"u"&&k&&p();const L=Gi(),F=async(d,q)=>{let ee=new File(d,"audio.wav");const be=await In([ee],q==="stream");t(2,l=(await W(be,u,void 0,P||void 0))?.filter(Boolean)[0]),L(q,l)};Ji(()=>{k&&R&&R.state!=="inactive"&&R.stop()});async function ye(){let d;try{d=await navigator.mediaDevices.getUserMedia({audio:!0})}catch(q){if(!navigator.mediaDevices){L("error",S("audio.no_device_support"));return}if(q instanceof DOMException&&q.name=="NotAllowedError"){L("error",S("audio.allow_recording_access"));return}throw q}if(d!=null){if(k){const[{MediaRecorder:q,register:ee},{connect:be}]=await Promise.all(x);await ee(await be()),t(34,R=new q(d,{mimeType:"audio/wav"})),R.addEventListener("dataavailable",tt)}else t(34,R=new MediaRecorder(d)),R.addEventListener("dataavailable",q=>{$.push(q.data)});R.addEventListener("stop",async()=>{t(1,y=!1),await F($,"change"),await F($,"stop_recording"),$=[]}),Q=!0}}async function tt(d){let q=await d.data.arrayBuffer(),ee=new Uint8Array(q);if(D||(t(35,D=new Uint8Array(q.slice(0,en))),ee=new Uint8Array(q.slice(en))),m)V.push(ee);else{let be=[D].concat(V,[ee]);if(!y||B==="waiting")return;F(be,"stream"),t(36,V=[])}}async function Ge(){t(1,y=!0),L("start_recording"),Q||await ye(),t(35,D=void 0),k&&R.state!="recording"&&R.start(N*1e3)}function nt(){L("change",null),L("clear"),t(23,C=""),t(2,l=null)}function it({detail:d}){t(2,l=d),L("change",d),L("upload",d)}async function Ye(){t(1,y=!1),k&&(L("close_stream"),L("stop_recording"),R.stop(),m&&t(37,Z=!0),F($,"stop_recording"),L("clear"),t(23,C=""))}function ot(d){C=d,t(23,C)}function st(d){pe.call(this,n,d)}function rt(d){pe.call(this,n,d)}function lt(d){pe.call(this,n,d)}function at(d){f=d,t(0,f)}function ut(d){M=d,t(4,M)}const _t=({detail:d})=>L("error",d),ct=()=>t(23,C="edit");function ft(d){C=d,t(23,C)}function dt(d){pe.call(this,n,d)}function mt(d){pe.call(this,n,d)}function gt(d){pe.call(this,n,d)}function ht(d){pe.call(this,n,d)}function bt(d){b=d,t(3,b)}return n.$$set=d=>{"value"in d&&t(2,l=d.value),"label"in d&&t(5,r=d.label),"root"in d&&t(6,u=d.root),"loop"in d&&t(7,s=d.loop),"show_label"in d&&t(8,_=d.show_label),"show_download_button"in d&&t(9,a=d.show_download_button),"sources"in d&&t(10,c=d.sources),"pending"in d&&t(30,m=d.pending),"streaming"in d&&t(11,k=d.streaming),"i18n"in d&&t(12,S=d.i18n),"waveform_settings"in d&&t(13,E=d.waveform_settings),"trim_region_settings"in d&&t(14,v=d.trim_region_settings),"waveform_options"in d&&t(15,h=d.waveform_options),"dragging"in d&&t(0,f=d.dragging),"active_source"in d&&t(3,b=d.active_source),"handle_reset_value"in d&&t(16,I=d.handle_reset_value),"editable"in d&&t(17,w=d.editable),"max_file_size"in d&&t(18,P=d.max_file_size),"upload"in d&&t(19,W=d.upload),"stream_handler"in d&&t(20,T=d.stream_handler),"stream_every"in d&&t(31,N=d.stream_every),"uploading"in d&&t(4,M=d.uploading),"recording"in d&&t(1,y=d.recording),"$$scope"in d&&t(53,o=d.$$scope)},n.$$.update=()=>{if(n.$$.dirty[0]&1&&L("drag",f),n.$$.dirty[0]&1073741824|n.$$.dirty[1]&112&&Z&&m===!1&&(t(37,Z=!1),D&&V)){let d=[D].concat(V);t(36,V=[]),F(d,"stream")}n.$$.dirty[0]&2|n.$$.dirty[1]&8&&!y&&R&&Ye(),n.$$.dirty[0]&2|n.$$.dirty[1]&8&&y&&R&&Ge()},[f,y,l,b,M,r,u,s,_,a,c,k,S,E,v,h,I,w,P,W,T,H,B,C,L,F,Ge,nt,it,Ye,m,N,K,_e,R,D,V,Z,i,ot,st,rt,lt,at,ut,_t,ct,ft,dt,mt,gt,ht,bt,o]}class no extends Ui{constructor(e){super(),Fi(this,e,to,eo,Hi,{value:2,label:5,root:6,loop:7,show_label:8,show_download_button:9,sources:10,pending:30,streaming:11,i18n:12,waveform_settings:13,trim_region_settings:14,waveform_options:15,dragging:0,active_source:3,handle_reset_value:16,editable:17,max_file_size:18,upload:19,stream_handler:20,stream_every:31,uploading:4,recording:1,modify_stream:32,set_time_limit:33},null,[-1,-1])}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),z()}get label(){return this.$$.ctx[5]}set label(e){this.$$set({label:e}),z()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),z()}get loop(){return this.$$.ctx[7]}set loop(e){this.$$set({loop:e}),z()}get show_label(){return this.$$.ctx[8]}set show_label(e){this.$$set({show_label:e}),z()}get show_download_button(){return this.$$.ctx[9]}set show_download_button(e){this.$$set({show_download_button:e}),z()}get sources(){return this.$$.ctx[10]}set sources(e){this.$$set({sources:e}),z()}get pending(){return this.$$.ctx[30]}set pending(e){this.$$set({pending:e}),z()}get streaming(){return this.$$.ctx[11]}set streaming(e){this.$$set({streaming:e}),z()}get i18n(){return this.$$.ctx[12]}set i18n(e){this.$$set({i18n:e}),z()}get waveform_settings(){return this.$$.ctx[13]}set waveform_settings(e){this.$$set({waveform_settings:e}),z()}get trim_region_settings(){return this.$$.ctx[14]}set trim_region_settings(e){this.$$set({trim_region_settings:e}),z()}get waveform_options(){return this.$$.ctx[15]}set waveform_options(e){this.$$set({waveform_options:e}),z()}get dragging(){return this.$$.ctx[0]}set dragging(e){this.$$set({dragging:e}),z()}get active_source(){return this.$$.ctx[3]}set active_source(e){this.$$set({active_source:e}),z()}get handle_reset_value(){return this.$$.ctx[16]}set handle_reset_value(e){this.$$set({handle_reset_value:e}),z()}get editable(){return this.$$.ctx[17]}set editable(e){this.$$set({editable:e}),z()}get max_file_size(){return this.$$.ctx[18]}set max_file_size(e){this.$$set({max_file_size:e}),z()}get upload(){return this.$$.ctx[19]}set upload(e){this.$$set({upload:e}),z()}get stream_handler(){return this.$$.ctx[20]}set stream_handler(e){this.$$set({stream_handler:e}),z()}get stream_every(){return this.$$.ctx[31]}set stream_every(e){this.$$set({stream_every:e}),z()}get uploading(){return this.$$.ctx[4]}set uploading(e){this.$$set({uploading:e}),z()}get recording(){return this.$$.ctx[1]}set recording(e){this.$$set({recording:e}),z()}get modify_stream(){return this.$$.ctx[32]}get set_time_limit(){return this.$$.ctx[33]}}const io=no,{SvelteComponent:oo,add_flush_callback:Be,assign:kn,bind:Le,binding_callbacks:ze,check_outros:so,create_component:we,destroy_component:ve,detach:Mt,empty:ro,flush:A,get_spread_object:yn,get_spread_update:Rn,group_outros:lo,init:ao,insert:At,mount_component:ke,safe_not_equal:uo,space:Dn,transition_in:ae,transition_out:ue}=window.__gradio__svelte__internal,{afterUpdate:_o,onMount:co}=window.__gradio__svelte__internal;function fo(n){let e,t;return e=new on({props:{variant:n[0]===null&&n[25]==="upload"?"dashed":"solid",border_mode:n[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:n[4],elem_classes:n[5],visible:n[6],container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[ho]},$$scope:{ctx:n}}}),{c(){we(e.$$.fragment)},m(i,o){ke(e,i,o),t=!0},p(i,o){const l={};o[0]&33554433&&(l.variant=i[0]===null&&i[25]==="upload"?"dashed":"solid"),o[0]&134217728&&(l.border_mode=i[27]?"focus":"base"),o[0]&16&&(l.elem_id=i[4]),o[0]&32&&(l.elem_classes=i[5]),o[0]&64&&(l.visible=i[6]),o[0]&4096&&(l.container=i[12]),o[0]&8192&&(l.scale=i[13]),o[0]&16384&&(l.min_width=i[14]),o[0]&536710927|o[2]&128&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(ae(e.$$.fragment,i),t=!0)},o(i){ue(e.$$.fragment,i),t=!1},d(i){ve(e,i)}}}function mo(n){let e,t;return e=new on({props:{variant:"solid",border_mode:n[27]?"focus":"base",padding:!1,allow_overflow:!1,elem_id:n[4],elem_classes:n[5],visible:n[6],container:n[12],scale:n[13],min_width:n[14],$$slots:{default:[bo]},$$scope:{ctx:n}}}),{c(){we(e.$$.fragment)},m(i,o){ke(e,i,o),t=!0},p(i,o){const l={};o[0]&134217728&&(l.border_mode=i[27]?"focus":"base"),o[0]&16&&(l.elem_id=i[4]),o[0]&32&&(l.elem_classes=i[5]),o[0]&64&&(l.visible=i[6]),o[0]&4096&&(l.container=i[12]),o[0]&8192&&(l.scale=i[13]),o[0]&16384&&(l.min_width=i[14]),o[0]&277842435|o[2]&128&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(ae(e.$$.fragment,i),t=!0)},o(i){ue(e.$$.fragment,i),t=!1},d(i){ve(e,i)}}}function go(n){let e,t;return e=new Fn({props:{i18n:n[23].i18n,type:"audio"}}),{c(){we(e.$$.fragment)},m(i,o){ke(e,i,o),t=!0},p(i,o){const l={};o[0]&8388608&&(l.i18n=i[23].i18n),e.$set(l)},i(i){t||(ae(e.$$.fragment,i),t=!0)},o(i){ue(e.$$.fragment,i),t=!1},d(i){ve(e,i)}}}function ho(n){let e,t,i,o,l,r,u,s,_;const a=[{autoscroll:n[23].autoscroll},{i18n:n[23].i18n},n[1]];let c={};for(let f=0;f<a.length;f+=1)c=kn(c,a[f]);e=new nn({props:c}),e.$on("clear_status",n[45]);function m(f){n[48](f)}function k(f){n[49](f)}function S(f){n[50](f)}function E(f){n[51](f)}function v(f){n[52](f)}let h={label:n[9],show_label:n[11],show_download_button:n[16],value:n[0],root:n[10],sources:n[8],active_source:n[25],pending:n[20],streaming:n[21],loop:n[15],max_file_size:n[23].max_file_size,handle_reset_value:n[29],editable:n[18],i18n:n[23].i18n,waveform_settings:n[28],waveform_options:n[19],trim_region_settings:n[30],stream_every:n[22],upload:n[46],stream_handler:n[47],$$slots:{default:[go]},$$scope:{ctx:n}};return n[2]!==void 0&&(h.recording=n[2]),n[27]!==void 0&&(h.dragging=n[27]),n[24]!==void 0&&(h.uploading=n[24]),n[26]!==void 0&&(h.modify_stream=n[26]),n[3]!==void 0&&(h.set_time_limit=n[3]),i=new io({props:h}),ze.push(()=>Le(i,"recording",m)),ze.push(()=>Le(i,"dragging",k)),ze.push(()=>Le(i,"uploading",S)),ze.push(()=>Le(i,"modify_stream",E)),ze.push(()=>Le(i,"set_time_limit",v)),i.$on("change",n[53]),i.$on("stream",n[54]),i.$on("drag",n[55]),i.$on("edit",n[56]),i.$on("play",n[57]),i.$on("pause",n[58]),i.$on("stop",n[59]),i.$on("start_recording",n[60]),i.$on("pause_recording",n[61]),i.$on("stop_recording",n[62]),i.$on("upload",n[63]),i.$on("clear",n[64]),i.$on("error",n[31]),i.$on("close_stream",n[65]),{c(){we(e.$$.fragment),t=Dn(),we(i.$$.fragment)},m(f,b){ke(e,f,b),At(f,t,b),ke(i,f,b),_=!0},p(f,b){const I=b[0]&8388610?Rn(a,[b[0]&8388608&&{autoscroll:f[23].autoscroll},b[0]&8388608&&{i18n:f[23].i18n},b[0]&2&&yn(f[1])]):{};e.$set(I);const w={};b[0]&512&&(w.label=f[9]),b[0]&2048&&(w.show_label=f[11]),b[0]&65536&&(w.show_download_button=f[16]),b[0]&1&&(w.value=f[0]),b[0]&1024&&(w.root=f[10]),b[0]&256&&(w.sources=f[8]),b[0]&33554432&&(w.active_source=f[25]),b[0]&1048576&&(w.pending=f[20]),b[0]&2097152&&(w.streaming=f[21]),b[0]&32768&&(w.loop=f[15]),b[0]&8388608&&(w.max_file_size=f[23].max_file_size),b[0]&262144&&(w.editable=f[18]),b[0]&8388608&&(w.i18n=f[23].i18n),b[0]&268435456&&(w.waveform_settings=f[28]),b[0]&524288&&(w.waveform_options=f[19]),b[0]&4194304&&(w.stream_every=f[22]),b[0]&8388608&&(w.upload=f[46]),b[0]&8388608&&(w.stream_handler=f[47]),b[0]&8388608|b[2]&128&&(w.$$scope={dirty:b,ctx:f}),!o&&b[0]&4&&(o=!0,w.recording=f[2],Be(()=>o=!1)),!l&&b[0]&134217728&&(l=!0,w.dragging=f[27],Be(()=>l=!1)),!r&&b[0]&16777216&&(r=!0,w.uploading=f[24],Be(()=>r=!1)),!u&&b[0]&67108864&&(u=!0,w.modify_stream=f[26],Be(()=>u=!1)),!s&&b[0]&8&&(s=!0,w.set_time_limit=f[3],Be(()=>s=!1)),i.$set(w)},i(f){_||(ae(e.$$.fragment,f),ae(i.$$.fragment,f),_=!0)},o(f){ue(e.$$.fragment,f),ue(i.$$.fragment,f),_=!1},d(f){f&&Mt(t),ve(e,f),ve(i,f)}}}function bo(n){let e,t,i,o;const l=[{autoscroll:n[23].autoscroll},{i18n:n[23].i18n},n[1]];let r={};for(let u=0;u<l.length;u+=1)r=kn(r,l[u]);return e=new nn({props:r}),e.$on("clear_status",n[39]),i=new zn({props:{i18n:n[23].i18n,show_label:n[11],show_download_button:n[16],show_share_button:n[17],value:n[0],label:n[9],loop:n[15],waveform_settings:n[28],waveform_options:n[19],editable:n[18]}}),i.$on("share",n[40]),i.$on("error",n[41]),i.$on("play",n[42]),i.$on("pause",n[43]),i.$on("stop",n[44]),{c(){we(e.$$.fragment),t=Dn(),we(i.$$.fragment)},m(u,s){ke(e,u,s),At(u,t,s),ke(i,u,s),o=!0},p(u,s){const _=s[0]&8388610?Rn(l,[s[0]&8388608&&{autoscroll:u[23].autoscroll},s[0]&8388608&&{i18n:u[23].i18n},s[0]&2&&yn(u[1])]):{};e.$set(_);const a={};s[0]&8388608&&(a.i18n=u[23].i18n),s[0]&2048&&(a.show_label=u[11]),s[0]&65536&&(a.show_download_button=u[16]),s[0]&131072&&(a.show_share_button=u[17]),s[0]&1&&(a.value=u[0]),s[0]&512&&(a.label=u[9]),s[0]&32768&&(a.loop=u[15]),s[0]&268435456&&(a.waveform_settings=u[28]),s[0]&524288&&(a.waveform_options=u[19]),s[0]&262144&&(a.editable=u[18]),i.$set(a)},i(u){o||(ae(e.$$.fragment,u),ae(i.$$.fragment,u),o=!0)},o(u){ue(e.$$.fragment,u),ue(i.$$.fragment,u),o=!1},d(u){u&&Mt(t),ve(e,u),ve(i,u)}}}function po(n){let e,t,i,o;const l=[mo,fo],r=[];function u(s,_){return s[7]?1:0}return e=u(n),t=r[e]=l[e](n),{c(){t.c(),i=ro()},m(s,_){r[e].m(s,_),At(s,i,_),o=!0},p(s,_){let a=e;e=u(s),e===a?r[e].p(s,_):(lo(),ue(r[a],1,1,()=>{r[a]=null}),so(),t=r[e],t?t.p(s,_):(t=r[e]=l[e](s),t.c()),ae(t,1),t.m(i.parentNode,i))},i(s){o||(ae(t),o=!0)},o(s){ue(t),o=!1},d(s){s&&Mt(i),r[e].d(s)}}}function wo(n,e,t){let{value_is_output:i=!1}=e,{elem_id:o=""}=e,{elem_classes:l=[]}=e,{visible:r=!0}=e,{interactive:u}=e,{value:s=null}=e,{sources:_}=e,{label:a}=e,{root:c}=e,{show_label:m}=e,{container:k=!0}=e,{scale:S=null}=e,{min_width:E=void 0}=e,{loading_status:v}=e,{autoplay:h=!1}=e,{loop:f=!1}=e,{show_download_button:b}=e,{show_share_button:I=!1}=e,{editable:w=!0}=e,{waveform_options:P={}}=e,{pending:W}=e,{streaming:T}=e,{stream_every:N}=e,{input_ready:M}=e,{recording:y=!1}=e,H=!1,B="closed",K;function _e(g){B=g,K(g)}const R=()=>B;let{set_time_limit:C}=e,{gradio:D}=e,V=null,Z,Q=s;const $=()=>{Q===null||s===Q||t(0,s=Q)};let x,p,J="darkorange";co(()=>{J=getComputedStyle(document?.documentElement).getPropertyValue("--color-accent"),F(),t(28,p.waveColor=P.waveform_color||"#9ca3af",p),t(28,p.progressColor=P.waveform_progress_color||J,p),t(28,p.mediaControls=P.show_controls,p),t(28,p.sampleRate=P.sample_rate||44100,p)});const L={color:P.trim_region_color,drag:!0,resize:!0};function F(){document.documentElement.style.setProperty("--trim-region-color",L.color||J)}function ye({detail:g}){const[Cn,Pn]=g.includes("Invalid file type")?["warning","complete"]:["error","error"];t(1,v=v||{}),t(1,v.status=Pn,v),t(1,v.message=g,v),D.dispatch(Cn,g)}_o(()=>{t(32,i=!1)});const tt=()=>D.dispatch("clear_status",v),Ge=g=>D.dispatch("share",g.detail),nt=g=>D.dispatch("error",g.detail),it=()=>D.dispatch("play"),Ye=()=>D.dispatch("pause"),ot=()=>D.dispatch("stop"),st=()=>D.dispatch("clear_status",v),rt=(...g)=>D.client.upload(...g),lt=(...g)=>D.client.stream(...g);function at(g){y=g,t(2,y)}function ut(g){x=g,t(27,x)}function _t(g){H=g,t(24,H)}function ct(g){K=g,t(26,K)}function ft(g){C=g,t(3,C)}const dt=({detail:g})=>t(0,s=g),mt=({detail:g})=>{t(0,s=g),D.dispatch("stream",s)},gt=({detail:g})=>t(27,x=g),ht=()=>D.dispatch("edit"),bt=()=>D.dispatch("play"),d=()=>D.dispatch("pause"),q=()=>D.dispatch("stop"),ee=()=>D.dispatch("start_recording"),be=()=>D.dispatch("pause_recording"),Sn=g=>D.dispatch("stop_recording"),En=()=>D.dispatch("upload"),Mn=()=>D.dispatch("clear"),An=()=>D.dispatch("close_stream","stream");return n.$$set=g=>{"value_is_output"in g&&t(32,i=g.value_is_output),"elem_id"in g&&t(4,o=g.elem_id),"elem_classes"in g&&t(5,l=g.elem_classes),"visible"in g&&t(6,r=g.visible),"interactive"in g&&t(7,u=g.interactive),"value"in g&&t(0,s=g.value),"sources"in g&&t(8,_=g.sources),"label"in g&&t(9,a=g.label),"root"in g&&t(10,c=g.root),"show_label"in g&&t(11,m=g.show_label),"container"in g&&t(12,k=g.container),"scale"in g&&t(13,S=g.scale),"min_width"in g&&t(14,E=g.min_width),"loading_status"in g&&t(1,v=g.loading_status),"autoplay"in g&&t(34,h=g.autoplay),"loop"in g&&t(15,f=g.loop),"show_download_button"in g&&t(16,b=g.show_download_button),"show_share_button"in g&&t(17,I=g.show_share_button),"editable"in g&&t(18,w=g.editable),"waveform_options"in g&&t(19,P=g.waveform_options),"pending"in g&&t(20,W=g.pending),"streaming"in g&&t(21,T=g.streaming),"stream_every"in g&&t(22,N=g.stream_every),"input_ready"in g&&t(33,M=g.input_ready),"recording"in g&&t(2,y=g.recording),"set_time_limit"in g&&t(3,C=g.set_time_limit),"gradio"in g&&t(23,D=g.gradio)},n.$$.update=()=>{n.$$.dirty[0]&16777216&&t(33,M=!H),n.$$.dirty[0]&1|n.$$.dirty[1]&128&&s&&Q===null&&t(38,Q=s),n.$$.dirty[0]&8388609|n.$$.dirty[1]&66&&JSON.stringify(s)!==JSON.stringify(V)&&(t(37,V=s),D.dispatch("change"),i||D.dispatch("input")),n.$$.dirty[0]&33554688&&!Z&&_&&t(25,Z=_[0]),n.$$.dirty[1]&8&&t(28,p={height:50,barWidth:2,barGap:3,cursorWidth:2,cursorColor:"#ddd5e9",autoplay:h,barRadius:10,dragToSeek:!0,normalize:!0,minPxPerSec:20})},[s,v,y,C,o,l,r,u,_,a,c,m,k,S,E,f,b,I,w,P,W,T,N,D,H,Z,K,x,p,$,L,ye,i,M,h,_e,R,V,Q,tt,Ge,nt,it,Ye,ot,st,rt,lt,at,ut,_t,ct,ft,dt,mt,gt,ht,bt,d,q,ee,be,Sn,En,Mn,An]}class vo extends oo{constructor(e){super(),ao(this,e,wo,po,uo,{value_is_output:32,elem_id:4,elem_classes:5,visible:6,interactive:7,value:0,sources:8,label:9,root:10,show_label:11,container:12,scale:13,min_width:14,loading_status:1,autoplay:34,loop:15,show_download_button:16,show_share_button:17,editable:18,waveform_options:19,pending:20,streaming:21,stream_every:22,input_ready:33,recording:2,modify_stream_state:35,get_stream_state:36,set_time_limit:3,gradio:23},null,[-1,-1,-1])}get value_is_output(){return this.$$.ctx[32]}set value_is_output(e){this.$$set({value_is_output:e}),A()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),A()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),A()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),A()}get interactive(){return this.$$.ctx[7]}set interactive(e){this.$$set({interactive:e}),A()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),A()}get sources(){return this.$$.ctx[8]}set sources(e){this.$$set({sources:e}),A()}get label(){return this.$$.ctx[9]}set label(e){this.$$set({label:e}),A()}get root(){return this.$$.ctx[10]}set root(e){this.$$set({root:e}),A()}get show_label(){return this.$$.ctx[11]}set show_label(e){this.$$set({show_label:e}),A()}get container(){return this.$$.ctx[12]}set container(e){this.$$set({container:e}),A()}get scale(){return this.$$.ctx[13]}set scale(e){this.$$set({scale:e}),A()}get min_width(){return this.$$.ctx[14]}set min_width(e){this.$$set({min_width:e}),A()}get loading_status(){return this.$$.ctx[1]}set loading_status(e){this.$$set({loading_status:e}),A()}get autoplay(){return this.$$.ctx[34]}set autoplay(e){this.$$set({autoplay:e}),A()}get loop(){return this.$$.ctx[15]}set loop(e){this.$$set({loop:e}),A()}get show_download_button(){return this.$$.ctx[16]}set show_download_button(e){this.$$set({show_download_button:e}),A()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),A()}get editable(){return this.$$.ctx[18]}set editable(e){this.$$set({editable:e}),A()}get waveform_options(){return this.$$.ctx[19]}set waveform_options(e){this.$$set({waveform_options:e}),A()}get pending(){return this.$$.ctx[20]}set pending(e){this.$$set({pending:e}),A()}get streaming(){return this.$$.ctx[21]}set streaming(e){this.$$set({streaming:e}),A()}get stream_every(){return this.$$.ctx[22]}set stream_every(e){this.$$set({stream_every:e}),A()}get input_ready(){return this.$$.ctx[33]}set input_ready(e){this.$$set({input_ready:e}),A()}get recording(){return this.$$.ctx[2]}set recording(e){this.$$set({recording:e}),A()}get modify_stream_state(){return this.$$.ctx[35]}get get_stream_state(){return this.$$.ctx[36]}get set_time_limit(){return this.$$.ctx[3]}set set_time_limit(e){this.$$set({set_time_limit:e}),A()}get gradio(){return this.$$.ctx[23]}set gradio(e){this.$$set({gradio:e}),A()}}const ns=vo;export{ss as BaseExample,io as BaseInteractiveAudio,Ln as BasePlayer,zn as BaseStaticAudio,ns as default};
//# sourceMappingURL=index-CJYUQBJu.js.map
