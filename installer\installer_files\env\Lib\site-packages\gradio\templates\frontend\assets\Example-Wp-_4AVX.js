const{SvelteComponent:f,append:g,attr:_,detach:d,element:h,flush:r,init:o,insert:v,noop:c,safe_not_equal:y,set_data:m,text:b,toggle_class:u}=window.__gradio__svelte__internal;function p(s){let e,a=(s[0]?s[0]:"")+"",n;return{c(){e=h("pre"),n=b(a),_(e,"class","svelte-agpzo2"),u(e,"table",s[1]==="table"),u(e,"gallery",s[1]==="gallery"),u(e,"selected",s[2])},m(t,l){v(t,e,l),g(e,n)},p(t,[l]){l&1&&a!==(a=(t[0]?t[0]:"")+"")&&m(n,a),l&2&&u(e,"table",t[1]==="table"),l&2&&u(e,"gallery",t[1]==="gallery"),l&4&&u(e,"selected",t[2])},i:c,o:c,d(t){t&&d(e)}}}function w(s,e,a){let{value:n}=e,{type:t}=e,{selected:l=!1}=e;return s.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,l=i.selected)},[n,t,l]}class q extends f{constructor(e){super(),o(this,e,w,p,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),r()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),r()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),r()}}export{q as default};
//# sourceMappingURL=Example-Wp-_4AVX.js.map
