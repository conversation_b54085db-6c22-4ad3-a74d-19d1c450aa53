{"version": 3, "file": "index32-CrY4giu-.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index32.js"], "sourcesContent": ["import { L as LRParser } from \"./index12.js\";\nimport { s as styleTags, t as tags, L as LRLanguage, i as indentNodeProp, w as continuedIndent, f as foldNodeProp, c as foldInside, e as LanguageSupport } from \"./Index13.js\";\nconst jsonHighlighting = styleTags({\n  String: tags.string,\n  Number: tags.number,\n  \"True False\": tags.bool,\n  PropertyName: tags.propertyName,\n  Null: tags.null,\n  \",\": tags.separator,\n  \"[ ]\": tags.squareBracket,\n  \"{ }\": tags.brace\n});\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"$bOVQPOOOOQO'#Cb'#CbOnQPO'#CeOvQPO'#CjOOQO'#Cp'#CpQOQPOOOOQO'#Cg'#CgO}QPO'#CfO!SQPO'#CrOOQO,59P,59PO![QPO,59PO!aQPO'#CuOOQO,59U,59UO!iQPO,59UOVQPO,59QOqQPO'#CkO!nQPO,59^OOQO1G.k1G.kOVQPO'#ClO!vQPO,59aOOQO1G.p1G.pOOQO1G.l1G.lOOQO,59V,59VOOQO-E6i-E6iOOQO,59W,59WOOQO-E6j-E6j\",\n  stateData: \"#O~OcOS~OQSORSOSSOTSOWQO]ROePO~OVXOeUO~O[[O~PVOg^O~Oh_OVfX~OVaO~OhbO[iX~O[dO~Oh_OVfa~OhbO[ia~O\",\n  goto: \"!kjPPPPPPkPPkqwPPk{!RPPP!XP!ePP!hXSOR^bQWQRf_TVQ_Q`WRg`QcZRicQTOQZRQe^RhbRYQR]R\",\n  nodeNames: \"⚠ JsonText True False Null Number String } { Object Property PropertyName ] [ Array\",\n  maxTerm: 25,\n  nodeProps: [\n    [\"openedBy\", 7, \"{\", 12, \"[\"],\n    [\"closedBy\", 8, \"}\", 13, \"]\"]\n  ],\n  propSources: [jsonHighlighting],\n  skippedNodes: [0],\n  repeatNodeCount: 2,\n  tokenData: \"(|~RaXY!WYZ!W]^!Wpq!Wrs!]|}$u}!O$z!Q!R%T!R![&c![!]&t!}#O&y#P#Q'O#Y#Z'T#b#c'r#h#i(Z#o#p(r#q#r(w~!]Oc~~!`Wpq!]qr!]rs!xs#O!]#O#P!}#P;'S!];'S;=`$o<%lO!]~!}Oe~~#QXrs!]!P!Q!]#O#P!]#U#V!]#Y#Z!]#b#c!]#f#g!]#h#i!]#i#j#m~#pR!Q![#y!c!i#y#T#Z#y~#|R!Q![$V!c!i$V#T#Z$V~$YR!Q![$c!c!i$c#T#Z$c~$fR!Q![!]!c!i!]#T#Z!]~$rP;=`<%l!]~$zOh~~$}Q!Q!R%T!R![&c~%YRT~!O!P%c!g!h%w#X#Y%w~%fP!Q![%i~%nRT~!Q![%i!g!h%w#X#Y%w~%zR{|&T}!O&T!Q![&Z~&WP!Q![&Z~&`PT~!Q![&Z~&hST~!O!P%c!Q![&c!g!h%w#X#Y%w~&yOg~~'OO]~~'TO[~~'WP#T#U'Z~'^P#`#a'a~'dP#g#h'g~'jP#X#Y'm~'rOR~~'uP#i#j'x~'{P#`#a(O~(RP#`#a(U~(ZOS~~(^P#f#g(a~(dP#i#j(g~(jP#X#Y(m~(rOQ~~(wOW~~(|OV~\",\n  tokenizers: [0],\n  topRules: { \"JsonText\": [0, 1] },\n  tokenPrec: 0\n});\nconst jsonParseLinter = () => (view) => {\n  try {\n    JSON.parse(view.state.doc.toString());\n  } catch (e) {\n    if (!(e instanceof SyntaxError))\n      throw e;\n    const pos = getErrorPosition(e, view.state.doc);\n    return [{\n      from: pos,\n      message: e.message,\n      severity: \"error\",\n      to: pos\n    }];\n  }\n  return [];\n};\nfunction getErrorPosition(error, doc) {\n  let m;\n  if (m = error.message.match(/at position (\\d+)/))\n    return Math.min(+m[1], doc.length);\n  if (m = error.message.match(/at line (\\d+) column (\\d+)/))\n    return Math.min(doc.line(+m[1]).from + +m[2] - 1, doc.length);\n  return 0;\n}\nconst jsonLanguage = /* @__PURE__ */ LRLanguage.define({\n  name: \"json\",\n  parser: /* @__PURE__ */ parser.configure({\n    props: [\n      /* @__PURE__ */ indentNodeProp.add({\n        Object: /* @__PURE__ */ continuedIndent({ except: /^\\s*\\}/ }),\n        Array: /* @__PURE__ */ continuedIndent({ except: /^\\s*\\]/ })\n      }),\n      /* @__PURE__ */ foldNodeProp.add({\n        \"Object Array\": foldInside\n      })\n    ]\n  }),\n  languageData: {\n    closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n    indentOnInput: /^\\s*[\\}\\]]$/\n  }\n});\nfunction json() {\n  return new LanguageSupport(jsonLanguage);\n}\nexport {\n  json,\n  jsonLanguage,\n  jsonParseLinter\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA,MAAM,gBAAgB,GAAG,SAAS,CAAC;AACnC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;AACrB,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;AACrB,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI;AACzB,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY;AACjC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI;AACjB,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS;AACrB,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa;AAC3B,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK;AACnB,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC;AACpC,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,MAAM,EAAE,kRAAkR;AAC5R,EAAE,SAAS,EAAE,gGAAgG;AAC7G,EAAE,IAAI,EAAE,iFAAiF;AACzF,EAAE,SAAS,EAAE,qFAAqF;AAClG,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,SAAS,EAAE;AACb,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AACjC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;AACjC,GAAG;AACH,EAAE,WAAW,EAAE,CAAC,gBAAgB,CAAC;AACjC,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AACnB,EAAE,eAAe,EAAE,CAAC;AACpB,EAAE,SAAS,EAAE,mmBAAmmB;AAChnB,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AACjB,EAAE,QAAQ,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;AAClC,EAAE,SAAS,EAAE,CAAC;AACd,CAAC,CAAC,CAAC;AACE,MAAC,eAAe,GAAG,MAAM,CAAC,IAAI,KAAK;AACxC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC1C,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,YAAY,WAAW,CAAC;AACnC,MAAM,MAAM,CAAC,CAAC;AACd,IAAI,MAAM,GAAG,GAAG,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpD,IAAI,OAAO,CAAC;AACZ,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,OAAO,EAAE,CAAC,CAAC,OAAO;AACxB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,EAAE,EAAE,GAAG;AACb,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE;AACF,SAAS,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE;AACtC,EAAE,IAAI,CAAC,CAAC;AACR,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACvC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AAClE,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACI,MAAC,YAAY,mBAAmB,UAAU,CAAC,MAAM,CAAC;AACvD,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,kBAAkB,MAAM,CAAC,SAAS,CAAC;AAC3C,IAAI,KAAK,EAAE;AACX,sBAAsB,cAAc,CAAC,GAAG,CAAC;AACzC,QAAQ,MAAM,kBAAkB,eAAe,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;AACrE,QAAQ,KAAK,kBAAkB,eAAe,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;AACpE,OAAO,CAAC;AACR,sBAAsB,YAAY,CAAC,GAAG,CAAC;AACvC,QAAQ,cAAc,EAAE,UAAU;AAClC,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,YAAY,EAAE;AAChB,IAAI,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;AAChD,IAAI,aAAa,EAAE,aAAa;AAChC,GAAG;AACH,CAAC,EAAE;AACH,SAAS,IAAI,GAAG;AAChB,EAAE,OAAO,IAAI,eAAe,CAAC,YAAY,CAAC,CAAC;AAC3C;;;;"}