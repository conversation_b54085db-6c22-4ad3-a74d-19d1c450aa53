{"version": 3, "file": "Example-uQ8MuYg6.js", "sources": ["../../../../js/model3D/Example.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\texport let value: string | null;\n\texport let type: \"gallery\" | \"table\";\n\texport let selected = false;\n</script>\n\n<div\n\tclass:table={type === \"table\"}\n\tclass:gallery={type === \"gallery\"}\n\tclass:selected\n>\n\t{value ? value : \"\"}\n</div>\n\n<style>\n\t.gallery {\n\t\tpadding: var(--size-1) var(--size-2);\n\t}\n</style>\n"], "names": ["ctx", "toggle_class", "div", "insert", "target", "anchor", "set_data", "t", "t_value", "value", "$$props", "type", "selected"], "mappings": "sMAWEA,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,gEAJNC,EAAAC,EAAA,QAAAF,OAAS,OAAO,EACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,+BAFlCG,EAMKC,EAAAF,EAAAG,CAAA,gCADHL,EAAK,CAAA,EAAGA,EAAK,CAAA,EAAG,IAAE,KAAAM,EAAAC,EAAAC,CAAA,OAJNP,EAAAC,EAAA,QAAAF,OAAS,OAAO,OACdC,EAAAC,EAAA,UAAAF,OAAS,SAAS,sEAPtB,GAAA,CAAA,MAAAS,CAAA,EAAAC,EACA,CAAA,KAAAC,CAAA,EAAAD,GACA,SAAAE,EAAW,EAAA,EAAAF"}