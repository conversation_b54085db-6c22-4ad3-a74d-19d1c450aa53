import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{B as Ie}from"./BlockTitle-IUerRYnJ.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{C as Je}from"./Check-BiRlaMNo.js";import{C as Me}from"./Copy-CxQ9EyK2.js";import{S as Oe}from"./Send-DyoOovnk.js";import{S as Pe}from"./Square-oAGqOwsh.js";import{f as Qe}from"./index-C30MtwUc.js";/* empty css                                              */const{SvelteComponent:Re,action_destroyer:Ve,add_render_callback:We,append:j,attr:f,binding_callbacks:P,bubble:F,check_outros:U,create_component:A,create_in_transition:Xe,destroy_component:G,detach:H,element:E,empty:he,flush:w,group_outros:Y,init:Ze,insert:S,is_function:xe,listen:d,mount_component:I,noop:K,run_all:R,safe_not_equal:$e,set_data:te,set_input_value:L,space:Q,text:le,toggle_class:z,transition_in:y,transition_out:T}=window.__gradio__svelte__internal,{beforeUpdate:et,afterUpdate:tt,createEventDispatcher:lt,tick:fe}=window.__gradio__svelte__internal;function ae(l){let e,t,n,o;const s=[it,nt],u=[];function a(r,c){return r[19]?0:1}return e=a(l),t=u[e]=s[e](l),{c(){t.c(),n=he()},m(r,c){u[e].m(r,c),S(r,n,c),o=!0},p(r,c){let _=e;e=a(r),e===_?u[e].p(r,c):(Y(),T(u[_],1,1,()=>{u[_]=null}),U(),t=u[e],t?t.p(r,c):(t=u[e]=s[e](r),t.c()),y(t,1),t.m(n.parentNode,n))},i(r){o||(y(t),o=!0)},o(r){T(t),o=!1},d(r){r&&H(n),u[e].d(r)}}}function nt(l){let e,t,n,o,s;return t=new Me({}),{c(){e=E("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copy"),f(e,"aria-roledescription","Copy text")},m(u,a){S(u,e,a),I(t,e,null),n=!0,o||(s=d(e,"click",l[21]),o=!0)},p:K,i(u){n||(y(t.$$.fragment,u),n=!0)},o(u){T(t.$$.fragment,u),n=!1},d(u){u&&H(e),G(t),o=!1,s()}}}function it(l){let e,t,n,o;return t=new Je({}),{c(){e=E("button"),A(t.$$.fragment),f(e,"class","copy-button svelte-173056l"),f(e,"aria-label","Copied"),f(e,"aria-roledescription","Text copied")},m(s,u){S(s,e,u),I(t,e,null),o=!0},p:K,i(s){o||(y(t.$$.fragment,s),s&&(n||We(()=>{n=Xe(e,Qe,{duration:300}),n.start()})),o=!0)},o(s){T(t.$$.fragment,s),o=!1},d(s){s&&H(e),G(t)}}}function ot(l){let e;return{c(){e=le(l[3])},m(t,n){S(t,e,n)},p(t,n){n[0]&8&&te(e,t[3])},d(t){t&&H(e)}}}function st(l){let e,t,n,o,s,u;return{c(){e=E("textarea"),f(e,"data-testid","textbox"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[13]?"rtl":"ltr"),f(e,"placeholder",l[2]),f(e,"rows",l[1]),e.disabled=l[5],e.autofocus=l[14],f(e,"maxlength",l[16]),f(e,"style",n=l[15]?"text-align: "+l[15]:""),z(e,"no-label",!l[6]&&(l[11]||l[12]))},m(a,r){S(a,e,r),L(e,l[0]),l[45](e),l[14]&&e.focus(),s||(u=[Ve(o=l[27].call(null,e,l[0])),d(e,"input",l[44]),d(e,"keypress",l[23]),d(e,"blur",l[36]),d(e,"select",l[22]),d(e,"focus",l[37]),d(e,"scroll",l[24])],s=!0)},p(a,r){r[0]&8192&&t!==(t=a[13]?"rtl":"ltr")&&f(e,"dir",t),r[0]&4&&f(e,"placeholder",a[2]),r[0]&2&&f(e,"rows",a[1]),r[0]&32&&(e.disabled=a[5]),r[0]&16384&&(e.autofocus=a[14]),r[0]&65536&&f(e,"maxlength",a[16]),r[0]&32768&&n!==(n=a[15]?"text-align: "+a[15]:"")&&f(e,"style",n),o&&xe(o.update)&&r[0]&1&&o.update.call(null,a[0]),r[0]&1&&L(e,a[0]),r[0]&6208&&z(e,"no-label",!a[6]&&(a[11]||a[12]))},d(a){a&&H(e),l[45](null),s=!1,R(u)}}}function ut(l){let e;function t(s,u){if(s[9]==="text")return at;if(s[9]==="password")return ft;if(s[9]==="email")return rt}let n=t(l),o=n&&n(l);return{c(){o&&o.c(),e=he()},m(s,u){o&&o.m(s,u),S(s,e,u)},p(s,u){n===(n=t(s))&&o?o.p(s,u):(o&&o.d(1),o=n&&n(s),o&&(o.c(),o.m(e.parentNode,e)))},d(s){s&&H(e),o&&o.d(s)}}}function rt(l){let e,t,n;return{c(){e=E("input"),f(e,"data-testid","textbox"),f(e,"type","email"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],f(e,"maxlength",l[16]),f(e,"autocomplete","email")},m(o,s){S(o,e,s),L(e,l[0]),l[43](e),l[14]&&e.focus(),t||(n=[d(e,"input",l[42]),d(e,"keypress",l[23]),d(e,"blur",l[34]),d(e,"select",l[22]),d(e,"focus",l[35])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&16384&&(e.autofocus=o[14]),s[0]&65536&&f(e,"maxlength",o[16]),s[0]&1&&e.value!==o[0]&&L(e,o[0])},d(o){o&&H(e),l[43](null),t=!1,R(n)}}}function ft(l){let e,t,n;return{c(){e=E("input"),f(e,"data-testid","password"),f(e,"type","password"),f(e,"class","scroll-hide svelte-173056l"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],f(e,"maxlength",l[16]),f(e,"autocomplete","")},m(o,s){S(o,e,s),L(e,l[0]),l[41](e),l[14]&&e.focus(),t||(n=[d(e,"input",l[40]),d(e,"keypress",l[23]),d(e,"blur",l[32]),d(e,"select",l[22]),d(e,"focus",l[33])],t=!0)},p(o,s){s[0]&4&&f(e,"placeholder",o[2]),s[0]&32&&(e.disabled=o[5]),s[0]&16384&&(e.autofocus=o[14]),s[0]&65536&&f(e,"maxlength",o[16]),s[0]&1&&e.value!==o[0]&&L(e,o[0])},d(o){o&&H(e),l[41](null),t=!1,R(n)}}}function at(l){let e,t,n,o,s;return{c(){e=E("input"),f(e,"data-testid","textbox"),f(e,"type","text"),f(e,"class","scroll-hide svelte-173056l"),f(e,"dir",t=l[13]?"rtl":"ltr"),f(e,"placeholder",l[2]),e.disabled=l[5],e.autofocus=l[14],f(e,"maxlength",l[16]),f(e,"style",n=l[15]?"text-align: "+l[15]:"")},m(u,a){S(u,e,a),L(e,l[0]),l[39](e),l[14]&&e.focus(),o||(s=[d(e,"input",l[38]),d(e,"keypress",l[23]),d(e,"blur",l[30]),d(e,"select",l[22]),d(e,"focus",l[31])],o=!0)},p(u,a){a[0]&8192&&t!==(t=u[13]?"rtl":"ltr")&&f(e,"dir",t),a[0]&4&&f(e,"placeholder",u[2]),a[0]&32&&(e.disabled=u[5]),a[0]&16384&&(e.autofocus=u[14]),a[0]&65536&&f(e,"maxlength",u[16]),a[0]&32768&&n!==(n=u[15]?"text-align: "+u[15]:"")&&f(e,"style",n),a[0]&1&&e.value!==u[0]&&L(e,u[0])},d(u){u&&H(e),l[39](null),o=!1,R(s)}}}function _e(l){let e,t,n,o,s,u;const a=[ct,_t],r=[];function c(_,m){return _[11]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=E("button"),n.c(),f(e,"class","submit-button svelte-173056l"),z(e,"padded-button",l[11]!==!0)},m(_,m){S(_,e,m),r[t].m(e,null),o=!0,s||(u=d(e,"click",l[26]),s=!0)},p(_,m){let b=t;t=c(_),t===b?r[t].p(_,m):(Y(),T(r[b],1,1,()=>{r[b]=null}),U(),n=r[t],n?n.p(_,m):(n=r[t]=a[t](_),n.c()),y(n,1),n.m(e,null)),(!o||m[0]&2048)&&z(e,"padded-button",_[11]!==!0)},i(_){o||(y(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function _t(l){let e;return{c(){e=le(l[11])},m(t,n){S(t,e,n)},p(t,n){n[0]&2048&&te(e,t[11])},i:K,o:K,d(t){t&&H(e)}}}function ct(l){let e,t;return e=new Oe({}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:K,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function ce(l){let e,t,n,o,s,u;const a=[bt,ht],r=[];function c(_,m){return _[12]===!0?0:1}return t=c(l),n=r[t]=a[t](l),{c(){e=E("button"),n.c(),f(e,"class","stop-button svelte-173056l"),z(e,"padded-button",l[12]!==!0)},m(_,m){S(_,e,m),r[t].m(e,null),o=!0,s||(u=d(e,"click",l[25]),s=!0)},p(_,m){let b=t;t=c(_),t===b?r[t].p(_,m):(Y(),T(r[b],1,1,()=>{r[b]=null}),U(),n=r[t],n?n.p(_,m):(n=r[t]=a[t](_),n.c()),y(n,1),n.m(e,null)),(!o||m[0]&4096)&&z(e,"padded-button",_[12]!==!0)},i(_){o||(y(n),o=!0)},o(_){T(n),o=!1},d(_){_&&H(e),r[t].d(),s=!1,u()}}}function ht(l){let e;return{c(){e=le(l[12])},m(t,n){S(t,e,n)},p(t,n){n[0]&4096&&te(e,t[12])},i:K,o:K,d(t){t&&H(e)}}}function bt(l){let e,t;return e=new Pe({props:{fill:"none",stroke_width:2.5}}),{c(){A(e.$$.fragment)},m(n,o){I(e,n,o),t=!0},p:K,i(n){t||(y(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){G(e,n)}}}function dt(l){let e,t,n,o,s,u,a,r,c=l[6]&&l[10]&&ae(l);n=new Ie({props:{root:l[17],show_label:l[6],info:l[4],$$slots:{default:[ot]},$$scope:{ctx:l}}});function _(h,k){return h[1]===1&&h[8]===1?ut:st}let m=_(l),b=m(l),g=l[11]&&_e(l),p=l[12]&&ce(l);return{c(){e=E("label"),c&&c.c(),t=Q(),A(n.$$.fragment),o=Q(),s=E("div"),b.c(),u=Q(),g&&g.c(),a=Q(),p&&p.c(),f(s,"class","input-container svelte-173056l"),f(e,"class","svelte-173056l"),z(e,"container",l[7]),z(e,"show_textbox_border",l[20])},m(h,k){S(h,e,k),c&&c.m(e,null),j(e,t),I(n,e,null),j(e,o),j(e,s),b.m(s,null),j(s,u),g&&g.m(s,null),j(s,a),p&&p.m(s,null),r=!0},p(h,k){h[6]&&h[10]?c?(c.p(h,k),k[0]&1088&&y(c,1)):(c=ae(h),c.c(),y(c,1),c.m(e,t)):c&&(Y(),T(c,1,1,()=>{c=null}),U());const B={};k[0]&131072&&(B.root=h[17]),k[0]&64&&(B.show_label=h[6]),k[0]&16&&(B.info=h[4]),k[0]&8|k[1]&16777216&&(B.$$scope={dirty:k,ctx:h}),n.$set(B),m===(m=_(h))&&b?b.p(h,k):(b.d(1),b=m(h),b&&(b.c(),b.m(s,u))),h[11]?g?(g.p(h,k),k[0]&2048&&y(g,1)):(g=_e(h),g.c(),y(g,1),g.m(s,a)):g&&(Y(),T(g,1,1,()=>{g=null}),U()),h[12]?p?(p.p(h,k),k[0]&4096&&y(p,1)):(p=ce(h),p.c(),y(p,1),p.m(s,null)):p&&(Y(),T(p,1,1,()=>{p=null}),U()),(!r||k[0]&128)&&z(e,"container",h[7])},i(h){r||(y(c),y(n.$$.fragment,h),y(g),y(p),r=!0)},o(h){T(c),T(n.$$.fragment,h),T(g),T(p),r=!1},d(h){h&&H(e),c&&c.d(),G(n),b.d(),g&&g.d(),p&&p.d()}}}function mt(l,e,t){let{value:n=""}=e,{value_is_output:o=!1}=e,{lines:s=1}=e,{placeholder:u="Type here..."}=e,{label:a}=e,{info:r=void 0}=e,{disabled:c=!1}=e,{show_label:_=!0}=e,{container:m=!0}=e,{max_lines:b}=e,{type:g="text"}=e,{show_copy_button:p=!1}=e,{submit_btn:h=null}=e,{stop_btn:k=null}=e,{rtl:B=!1}=e,{autofocus:V=!1}=e,{text_align:ne=void 0}=e,{autoscroll:J=!0}=e,{max_length:ie=void 0}=e,{root:oe}=e,v,W=!1,X,Z,se=0,x=!1;const be=!h,D=lt();et(()=>{Z=v&&v.offsetHeight+v.scrollTop>v.scrollHeight-100});const de=()=>{Z&&J&&!x&&v.scrollTo(0,v.scrollHeight)};function me(){D("change",n),o||D("input")}tt(()=>{V&&v.focus(),Z&&J&&de(),t(28,o=!1)});async function ge(){"clipboard"in navigator&&(await navigator.clipboard.writeText(n),D("copy",{value:n}),pe())}function pe(){t(19,W=!0),X&&clearTimeout(X),X=setTimeout(()=>{t(19,W=!1)},1e3)}function ke(i){const C=i.target,q=C.value,N=[C.selectionStart,C.selectionEnd];D("select",{value:q.substring(...N),index:N})}async function we(i){await fe(),(i.key==="Enter"&&i.shiftKey&&s>1||i.key==="Enter"&&!i.shiftKey&&s===1&&b>=1)&&(i.preventDefault(),D("submit"))}function ye(i){const C=i.target,q=C.scrollTop;q<se&&(x=!0),se=q;const N=C.scrollHeight-C.clientHeight;q>=N&&(x=!1)}function ve(){D("stop")}function Te(){D("submit")}async function M(i){if(await fe(),s===b)return;const C=i.target,q=window.getComputedStyle(C),N=parseFloat(q.paddingTop),$=parseFloat(q.paddingBottom),ue=parseFloat(q.lineHeight);let ee=b===void 0?!1:N+$+ue*b,re=N+$+s*ue;C.style.height="1px";let O;ee&&C.scrollHeight>ee?O=ee:C.scrollHeight<re?O=re:O=C.scrollHeight,C.style.height=`${O}px`}function Ce(i,C){if(s!==b&&(i.style.overflowY="scroll",i.addEventListener("input",M),!!C.trim()))return M({target:i}),{destroy:()=>i.removeEventListener("input",M)}}function He(i){F.call(this,l,i)}function Se(i){F.call(this,l,i)}function Ee(i){F.call(this,l,i)}function qe(i){F.call(this,l,i)}function ze(i){F.call(this,l,i)}function Be(i){F.call(this,l,i)}function De(i){F.call(this,l,i)}function Fe(i){F.call(this,l,i)}function Ke(){n=this.value,t(0,n)}function Le(i){P[i?"unshift":"push"](()=>{v=i,t(18,v)})}function Ne(){n=this.value,t(0,n)}function Ue(i){P[i?"unshift":"push"](()=>{v=i,t(18,v)})}function Ye(){n=this.value,t(0,n)}function je(i){P[i?"unshift":"push"](()=>{v=i,t(18,v)})}function Ae(){n=this.value,t(0,n)}function Ge(i){P[i?"unshift":"push"](()=>{v=i,t(18,v)})}return l.$$set=i=>{"value"in i&&t(0,n=i.value),"value_is_output"in i&&t(28,o=i.value_is_output),"lines"in i&&t(1,s=i.lines),"placeholder"in i&&t(2,u=i.placeholder),"label"in i&&t(3,a=i.label),"info"in i&&t(4,r=i.info),"disabled"in i&&t(5,c=i.disabled),"show_label"in i&&t(6,_=i.show_label),"container"in i&&t(7,m=i.container),"max_lines"in i&&t(8,b=i.max_lines),"type"in i&&t(9,g=i.type),"show_copy_button"in i&&t(10,p=i.show_copy_button),"submit_btn"in i&&t(11,h=i.submit_btn),"stop_btn"in i&&t(12,k=i.stop_btn),"rtl"in i&&t(13,B=i.rtl),"autofocus"in i&&t(14,V=i.autofocus),"text_align"in i&&t(15,ne=i.text_align),"autoscroll"in i&&t(29,J=i.autoscroll),"max_length"in i&&t(16,ie=i.max_length),"root"in i&&t(17,oe=i.root)},l.$$.update=()=>{l.$$.dirty[0]&1&&n===null&&t(0,n=""),l.$$.dirty[0]&262403&&v&&s!==b&&M({target:v}),l.$$.dirty[0]&1&&me()},[n,s,u,a,r,c,_,m,b,g,p,h,k,B,V,ne,ie,oe,v,W,be,ge,ke,we,ye,ve,Te,Ce,o,J,He,Se,Ee,qe,ze,Be,De,Fe,Ke,Le,Ne,Ue,Ye,je,Ae,Ge]}class St extends Re{constructor(e){super(),Ze(this,e,mt,dt,$e,{value:0,value_is_output:28,lines:1,placeholder:2,label:3,info:4,disabled:5,show_label:6,container:7,max_lines:8,type:9,show_copy_button:10,submit_btn:11,stop_btn:12,rtl:13,autofocus:14,text_align:15,autoscroll:29,max_length:16,root:17},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),w()}get value_is_output(){return this.$$.ctx[28]}set value_is_output(e){this.$$set({value_is_output:e}),w()}get lines(){return this.$$.ctx[1]}set lines(e){this.$$set({lines:e}),w()}get placeholder(){return this.$$.ctx[2]}set placeholder(e){this.$$set({placeholder:e}),w()}get label(){return this.$$.ctx[3]}set label(e){this.$$set({label:e}),w()}get info(){return this.$$.ctx[4]}set info(e){this.$$set({info:e}),w()}get disabled(){return this.$$.ctx[5]}set disabled(e){this.$$set({disabled:e}),w()}get show_label(){return this.$$.ctx[6]}set show_label(e){this.$$set({show_label:e}),w()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),w()}get max_lines(){return this.$$.ctx[8]}set max_lines(e){this.$$set({max_lines:e}),w()}get type(){return this.$$.ctx[9]}set type(e){this.$$set({type:e}),w()}get show_copy_button(){return this.$$.ctx[10]}set show_copy_button(e){this.$$set({show_copy_button:e}),w()}get submit_btn(){return this.$$.ctx[11]}set submit_btn(e){this.$$set({submit_btn:e}),w()}get stop_btn(){return this.$$.ctx[12]}set stop_btn(e){this.$$set({stop_btn:e}),w()}get rtl(){return this.$$.ctx[13]}set rtl(e){this.$$set({rtl:e}),w()}get autofocus(){return this.$$.ctx[14]}set autofocus(e){this.$$set({autofocus:e}),w()}get text_align(){return this.$$.ctx[15]}set text_align(e){this.$$set({text_align:e}),w()}get autoscroll(){return this.$$.ctx[29]}set autoscroll(e){this.$$set({autoscroll:e}),w()}get max_length(){return this.$$.ctx[16]}set max_length(e){this.$$set({max_length:e}),w()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),w()}}export{St as T};
//# sourceMappingURL=Textbox-CiAKq9_B.js.map
