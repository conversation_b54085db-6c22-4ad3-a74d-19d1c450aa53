import{t as O,E as Le}from"./tinycolor-DhRrpXkc.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{B as Pe}from"./BlockTitle-IUerRYnJ.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{B as Ge}from"./Block-CB3nIXHA.js";import{S as Ye}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import{default as Tt}from"./Example-BaLyJYAe.js";import"./Info-QztxykAE.js";import"./MarkdownCode-DYQlapxH.js";import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./IconButton-B3BI2i6T.js";import"./Clear-By3xiIwg.js";function je(t,e){const n=l=>{t&&!t.contains(l.target)&&!l.defaultPrevented&&e(l)};return document.addEventListener("mousedown",n,!0),{destroy(){document.removeEventListener("mousedown",n,!0)}}}function ee(t){const e=t.s,n=t.v;let l=e*n;const u=t.h/60;let r=l*(1-Math.abs(u%2-1));const c=n-l;l=l+c,r=r+c;const b=Math.floor(u)%6,w=[l,r,c,c,r,l][b],f=[r,l,l,r,c,c][b],a=[c,c,r,l,l,r][b];return`rgba(${w*255}, ${f*255}, ${a*255}, ${t.a})`}function Ie(t,e){return e==="hex"?O(t).toHexString():e==="rgb"?O(t).toRgbString():O(t).toHslString()}const{SvelteComponent:Ne,action_destroyer:Ue,append:C,attr:S,binding_callbacks:te,bubble:ne,check_outros:de,create_component:me,destroy_component:ge,destroy_each:Ae,detach:U,element:E,empty:Fe,ensure_array_like:le,flush:G,globals:Je,group_outros:be,init:Ke,insert:A,listen:L,mount_component:we,run_all:ve,safe_not_equal:Oe,set_data:Qe,set_input_value:ie,set_style:R,space:N,text:Ve,toggle_class:se,transition_in:j,transition_out:F}=window.__gradio__svelte__internal,{window:oe}=Je,{createEventDispatcher:We,afterUpdate:Ze,onMount:ye,tick:xe}=window.__gradio__svelte__internal;function ue(t,e,n){const l=t.slice();return l[7]=e[n][0],l[1]=e[n][1],l}function $e(t){let e;return{c(){e=Ve(t[7])},m(n,l){A(n,e,l)},p(n,l){l[0]&128&&Qe(e,n[7])},d(n){n&&U(e)}}}function re(t){let e,n,l,u=`translate(${t[12][0]}px,${t[12][1]}px)`,r,c,b,w=`translateX(${t[14]}px)`,f,a,_,s,m,z,g,B,H,v,q,T,P,Y,d=t[9]&&ae(),X=le(t[21]),k=[];for(let h=0;h<X.length;h+=1)k[h]=_e(ue(t,X,h));return{c(){e=E("div"),n=E("div"),l=E("div"),r=N(),c=E("div"),b=E("div"),f=N(),a=E("div"),_=E("button"),s=N(),m=E("div"),z=E("div"),g=E("input"),B=N(),H=E("button"),d&&d.c(),v=N(),q=E("div");for(let h=0;h<k.length;h+=1)k[h].c();S(l,"class","marker svelte-1oxhzww"),R(l,"transform",u),R(l,"background",t[1]),S(n,"class","color-gradient svelte-1oxhzww"),R(n,"--hue",t[13]),S(b,"class","marker svelte-1oxhzww"),R(b,"background","hsl("+t[13]+", 100%, 50%)"),R(b,"transform",w),S(c,"class","hue-slider svelte-1oxhzww"),S(_,"class","swatch svelte-1oxhzww"),R(_,"background",t[1]),S(g,"type","text"),S(g,"class","svelte-1oxhzww"),S(H,"class","eyedropper svelte-1oxhzww"),S(z,"class","input-wrap svelte-1oxhzww"),S(q,"class","buttons svelte-1oxhzww"),S(a,"class","input svelte-1oxhzww"),S(e,"class","color-picker svelte-1oxhzww")},m(h,i){A(h,e,i),C(e,n),C(n,l),t[28](n),C(e,r),C(e,c),C(c,b),t[29](c),C(e,f),C(e,a),C(a,_),C(a,s),C(a,m),C(m,z),C(z,g),ie(g,t[8]),C(z,B),C(z,H),d&&d.m(H,null),C(m,v),C(m,q);for(let p=0;p<k.length;p+=1)k[p]&&k[p].m(q,null);T=!0,P||(Y=[L(n,"mousedown",t[16]),L(c,"mousedown",t[15]),L(_,"click",t[23]),L(g,"input",t[30]),L(g,"change",t[31]),L(H,"click",t[20]),L(e,"focus",t[25]),L(e,"blur",t[26]),Ue(je.call(null,e,t[22]))],P=!0)},p(h,i){if(i[0]&4096&&u!==(u=`translate(${h[12][0]}px,${h[12][1]}px)`)&&R(l,"transform",u),i[0]&2&&R(l,"background",h[1]),(!T||i[0]&8192)&&R(n,"--hue",h[13]),i[0]&8192&&R(b,"background","hsl("+h[13]+", 100%, 50%)"),i[0]&16384&&w!==(w=`translateX(${h[14]}px)`)&&R(b,"transform",w),i[0]&2&&R(_,"background",h[1]),i[0]&256&&g.value!==h[8]&&ie(g,h[8]),h[9]?d?i[0]&512&&j(d,1):(d=ae(),d.c(),j(d,1),d.m(H,null)):d&&(be(),F(d,1,1,()=>{d=null}),de()),i[0]&2097153){X=le(h[21]);let p;for(p=0;p<X.length;p+=1){const J=ue(h,X,p);k[p]?k[p].p(J,i):(k[p]=_e(J),k[p].c(),k[p].m(q,null))}for(;p<k.length;p+=1)k[p].d(1);k.length=X.length}},i(h){T||(j(d),T=!0)},o(h){F(d),T=!1},d(h){h&&U(e),t[28](null),t[29](null),d&&d.d(),Ae(k,h),P=!1,ve(Y)}}}function ae(t){let e,n;return e=new Le({}),{c(){me(e.$$.fragment)},m(l,u){we(e,l,u),n=!0},i(l){n||(j(e.$$.fragment,l),n=!0)},o(l){F(e.$$.fragment,l),n=!1},d(l){ge(e,l)}}}function _e(t){let e,n,l;function u(){return t[32](t[1])}return{c(){e=E("button"),e.textContent=`${t[7]}`,S(e,"class","button svelte-1oxhzww"),se(e,"active",t[0]===t[1])},m(r,c){A(r,e,c),n||(l=L(e,"click",u),n=!0)},p(r,c){t=r,c[0]&2097153&&se(e,"active",t[0]===t[1])},d(r){r&&U(e),n=!1,l()}}}function et(t){let e,n,l,u,r,c,b,w;e=new Pe({props:{root:t[6],show_label:t[5],info:t[3],$$slots:{default:[$e]},$$scope:{ctx:t}}});let f=t[2]&&re(t);return{c(){me(e.$$.fragment),n=N(),l=E("button"),u=N(),f&&f.c(),r=Fe(),S(l,"class","dialog-button svelte-1oxhzww"),l.disabled=t[4],R(l,"background",t[1])},m(a,_){we(e,a,_),A(a,n,_),A(a,l,_),A(a,u,_),f&&f.m(a,_),A(a,r,_),c=!0,b||(w=[L(oe,"mousemove",t[17]),L(oe,"mouseup",t[18]),L(l,"click",t[27])],b=!0)},p(a,_){const s={};_[0]&64&&(s.root=a[6]),_[0]&32&&(s.show_label=a[5]),_[0]&8&&(s.info=a[3]),_[0]&128|_[1]&8192&&(s.$$scope={dirty:_,ctx:a}),e.$set(s),(!c||_[0]&16)&&(l.disabled=a[4]),_[0]&2&&R(l,"background",a[1]),a[2]?f?(f.p(a,_),_[0]&4&&j(f,1)):(f=re(a),f.c(),j(f,1),f.m(r.parentNode,r)):f&&(be(),F(f,1,1,()=>{f=null}),de())},i(a){c||(j(e.$$.fragment,a),j(f),c=!0)},o(a){F(e.$$.fragment,a),F(f),c=!1},d(a){a&&(U(n),U(l),U(u),U(r)),ge(e,a),f&&f.d(a),b=!1,ve(w)}}}function tt(t,e,n){let l,{value:u="#000000"}=e,{value_is_output:r=!1}=e,{label:c}=e,{info:b=void 0}=e,{disabled:w=!1}=e,{show_label:f=!0}=e,{root:a}=e,{current_mode:_="hex"}=e,{dialog_open:s=!1}=e,m=!1,z,g;const B=We();let H=[0,0],v=null,q=!1,T=[0,0],P=0,Y=0,d=null,X=!1;function k(o){d=o.currentTarget.getBoundingClientRect(),X=!0,h(o.clientX)}function h(o){if(!d)return;const D=Math.max(0,Math.min(o-d.left,d.width));n(14,Y=D);const I=D/d.width*360;n(13,P=I),n(1,u=ee({h:I,s:T[0],v:T[1],a:1}))}function i(o,D){if(!v)return;const I=Math.max(0,Math.min(o-v.left,v.width)),K=Math.max(0,Math.min(D-v.top,v.height));n(12,H=[I,K]);const V={h:P*1,s:I/v.width,v:1-K/v.height,a:1};T=[V.s,V.v],n(1,u=ee(V))}function p(o){q=!0,v=o.currentTarget.getBoundingClientRect(),i(o.clientX,o.clientY)}function J(o){q&&i(o.clientX,o.clientY),X&&h(o.clientX)}function ke(){q=!1,X=!1}async function Q(o){if(q||X||(await xe(),!o)||(!v&&z&&(v=z.getBoundingClientRect()),!d&&g&&(d=g.getBoundingClientRect()),!v||!d))return;const D=O(o).toHsv(),I=D.s*v.width,K=(1-D.v)*v.height;n(12,H=[I,K]),T=[D.s,D.v],n(13,P=D.h),n(14,Y=D.h/360*d.width)}function pe(){new EyeDropper().open().then(D=>{n(1,u=D.sRGBHex)})}const ze=[["Hex","hex"],["RGB","rgb"],["HSL","hsl"]];ye(async()=>{n(9,m=window!==void 0&&!!window.EyeDropper)});function Be(){n(2,s=!1)}function Ce(){B("change",u),r||B("input")}Ze(()=>{n(24,r=!1)});function Me(){B("selected",l),B("close")}function Se(o){ne.call(this,t,o)}function Ee(o){ne.call(this,t,o)}const Re=()=>{Q(u),n(2,s=!s)};function He(o){te[o?"unshift":"push"](()=>{z=o,n(10,z)})}function Xe(o){te[o?"unshift":"push"](()=>{g=o,n(11,g)})}function De(){l=this.value,n(8,l),n(1,u),n(0,_)}const qe=o=>n(1,u=o.currentTarget.value),Te=o=>n(0,_=o);return t.$$set=o=>{"value"in o&&n(1,u=o.value),"value_is_output"in o&&n(24,r=o.value_is_output),"label"in o&&n(7,c=o.label),"info"in o&&n(3,b=o.info),"disabled"in o&&n(4,w=o.disabled),"show_label"in o&&n(5,f=o.show_label),"root"in o&&n(6,a=o.root),"current_mode"in o&&n(0,_=o.current_mode),"dialog_open"in o&&n(2,s=o.dialog_open)},t.$$.update=()=>{t.$$.dirty[0]&3&&n(8,l=Ie(u,_)),t.$$.dirty[0]&256&&l&&B("selected",l),t.$$.dirty[0]&2&&Q(u),t.$$.dirty[0]&2&&Ce()},[_,u,s,b,w,f,a,c,l,m,z,g,H,P,Y,k,p,J,ke,Q,pe,ze,Be,Me,r,Se,Ee,Re,He,Xe,De,qe,Te]}class nt extends Ne{constructor(e){super(),Ke(this,e,tt,et,Oe,{value:1,value_is_output:24,label:7,info:3,disabled:4,show_label:5,root:6,current_mode:0,dialog_open:2},null,[-1,-1])}get value(){return this.$$.ctx[1]}set value(e){this.$$set({value:e}),G()}get value_is_output(){return this.$$.ctx[24]}set value_is_output(e){this.$$set({value_is_output:e}),G()}get label(){return this.$$.ctx[7]}set label(e){this.$$set({label:e}),G()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),G()}get disabled(){return this.$$.ctx[4]}set disabled(e){this.$$set({disabled:e}),G()}get show_label(){return this.$$.ctx[5]}set show_label(e){this.$$set({show_label:e}),G()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),G()}get current_mode(){return this.$$.ctx[0]}set current_mode(e){this.$$set({current_mode:e}),G()}get dialog_open(){return this.$$.ctx[2]}set dialog_open(e){this.$$set({dialog_open:e}),G()}}const lt=nt,{SvelteComponent:it,add_flush_callback:ce,assign:st,bind:fe,binding_callbacks:he,create_component:W,destroy_component:Z,detach:ot,flush:M,get_spread_object:ut,get_spread_update:rt,init:at,insert:_t,mount_component:y,safe_not_equal:ct,space:ft,transition_in:x,transition_out:$}=window.__gradio__svelte__internal;function ht(t){let e,n,l,u,r,c;const b=[{autoscroll:t[13].autoscroll},{i18n:t[13].i18n},t[11]];let w={};for(let s=0;s<b.length;s+=1)w=st(w,b[s]);e=new Ye({props:w}),e.$on("clear_status",t[16]);function f(s){t[17](s)}function a(s){t[18](s)}let _={root:t[12],label:t[2],info:t[3],show_label:t[7],disabled:!t[14]||t[15]};return t[0]!==void 0&&(_.value=t[0]),t[1]!==void 0&&(_.value_is_output=t[1]),l=new lt({props:_}),he.push(()=>fe(l,"value",f)),he.push(()=>fe(l,"value_is_output",a)),l.$on("change",t[19]),l.$on("input",t[20]),l.$on("submit",t[21]),l.$on("blur",t[22]),l.$on("focus",t[23]),{c(){W(e.$$.fragment),n=ft(),W(l.$$.fragment)},m(s,m){y(e,s,m),_t(s,n,m),y(l,s,m),c=!0},p(s,m){const z=m&10240?rt(b,[m&8192&&{autoscroll:s[13].autoscroll},m&8192&&{i18n:s[13].i18n},m&2048&&ut(s[11])]):{};e.$set(z);const g={};m&4096&&(g.root=s[12]),m&4&&(g.label=s[2]),m&8&&(g.info=s[3]),m&128&&(g.show_label=s[7]),m&49152&&(g.disabled=!s[14]||s[15]),!u&&m&1&&(u=!0,g.value=s[0],ce(()=>u=!1)),!r&&m&2&&(r=!0,g.value_is_output=s[1],ce(()=>r=!1)),l.$set(g)},i(s){c||(x(e.$$.fragment,s),x(l.$$.fragment,s),c=!0)},o(s){$(e.$$.fragment,s),$(l.$$.fragment,s),c=!1},d(s){s&&ot(n),Z(e,s),Z(l,s)}}}function dt(t){let e,n;return e=new Ge({props:{visible:t[6],elem_id:t[4],elem_classes:t[5],container:t[8],scale:t[9],min_width:t[10],$$slots:{default:[ht]},$$scope:{ctx:t}}}),{c(){W(e.$$.fragment)},m(l,u){y(e,l,u),n=!0},p(l,[u]){const r={};u&64&&(r.visible=l[6]),u&16&&(r.elem_id=l[4]),u&32&&(r.elem_classes=l[5]),u&256&&(r.container=l[8]),u&512&&(r.scale=l[9]),u&1024&&(r.min_width=l[10]),u&16840847&&(r.$$scope={dirty:u,ctx:l}),e.$set(r)},i(l){n||(x(e.$$.fragment,l),n=!0)},o(l){$(e.$$.fragment,l),n=!1},d(l){Z(e,l)}}}function mt(t,e,n){let{label:l="ColorPicker"}=e,{info:u=void 0}=e,{elem_id:r=""}=e,{elem_classes:c=[]}=e,{visible:b=!0}=e,{value:w}=e,{value_is_output:f=!1}=e,{show_label:a}=e,{container:_=!0}=e,{scale:s=null}=e,{min_width:m=void 0}=e,{loading_status:z}=e,{root:g}=e,{gradio:B}=e,{interactive:H}=e,{disabled:v=!1}=e;const q=()=>B.dispatch("clear_status",z);function T(i){w=i,n(0,w)}function P(i){f=i,n(1,f)}const Y=()=>B.dispatch("change"),d=()=>B.dispatch("input"),X=()=>B.dispatch("submit"),k=()=>B.dispatch("blur"),h=()=>B.dispatch("focus");return t.$$set=i=>{"label"in i&&n(2,l=i.label),"info"in i&&n(3,u=i.info),"elem_id"in i&&n(4,r=i.elem_id),"elem_classes"in i&&n(5,c=i.elem_classes),"visible"in i&&n(6,b=i.visible),"value"in i&&n(0,w=i.value),"value_is_output"in i&&n(1,f=i.value_is_output),"show_label"in i&&n(7,a=i.show_label),"container"in i&&n(8,_=i.container),"scale"in i&&n(9,s=i.scale),"min_width"in i&&n(10,m=i.min_width),"loading_status"in i&&n(11,z=i.loading_status),"root"in i&&n(12,g=i.root),"gradio"in i&&n(13,B=i.gradio),"interactive"in i&&n(14,H=i.interactive),"disabled"in i&&n(15,v=i.disabled)},[w,f,l,u,r,c,b,a,_,s,m,z,g,B,H,v,q,T,P,Y,d,X,k,h]}class Xt extends it{constructor(e){super(),at(this,e,mt,dt,ct,{label:2,info:3,elem_id:4,elem_classes:5,visible:6,value:0,value_is_output:1,show_label:7,container:8,scale:9,min_width:10,loading_status:11,root:12,gradio:13,interactive:14,disabled:15})}get label(){return this.$$.ctx[2]}set label(e){this.$$set({label:e}),M()}get info(){return this.$$.ctx[3]}set info(e){this.$$set({info:e}),M()}get elem_id(){return this.$$.ctx[4]}set elem_id(e){this.$$set({elem_id:e}),M()}get elem_classes(){return this.$$.ctx[5]}set elem_classes(e){this.$$set({elem_classes:e}),M()}get visible(){return this.$$.ctx[6]}set visible(e){this.$$set({visible:e}),M()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),M()}get value_is_output(){return this.$$.ctx[1]}set value_is_output(e){this.$$set({value_is_output:e}),M()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),M()}get container(){return this.$$.ctx[8]}set container(e){this.$$set({container:e}),M()}get scale(){return this.$$.ctx[9]}set scale(e){this.$$set({scale:e}),M()}get min_width(){return this.$$.ctx[10]}set min_width(e){this.$$set({min_width:e}),M()}get loading_status(){return this.$$.ctx[11]}set loading_status(e){this.$$set({loading_status:e}),M()}get root(){return this.$$.ctx[12]}set root(e){this.$$set({root:e}),M()}get gradio(){return this.$$.ctx[13]}set gradio(e){this.$$set({gradio:e}),M()}get interactive(){return this.$$.ctx[14]}set interactive(e){this.$$set({interactive:e}),M()}get disabled(){return this.$$.ctx[15]}set disabled(e){this.$$set({disabled:e}),M()}}export{lt as BaseColorPicker,Tt as BaseExample,Xt as default};
//# sourceMappingURL=Index-CG6cxww4.js.map
