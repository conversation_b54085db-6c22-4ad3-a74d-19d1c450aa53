{"version": 3, "file": "Index-CB5fNOZY.js", "sources": ["../../../../js/icons/src/Chat.svelte", "../../../../js/icons/src/Retry.svelte", "../../../../js/icons/src/ScrollDownArrow.svelte", "../../../../js/chatbot/shared/utils.ts", "../../../../js/chatbot/shared/Component.svelte", "../../../../js/chatbot/shared/MessageBox.svelte", "../../../../js/chatbot/shared/ThumbDownActive.svelte", "../../../../js/chatbot/shared/ThumbDownDefault.svelte", "../../../../js/chatbot/shared/ThumbUpActive.svelte", "../../../../js/chatbot/shared/ThumbUpDefault.svelte", "../../../../js/chatbot/shared/LikeDislike.svelte", "../../../../js/chatbot/shared/Copy.svelte", "../../../../js/chatbot/shared/ButtonPanel.svelte", "../../../../js/chatbot/shared/Message.svelte", "../../../../js/chatbot/shared/Pending.svelte", "../../../../js/chatbot/shared/Examples.svelte", "../../../../js/chatbot/shared/CopyAll.svelte", "../../../../js/chatbot/shared/ChatBot.svelte", "../../../../js/chatbot/Index.svelte"], "sourcesContent": ["<svg\n\txmlns=\"http://www.w3.org/2000/svg\"\n\txmlns:xlink=\"http://www.w3.org/1999/xlink\"\n\taria-hidden=\"true\"\n\trole=\"img\"\n\tclass=\"iconify iconify--carbon\"\n\twidth=\"100%\"\n\theight=\"100%\"\n\tpreserveAspectRatio=\"xMidYMid meet\"\n\tviewBox=\"0 0 32 32\"\n>\n\t<path\n\t\tfill=\"currentColor\"\n\t\td=\"M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z\"\n\t/>\n\t<path fill=\"currentColor\" d=\"M8 10h16v2H8zm0 6h10v2H8z\" />\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tstroke-width=\"1.5\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n\tcolor=\"currentColor\"\n>\n\t<path\n\t\td=\"M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n\t<path\n\t\td=\"M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"1.5\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t></path>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 24 24\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M12 20L12 4M12 20L7 15M12 20L17 15\"\n\t\tstroke=\"currentColor\"\n\t\tstroke-width=\"2\"\n\t\tstroke-linecap=\"round\"\n\t\tstroke-linejoin=\"round\"\n\t/>\n</svg>\n", "import type { FileData } from \"@gradio/client\";\nimport type { ComponentType, SvelteComponent } from \"svelte\";\nimport { uploadToHuggingFace } from \"@gradio/utils\";\nimport type {\n\tTupleFormat,\n\tComponentMessage,\n\tComponentData,\n\tTextMessage,\n\tNormalisedMessage,\n\tMessage,\n\tMessageRole\n} from \"../types\";\nimport type { LoadedComponent } from \"../../core/src/types\";\nimport { Gradio } from \"@gradio/utils\";\nexport const format_chat_for_sharing = async (\n\tchat: NormalisedMessage[]\n): Promise<string> => {\n\tlet messages = await Promise.all(\n\t\tchat.map(async (message) => {\n\t\t\tif (message.role === \"system\") return \"\";\n\t\t\tlet speaker_emoji = message.role === \"user\" ? \"😃\" : \"🤖\";\n\t\t\tlet html_content = \"\";\n\n\t\t\tif (message.type === \"text\") {\n\t\t\t\tconst regexPatterns = {\n\t\t\t\t\taudio: /<audio.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\tvideo: /<video.*?src=\"(\\/file=.*?)\"/g,\n\t\t\t\t\timage: /<img.*?src=\"(\\/file=.*?)\".*?\\/>|!\\[.*?\\]\\((\\/file=.*?)\\)/g\n\t\t\t\t};\n\n\t\t\t\thtml_content = message.content;\n\n\t\t\t\tfor (let [_, regex] of Object.entries(regexPatterns)) {\n\t\t\t\t\tlet match;\n\n\t\t\t\t\twhile ((match = regex.exec(message.content)) !== null) {\n\t\t\t\t\t\tconst fileUrl = match[1] || match[2];\n\t\t\t\t\t\tconst newUrl = await uploadToHuggingFace(fileUrl, \"url\");\n\t\t\t\t\t\thtml_content = html_content.replace(fileUrl, newUrl);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (!message.content.value) return \"\";\n\t\t\t\tconst url =\n\t\t\t\t\tmessage.content.component === \"video\"\n\t\t\t\t\t\t? message.content.value?.video.path\n\t\t\t\t\t\t: message.content.value;\n\t\t\t\tconst file_url = await uploadToHuggingFace(url, \"url\");\n\t\t\t\tif (message.content.component === \"audio\") {\n\t\t\t\t\thtml_content = `<audio controls src=\"${file_url}\"></audio>`;\n\t\t\t\t} else if (message.content.component === \"video\") {\n\t\t\t\t\thtml_content = file_url;\n\t\t\t\t} else if (message.content.component === \"image\") {\n\t\t\t\t\thtml_content = `<img src=\"${file_url}\" />`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn `${speaker_emoji}: ${html_content}`;\n\t\t})\n\t);\n\treturn messages.join(\"\\n\");\n};\n\nexport interface UndoRetryData {\n\tindex: number | [number, number];\n\tvalue: string | FileData | ComponentData;\n}\n\nconst redirect_src_url = (src: string, root: string): string =>\n\tsrc.replace('src=\"/file', `src=\"${root}file`);\n\nfunction get_component_for_mime_type(\n\tmime_type: string | null | undefined\n): string {\n\tif (!mime_type) return \"file\";\n\tif (mime_type.includes(\"audio\")) return \"audio\";\n\tif (mime_type.includes(\"video\")) return \"video\";\n\tif (mime_type.includes(\"image\")) return \"image\";\n\treturn \"file\";\n}\n\nfunction convert_file_message_to_component_message(\n\tmessage: any\n): ComponentData {\n\tconst _file = Array.isArray(message.file) ? message.file[0] : message.file;\n\treturn {\n\t\tcomponent: get_component_for_mime_type(_file?.mime_type),\n\t\tvalue: message.file,\n\t\talt_text: message.alt_text,\n\t\tconstructor_args: {},\n\t\tprops: {}\n\t} as ComponentData;\n}\n\nexport function normalise_messages(\n\tmessages: Message[] | null,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\treturn messages.map((message, i) => {\n\t\tif (typeof message.content === \"string\") {\n\t\t\treturn {\n\t\t\t\trole: message.role,\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\tcontent: redirect_src_url(message.content, root),\n\t\t\t\ttype: \"text\",\n\t\t\t\tindex: i,\n\t\t\t\toptions: message.options\n\t\t\t};\n\t\t} else if (\"file\" in message.content) {\n\t\t\treturn {\n\t\t\t\tcontent: convert_file_message_to_component_message(message.content),\n\t\t\t\tmetadata: message.metadata,\n\t\t\t\trole: message.role,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: i,\n\t\t\t\toptions: message.options\n\t\t\t};\n\t\t}\n\t\treturn { type: \"component\", ...message } as ComponentMessage;\n\t});\n}\n\nexport function normalise_tuples(\n\tmessages: TupleFormat,\n\troot: string\n): NormalisedMessage[] | null {\n\tif (messages === null) return messages;\n\tconst msg = messages.flatMap((message_pair, i) => {\n\t\treturn message_pair.map((message, index) => {\n\t\t\tif (message == null) return null;\n\t\t\tconst role = index == 0 ? \"user\" : \"assistant\";\n\n\t\t\tif (typeof message === \"string\") {\n\t\t\t\treturn {\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"text\",\n\t\t\t\t\tcontent: redirect_src_url(message, root),\n\t\t\t\t\tmetadata: { title: null },\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as TextMessage;\n\t\t\t}\n\n\t\t\tif (\"file\" in message) {\n\t\t\t\treturn {\n\t\t\t\t\tcontent: convert_file_message_to_component_message(message),\n\t\t\t\t\trole: role,\n\t\t\t\t\ttype: \"component\",\n\t\t\t\t\tindex: [i, index]\n\t\t\t\t} as ComponentMessage;\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\trole: role,\n\t\t\t\tcontent: message,\n\t\t\t\ttype: \"component\",\n\t\t\t\tindex: [i, index]\n\t\t\t} as ComponentMessage;\n\t\t});\n\t});\n\treturn msg.filter((message) => message != null) as NormalisedMessage[];\n}\n\nexport function is_component_message(\n\tmessage: NormalisedMessage\n): message is ComponentMessage {\n\treturn message.type === \"component\";\n}\n\nexport function is_last_bot_message(\n\tmessages: NormalisedMessage[],\n\tall_messages: NormalisedMessage[]\n): boolean {\n\tconst is_bot = messages[messages.length - 1].role === \"assistant\";\n\tconst last_index = messages[messages.length - 1].index;\n\t// use JSON.stringify to handle both the number and tuple cases\n\t// when msg_format is tuples, last_index is an array and when it is messages, it is a number\n\tconst is_last =\n\t\tJSON.stringify(last_index) ===\n\t\tJSON.stringify(all_messages[all_messages.length - 1].index);\n\treturn is_last && is_bot;\n}\n\nexport function group_messages(\n\tmessages: NormalisedMessage[],\n\tmsg_format: \"messages\" | \"tuples\"\n): NormalisedMessage[][] {\n\tconst groupedMessages: NormalisedMessage[][] = [];\n\tlet currentGroup: NormalisedMessage[] = [];\n\tlet currentRole: MessageRole | null = null;\n\n\tfor (const message of messages) {\n\t\tif (!(message.role === \"assistant\" || message.role === \"user\")) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (message.role === currentRole) {\n\t\t\tcurrentGroup.push(message);\n\t\t} else {\n\t\t\tif (currentGroup.length > 0) {\n\t\t\t\tgroupedMessages.push(currentGroup);\n\t\t\t}\n\t\t\tcurrentGroup = [message];\n\t\t\tcurrentRole = message.role;\n\t\t}\n\t}\n\n\tif (currentGroup.length > 0) {\n\t\tgroupedMessages.push(currentGroup);\n\t}\n\n\treturn groupedMessages;\n}\n\nexport async function load_components(\n\tcomponent_names: string[],\n\t_components: Record<string, ComponentType<SvelteComponent>>,\n\tload_component: Gradio[\"load_component\"]\n): Promise<Record<string, ComponentType<SvelteComponent>>> {\n\tlet names: string[] = [];\n\tlet components: ReturnType<typeof load_component>[\"component\"][] = [];\n\n\tcomponent_names.forEach((component_name) => {\n\t\tif (_components[component_name] || component_name === \"file\") {\n\t\t\treturn;\n\t\t}\n\n\t\tconst { name, component } = load_component(component_name, \"base\");\n\t\tnames.push(name);\n\t\tcomponents.push(component);\n\t\tcomponent_name;\n\t});\n\n\tconst loaded_components: LoadedComponent[] = await Promise.all(components);\n\tloaded_components.forEach((component, i) => {\n\t\t_components[names[i]] = component.default;\n\t});\n\n\treturn _components;\n}\n\nexport function get_components_from_messages(\n\tmessages: NormalisedMessage[] | null\n): string[] {\n\tif (!messages) return [];\n\tlet components: Set<string> = new Set();\n\tmessages.forEach((message) => {\n\t\tif (message.type === \"component\") {\n\t\t\tcomponents.add(message.content.component);\n\t\t}\n\t});\n\treturn Array.from(components);\n}\n", "<script lang=\"ts\">\n\texport let type: \"gallery\" | \"plot\" | \"audio\" | \"video\" | \"image\" | string;\n\texport let components;\n\texport let value;\n\texport let target;\n\texport let theme_mode;\n\texport let props;\n\texport let i18n;\n\texport let upload;\n\texport let _fetch;\n\texport let allow_file_downloads: boolean;\n\texport let display_icon_button_wrapper_top_corner = false;\n</script>\n\n{#if type === \"gallery\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_label={false}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\t{_fetch}\n\t\tallow_preview={false}\n\t\tinteractive={false}\n\t\tmode=\"minimal\"\n\t\tfixed_height={1}\n\t\ton:load\n\t/>\n{:else if type === \"plot\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\t{target}\n\t\t{theme_mode}\n\t\tbokeh_version={props.bokeh_version}\n\t\tcaption=\"\"\n\t\tshow_actions_button={true}\n\t\ton:load\n\t/>\n{:else if type === \"audio\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tlabel=\"\"\n\t\twaveform_settings={{ autoplay: props.autoplay }}\n\t\twaveform_options={{}}\n\t\tshow_download_button={allow_file_downloads}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\ton:load\n\t/>\n{:else if type === \"video\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\tautoplay={props.autoplay}\n\t\tvalue={value.video || value}\n\t\tshow_label={false}\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\t{upload}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\tshow_download_button={allow_file_downloads}\n\t\ton:load\n\t>\n\t\t<track kind=\"captions\" />\n\t</svelte:component>\n{:else if type === \"image\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_download_button={allow_file_downloads}\n\t\t{display_icon_button_wrapper_top_corner}\n\t\ton:load\n\t\t{i18n}\n\t/>\n{:else if type === \"html\"}\n\t<svelte:component\n\t\tthis={components[type]}\n\t\t{value}\n\t\tshow_label={false}\n\t\tlabel=\"chatbot-image\"\n\t\tshow_share_button={true}\n\t\t{i18n}\n\t\tgradio={{ dispatch: () => {} }}\n\t\ton:load\n\t/>\n{/if}\n", "<script lang=\"ts\">\n\texport let expanded = false;\n\texport let title: string;\n\n\tfunction toggleExpanded(): void {\n\t\texpanded = !expanded;\n\t}\n</script>\n\n<button class=\"box\" on:click={toggleExpanded}>\n\t<div class=\"title\">\n\t\t<span class=\"title-text\">{title}</span>\n\t\t<span\n\t\t\tstyle:transform={expanded ? \"rotate(0)\" : \"rotate(90deg)\"}\n\t\t\tclass=\"arrow\"\n\t\t>\n\t\t\t▼\n\t\t</span>\n\t</div>\n\t{#if expanded}\n\t\t<div class=\"content\">\n\t\t\t<slot></slot>\n\t\t</div>\n\t{/if}\n</button>\n\n<style>\n\t.box {\n\t\tborder-radius: 4px;\n\t\tcursor: pointer;\n\t\tmax-width: max-content;\n\t\tbackground: var(--color-accent-soft);\n\t\tborder: 1px solid var(--border-color-accent-subdued);\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 3px 6px;\n\t\tcolor: var(--body-text-color);\n\t\topacity: 0.8;\n\t}\n\n\t.content {\n\t\tpadding: 4px 8px;\n\t}\n\n\t.content :global(*) {\n\t\tfont-size: 0.8em;\n\t}\n\n\t.title-text {\n\t\tpadding-right: var(--spacing-lg);\n\t}\n\n\t.arrow {\n\t\tmargin-left: auto;\n\t\topacity: 0.8;\n\t}\n</style>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<svg\n\twidth=\"100%\"\n\theight=\"100%\"\n\tviewBox=\"0 0 12 12\"\n\tfill=\"none\"\n\txmlns=\"http://www.w3.org/2000/svg\"\n>\n\t<path\n\t\td=\"M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z\"\n\t\tfill=\"currentColor\"\n\t/>\n</svg>\n", "<script lang=\"ts\">\n\timport { IconButton } from \"@gradio/atoms\";\n\timport ThumbDownActive from \"./ThumbDownActive.svelte\";\n\timport ThumbDownDefault from \"./ThumbDownDefault.svelte\";\n\timport ThumbUpActive from \"./ThumbUpActive.svelte\";\n\timport ThumbUpDefault from \"./ThumbUpDefault.svelte\";\n\n\texport let handle_action: (selected: string | null) => void;\n\n\tlet selected: \"like\" | \"dislike\" | null = null;\n</script>\n\n<IconButton\n\tIcon={selected === \"dislike\" ? ThumbDownActive : ThumbDownDefault}\n\tlabel={selected === \"dislike\" ? \"clicked dislike\" : \"dislike\"}\n\tcolor={selected === \"dislike\"\n\t\t? \"var(--color-accent)\"\n\t\t: \"var(--block-label-text-color)\"}\n\ton:click={() => {\n\t\tselected = \"dislike\";\n\t\thandle_action(selected);\n\t}}\n/>\n\n<IconButton\n\tIcon={selected === \"like\" ? ThumbUpActive : ThumbUpDefault}\n\tlabel={selected === \"like\" ? \"clicked like\" : \"like\"}\n\tcolor={selected === \"like\"\n\t\t? \"var(--color-accent)\"\n\t\t: \"var(--block-label-text-color)\"}\n\ton:click={() => {\n\t\tselected = \"like\";\n\t\thandle_action(selected);\n\t}}\n/>\n", "<script lang=\"ts\">\n\timport { createEventDispatcher } from \"svelte\";\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport { IconButton } from \"@gradio/atoms\";\n\timport type { CopyData } from \"@gradio/utils\";\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tcopy: CopyData;\n\t}>();\n\n\tlet copied = false;\n\texport let value: string;\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 2000);\n\t}\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tdispatch(\"copy\", { value: value });\n\t\t\tawait navigator.clipboard.writeText(value);\n\t\t\tcopy_feedback();\n\t\t} else {\n\t\t\tconst textArea = document.createElement(\"textarea\");\n\t\t\ttextArea.value = value;\n\n\t\t\ttextArea.style.position = \"absolute\";\n\t\t\ttextArea.style.left = \"-999999px\";\n\n\t\t\tdocument.body.prepend(textArea);\n\t\t\ttextArea.select();\n\n\t\t\ttry {\n\t\t\t\tdocument.execCommand(\"copy\");\n\t\t\t\tcopy_feedback();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error(error);\n\t\t\t} finally {\n\t\t\t\ttextArea.remove();\n\t\t\t}\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied message\" : \"Copy message\"}\n\tIcon={copied ? Check : Copy}\n/>\n", "<script lang=\"ts\">\n\timport LikeDislike from \"./LikeDislike.svelte\";\n\timport Copy from \"./Copy.svelte\";\n\timport type { FileData } from \"@gradio/client\";\n\timport DownloadIcon from \"./Download.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\timport type { NormalisedMessage, TextMessage } from \"../types\";\n\timport { is_component_message } from \"./utils\";\n\timport { Retry, Undo } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\texport let likeable: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let show_copy_button: boolean;\n\texport let message: NormalisedMessage | NormalisedMessage[];\n\texport let position: \"right\" | \"left\";\n\texport let avatar: FileData | null;\n\texport let generating: boolean;\n\n\texport let handle_action: (selected: string | null) => void;\n\texport let layout: \"bubble\" | \"panel\";\n\texport let dispatch: any;\n\n\tfunction is_all_text(\n\t\tmessage: NormalisedMessage[] | NormalisedMessage\n\t): message is TextMessage[] | TextMessage {\n\t\treturn (\n\t\t\t(Array.isArray(message) &&\n\t\t\t\tmessage.every((m) => typeof m.content === \"string\")) ||\n\t\t\t(!Array.isArray(message) && typeof message.content === \"string\")\n\t\t);\n\t}\n\n\tfunction all_text(message: TextMessage[] | TextMessage): string {\n\t\tif (Array.isArray(message)) {\n\t\t\treturn message.map((m) => m.content).join(\"\\n\");\n\t\t}\n\t\treturn message.content;\n\t}\n\n\t$: message_text = is_all_text(message) ? all_text(message) : \"\";\n\n\t$: show_copy = show_copy_button && message && is_all_text(message);\n\t$: show_download =\n\t\t!Array.isArray(message) &&\n\t\tis_component_message(message) &&\n\t\tmessage.content.value?.url;\n</script>\n\n{#if show_copy || show_retry || show_undo || likeable}\n\t<div\n\t\tclass=\"message-buttons-{position} {layout} message-buttons {avatar !==\n\t\t\tnull && 'with-avatar'}\"\n\t>\n\t\t<IconButtonWrapper top_panel={false}>\n\t\t\t{#if show_copy}\n\t\t\t\t<Copy\n\t\t\t\t\tvalue={message_text}\n\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t\t{#if show_retry}\n\t\t\t\t<IconButton\n\t\t\t\t\tIcon={Retry}\n\t\t\t\t\tlabel=\"Retry\"\n\t\t\t\t\ton:click={() => handle_action(\"retry\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t\t{#if show_undo}\n\t\t\t\t<IconButton\n\t\t\t\t\tlabel=\"Undo\"\n\t\t\t\t\tIcon={Undo}\n\t\t\t\t\ton:click={() => handle_action(\"undo\")}\n\t\t\t\t\tdisabled={generating}\n\t\t\t\t/>\n\t\t\t{/if}\n\t\t\t{#if likeable}\n\t\t\t\t<LikeDislike {handle_action} />\n\t\t\t{/if}\n\t\t</IconButtonWrapper>\n\t</div>\n{/if}\n\n<style>\n\t.bubble :global(.icon-button-wrapper) {\n\t\tmargin: 0px calc(var(--spacing-xl) * 2);\n\t}\n\n\t.message-buttons {\n\t\tz-index: var(--layer-1);\n\t}\n\t.message-buttons-left {\n\t\talign-self: flex-start;\n\t}\n\n\t.bubble.message-buttons-right {\n\t\talign-self: flex-end;\n\t}\n\n\t.message-buttons-right :global(.icon-button-wrapper) {\n\t\tmargin-left: auto;\n\t}\n\n\t.bubble.with-avatar {\n\t\tmargin-left: calc(var(--spacing-xl) * 5);\n\t\tmargin-right: calc(var(--spacing-xl) * 5);\n\t}\n\n\t.panel {\n\t\tdisplay: flex;\n\t\talign-self: flex-start;\n\t\tpadding: 0 var(--spacing-xl);\n\t\tz-index: var(--layer-1);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { is_component_message, is_last_bot_message } from \"../shared/utils\";\n\timport { File } from \"@gradio/icons\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport Component from \"./Component.svelte\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport MessageBox from \"./MessageBox.svelte\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport type { ComponentType, SvelteComponent } from \"svelte\";\n\timport ButtonPanel from \"./ButtonPanel.svelte\";\n\n\texport let value: NormalisedMessage[];\n\texport let avatar_img: FileData | null;\n\texport let opposite_avatar_img: FileData | null = null;\n\texport let role = \"user\";\n\texport let messages: NormalisedMessage[] = [];\n\texport let layout: \"bubble\" | \"panel\";\n\texport let render_markdown: boolean;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let sanitize_html: boolean;\n\texport let selectable: boolean;\n\texport let _fetch: typeof fetch;\n\texport let rtl: boolean;\n\texport let dispatch: any;\n\texport let i18n: I18nFormatter;\n\texport let line_breaks: boolean;\n\texport let upload: Client[\"upload\"];\n\texport let target: HTMLElement | null;\n\texport let root: string;\n\texport let theme_mode: \"light\" | \"dark\" | \"system\";\n\texport let _components: Record<string, ComponentType<SvelteComponent>>;\n\texport let i: number;\n\texport let show_copy_button: boolean;\n\texport let generating: boolean;\n\texport let show_like: boolean;\n\texport let show_retry: boolean;\n\texport let show_undo: boolean;\n\texport let msg_format: \"tuples\" | \"messages\";\n\texport let handle_action: (selected: string | null) => void;\n\texport let scroll: () => void;\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\n\tfunction handle_select(i: number, message: NormalisedMessage): void {\n\t\tdispatch(\"select\", {\n\t\t\tindex: message.index,\n\t\t\tvalue: message.content\n\t\t});\n\t}\n\n\tfunction get_message_label_data(message: NormalisedMessage): string {\n\t\tif (message.type === \"text\") {\n\t\t\treturn message.content;\n\t\t} else if (\n\t\t\tmessage.type === \"component\" &&\n\t\t\tmessage.content.component === \"file\"\n\t\t) {\n\t\t\tif (Array.isArray(message.content.value)) {\n\t\t\t\treturn `file of extension type: ${message.content.value[0].orig_name?.split(\".\").pop()}`;\n\t\t\t}\n\t\t\treturn (\n\t\t\t\t`file of extension type: ${message.content.value?.orig_name?.split(\".\").pop()}` +\n\t\t\t\t(message.content.value?.orig_name ?? \"\")\n\t\t\t);\n\t\t}\n\t\treturn `a component of type ${message.content.component ?? \"unknown\"}`;\n\t}\n\n\ttype ButtonPanelProps = {\n\t\thandle_action: (selected: string | null) => void;\n\t\tlikeable: boolean;\n\t\tshow_retry: boolean;\n\t\tshow_undo: boolean;\n\t\tgenerating: boolean;\n\t\tshow_copy_button: boolean;\n\t\tmessage: NormalisedMessage[] | NormalisedMessage;\n\t\tposition: \"left\" | \"right\";\n\t\tlayout: \"bubble\" | \"panel\";\n\t\tavatar: FileData | null;\n\t\tdispatch: any;\n\t};\n\n\tlet button_panel_props: ButtonPanelProps;\n\t$: button_panel_props = {\n\t\thandle_action,\n\t\tlikeable: show_like,\n\t\tshow_retry,\n\t\tshow_undo,\n\t\tgenerating,\n\t\tshow_copy_button,\n\t\tmessage: msg_format === \"tuples\" ? messages[0] : messages,\n\t\tposition: role === \"user\" ? \"right\" : \"left\",\n\t\tavatar: avatar_img,\n\t\tlayout,\n\t\tdispatch\n\t};\n</script>\n\n<div\n\tclass=\"message-row {layout} {role}-row\"\n\tclass:with_avatar={avatar_img !== null}\n\tclass:with_opposite_avatar={opposite_avatar_img !== null}\n>\n\t{#if avatar_img !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_img?.url} alt=\"{role} avatar\" />\n\t\t</div>\n\t{/if}\n\t<div\n\t\tclass:role\n\t\tclass=\"flex-wrap\"\n\t\tclass:component-wrap={messages[0].type === \"component\"}\n\t>\n\t\t<div\n\t\t\tclass:message={display_consecutive_in_same_bubble}\n\t\t\tclass={display_consecutive_in_same_bubble ? role : \"\"}\n\t\t>\n\t\t\t{#each messages as message, thought_index}\n\t\t\t\t<div\n\t\t\t\t\tclass=\"message {!display_consecutive_in_same_bubble ? role : ''}\"\n\t\t\t\t\tclass:panel-full-width={true}\n\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\tclass:component={message.type === \"component\"}\n\t\t\t\t\tclass:html={is_component_message(message) &&\n\t\t\t\t\t\tmessage.content.component === \"html\"}\n\t\t\t\t\tclass:thought={thought_index > 0}\n\t\t\t\t>\n\t\t\t\t\t<button\n\t\t\t\t\t\tdata-testid={role}\n\t\t\t\t\t\tclass:latest={i === value.length - 1}\n\t\t\t\t\t\tclass:message-markdown-disabled={!render_markdown}\n\t\t\t\t\t\tstyle:user-select=\"text\"\n\t\t\t\t\t\tclass:selectable\n\t\t\t\t\t\tstyle:cursor={selectable ? \"pointer\" : \"default\"}\n\t\t\t\t\t\tstyle:text-align={rtl ? \"right\" : \"left\"}\n\t\t\t\t\t\ton:click={() => handle_select(i, message)}\n\t\t\t\t\t\ton:keydown={(e) => {\n\t\t\t\t\t\t\tif (e.key === \"Enter\") {\n\t\t\t\t\t\t\t\thandle_select(i, message);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}}\n\t\t\t\t\t\tdir={rtl ? \"rtl\" : \"ltr\"}\n\t\t\t\t\t\taria-label={role + \"'s message: \" + get_message_label_data(message)}\n\t\t\t\t\t>\n\t\t\t\t\t\t{#if message.type === \"text\"}\n\t\t\t\t\t\t\t<div class=\"message-content\">\n\t\t\t\t\t\t\t\t{#if message?.metadata?.title}\n\t\t\t\t\t\t\t\t\t<MessageBox\n\t\t\t\t\t\t\t\t\t\ttitle={message.metadata.title}\n\t\t\t\t\t\t\t\t\t\texpanded={is_last_bot_message([message], value)}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t</MessageBox>\n\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t<Markdown\n\t\t\t\t\t\t\t\t\t\tmessage={message.content}\n\t\t\t\t\t\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t\t\t\t\t\t{render_markdown}\n\t\t\t\t\t\t\t\t\t\t{line_breaks}\n\t\t\t\t\t\t\t\t\t\ton:load={scroll}\n\t\t\t\t\t\t\t\t\t\t{root}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if message.type === \"component\" && message.content.component in _components}\n\t\t\t\t\t\t\t<Component\n\t\t\t\t\t\t\t\t{target}\n\t\t\t\t\t\t\t\t{theme_mode}\n\t\t\t\t\t\t\t\tprops={message.content.props}\n\t\t\t\t\t\t\t\ttype={message.content.component}\n\t\t\t\t\t\t\t\tcomponents={_components}\n\t\t\t\t\t\t\t\tvalue={message.content.value}\n\t\t\t\t\t\t\t\tdisplay_icon_button_wrapper_top_corner={thought_index > 0 &&\n\t\t\t\t\t\t\t\t\tdisplay_consecutive_in_same_bubble}\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\t{upload}\n\t\t\t\t\t\t\t\t{_fetch}\n\t\t\t\t\t\t\t\ton:load={() => scroll()}\n\t\t\t\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else if message.type === \"component\" && message.content.component === \"file\"}\n\t\t\t\t\t\t\t<div class=\"file-container\">\n\t\t\t\t\t\t\t\t<div class=\"file-icon\">\n\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class=\"file-info\">\n\t\t\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\t\t\tdata-testid=\"chatbot-file\"\n\t\t\t\t\t\t\t\t\t\tclass=\"file-link\"\n\t\t\t\t\t\t\t\t\t\thref={message.content.value.url}\n\t\t\t\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\t\t\t\tdownload={window.__is_colab__\n\t\t\t\t\t\t\t\t\t\t\t? null\n\t\t\t\t\t\t\t\t\t\t\t: message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\t\t\t\t\"file\"}\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<span class=\"file-name\"\n\t\t\t\t\t\t\t\t\t\t\t>{message.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.path.split(\"/\").pop() ||\n\t\t\t\t\t\t\t\t\t\t\t\t\"file\"}</span\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t\t\t\t<span class=\"file-type\"\n\t\t\t\t\t\t\t\t\t\t>{(\n\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.orig_name ||\n\t\t\t\t\t\t\t\t\t\t\tmessage.content.value?.path ||\n\t\t\t\t\t\t\t\t\t\t\t\"\"\n\t\t\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t\t\t\t\t.split(\".\")\n\t\t\t\t\t\t\t\t\t\t\t.pop()\n\t\t\t\t\t\t\t\t\t\t\t.toUpperCase()}</span\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t{/each}\n\t\t</div>\n\t\t{#if layout === \"panel\"}\n\t\t\t<ButtonPanel\n\t\t\t\t{...button_panel_props}\n\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t/>\n\t\t{/if}\n\t</div>\n</div>\n\n{#if layout === \"bubble\"}\n\t<ButtonPanel {...button_panel_props} />\n{/if}\n\n<style>\n\t.message {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t/* avatar styles */\n\t.avatar-container {\n\t\tflex-shrink: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\toverflow: hidden;\n\t}\n\n\t.avatar-container :global(img) {\n\t\tobject-fit: cover;\n\t}\n\n\t/* message wrapper */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\twidth: calc(100% - var(--spacing-xxl));\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.component {\n\t\tpadding: 0;\n\t\tborder-radius: var(--radius-md);\n\t\twidth: fit-content;\n\t\toverflow: hidden;\n\t}\n\n\t.component.gallery {\n\t\tborder: none;\n\t}\n\n\t.message-row :not(.avatar-container) :global(img) {\n\t\tmargin: var(--size-2);\n\t\tmax-height: 300px;\n\t}\n\n\t.file-pil {\n\t\tdisplay: block;\n\t\twidth: fit-content;\n\t\tpadding: var(--spacing-sm) var(--spacing-lg);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground: var(--background-fill-secondary);\n\t\tcolor: var(--body-text-color);\n\t\ttext-decoration: none;\n\t\tmargin: 0;\n\t\tfont-family: var(--font-mono);\n\t\tfont-size: var(--text-sm);\n\t}\n\n\t.file {\n\t\twidth: auto !important;\n\t\tmax-width: fit-content !important;\n\t}\n\n\t@media (max-width: 600px) or (max-width: 480px) {\n\t\t.component {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.message :global(.prose) {\n\t\tfont-size: var(--chatbot-text-size);\n\t}\n\n\t.message-bubble-border {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t}\n\n\t.panel-full-width {\n\t\twidth: 100%;\n\t}\n\t.message-markdown-disabled {\n\t\twhite-space: pre-line;\n\t}\n\n\t.user {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-md);\n\t\talign-self: flex-end;\n\t\tborder-bottom-right-radius: 0;\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder-color: var(--border-color-accent-subdued);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.bot {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\ttext-align: right;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* Colors */\n\t.bubble .bot {\n\t\tborder-color: var(--border-color-primary);\n\t}\n\n\t.message-row {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t}\n\n\t/* bubble mode styles */\n\t.bubble {\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.user-row {\n\t\talign-self: flex-end;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble.bot-row {\n\t\talign-self: flex-start;\n\t\tmax-width: calc(100% - var(--spacing-xl) * 6);\n\t}\n\n\t.bubble .user-row {\n\t\tflex-direction: row;\n\t\tjustify-content: flex-end;\n\t}\n\n\t.bubble .with_avatar.user-row {\n\t\tmargin-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_avatar.bot-row {\n\t\tmargin-left: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.bubble .with_opposite_avatar.user-row {\n\t\tmargin-left: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t/* panel mode styles */\n\t.panel {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t}\n\n\t.panel.bot-row {\n\t\tbackground: var(--background-fill-secondary);\n\t}\n\n\t.panel .with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.panel .panel-full-width {\n\t\twidth: 100%;\n\t}\n\n\t.panel .user :global(*) {\n\t\ttext-align: right;\n\t}\n\n\t/* message content */\n\t.flex-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmax-width: 100%;\n\t\tcolor: var(--body-text-color);\n\t\tfont-size: var(--chatbot-text-size);\n\t\toverflow-wrap: break-word;\n\t}\n\n\t@media (max-width: 480px) {\n\t\t.user-row.bubble {\n\t\t\talign-self: flex-end;\n\t\t}\n\n\t\t.bot-row.bubble {\n\t\t\talign-self: flex-start;\n\t\t}\n\t\t.message {\n\t\t\twidth: 100%;\n\t\t}\n\t}\n\n\t.message-content {\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\t.user-row > .avatar-container {\n\t\torder: 2;\n\t}\n\n\t.user-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-left: var(--spacing-xxl);\n\t}\n\n\t.panel.user-row > .avatar-container {\n\t\torder: 0;\n\t}\n\n\t.bot-row.bubble > .avatar-container {\n\t\tmargin-right: var(--spacing-xxl);\n\t\tmargin-left: 0;\n\t}\n\n\t.avatar-container:not(.thumbnail-item) :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t\tborder-radius: 50%;\n\t\tpadding: 6px;\n\t}\n\n\t.selectable {\n\t\tcursor: pointer;\n\t}\n\n\t@keyframes dot-flashing {\n\t\t0% {\n\t\t\topacity: 0.8;\n\t\t}\n\t\t50% {\n\t\t\topacity: 0.5;\n\t\t}\n\t\t100% {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n\n\t/* Image preview */\n\t.message :global(.preview) {\n\t\tobject-fit: contain;\n\t\twidth: 95%;\n\t\tmax-height: 93%;\n\t}\n\t.image-preview {\n\t\tposition: absolute;\n\t\tz-index: 999;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\toverflow: auto;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\t.image-preview :global(svg) {\n\t\tstroke: white;\n\t}\n\t.image-preview-close-button {\n\t\tposition: absolute;\n\t\ttop: 10px;\n\t\tright: 10px;\n\t\tbackground: none;\n\t\tborder: none;\n\t\tfont-size: 1.5em;\n\t\tcursor: pointer;\n\t\theight: 30px;\n\t\twidth: 30px;\n\t\tpadding: 3px;\n\t\tbackground: var(--bg-color);\n\t\tbox-shadow: var(--shadow-drop);\n\t\tborder: 1px solid var(--button-secondary-border-color);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.message > button {\n\t\twidth: 100%;\n\t}\n\t.html {\n\t\tpadding: 0;\n\t\tborder: none;\n\t\tbackground: none;\n\t}\n\n\t.thought {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.panel .bot,\n\t.panel .user {\n\t\tborder: none;\n\t\tbox-shadow: none;\n\t\tbackground-color: var(--background-fill-secondary);\n\t}\n\n\t.panel.user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row,\n\t.panel .bot-row {\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .user :global(*),\n\t.panel .bot :global(*) {\n\t\ttext-align: left;\n\t}\n\n\t.panel .user {\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.panel .user-row {\n\t\tbackground-color: var(--color-accent-soft);\n\t\talign-self: flex-start;\n\t}\n\n\t.panel .message {\n\t\tmargin-bottom: var(--spacing-md);\n\t}\n\n\t.file-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\tpadding: var(--spacing-lg);\n\t\tborder-radius: var(--radius-lg);\n\t\twidth: fit-content;\n\t\tmargin: var(--spacing-sm) 0;\n\t}\n\n\t.file-icon {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcolor: var(--body-text-color);\n\t}\n\n\t.file-icon :global(svg) {\n\t\twidth: var(--size-7);\n\t\theight: var(--size-7);\n\t}\n\n\t.file-info {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.file-link {\n\t\ttext-decoration: none;\n\t\tcolor: var(--body-text-color);\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: var(--spacing-xs);\n\t}\n\n\t.file-name {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-md);\n\t\tfont-weight: 500;\n\t}\n\n\t.file-type {\n\t\tfont-family: var(--font);\n\t\tfont-size: var(--text-sm);\n\t\tcolor: var(--body-text-color-subdued);\n\t\ttext-transform: uppercase;\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let layout = \"bubble\";\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n</script>\n\n<div class=\"container\">\n\t{#if avatar_images[1] !== null}\n\t\t<div class=\"avatar-container\">\n\t\t\t<Image class=\"avatar-image\" src={avatar_images[1].url} alt=\"bot avatar\" />\n\t\t</div>\n\t{/if}\n\n\t<div\n\t\tclass=\"message bot pending {layout}\"\n\t\tclass:with_avatar={avatar_images[1] !== null}\n\t\tclass:with_opposite_avatar={avatar_images[0] !== null}\n\t\trole=\"status\"\n\t\taria-label=\"Loading response\"\n\t\taria-live=\"polite\"\n\t>\n\t\t<div class=\"message-content\">\n\t\t\t<span class=\"sr-only\">Loading content</span>\n\t\t\t<div class=\"dots\">\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t\t<div class=\"dot\" />\n\t\t\t</div>\n\t\t</div>\n\t</div>\n</div>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tmargin: calc(var(--spacing-xl) * 2);\n\t}\n\n\t.bubble.pending {\n\t\tborder-width: 1px;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder-bottom-left-radius: 0;\n\t\tborder-color: var(--border-color-primary);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tbox-shadow: var(--shadow-drop);\n\t\talign-self: flex-start;\n\t\twidth: fit-content;\n\t\tmargin-bottom: var(--spacing-xl);\n\t}\n\n\t.bubble.with_opposite_avatar {\n\t\tmargin-right: calc(var(--spacing-xxl) + 35px + var(--spacing-xxl));\n\t}\n\n\t.panel.pending {\n\t\tmargin: 0;\n\t\tpadding: calc(var(--spacing-lg) * 2) calc(var(--spacing-lg) * 2);\n\t\twidth: 100%;\n\t\tborder: none;\n\t\tbackground: none;\n\t\tbox-shadow: none;\n\t\tborder-radius: 0;\n\t}\n\n\t.panel.with_avatar {\n\t\tpadding-left: calc(var(--spacing-xl) * 2) !important;\n\t\tpadding-right: calc(var(--spacing-xl) * 2) !important;\n\t}\n\n\t.avatar-container {\n\t\talign-self: flex-start;\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 35px;\n\t\theight: 35px;\n\t\tflex-shrink: 0;\n\t\tbottom: 0;\n\t\tborder-radius: 50%;\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tmargin-right: var(--spacing-xxl);\n\t}\n\n\t.message-content {\n\t\tpadding: var(--spacing-sm) var(--spacing-xl);\n\t\tmin-height: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.dots {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-xs);\n\t\talign-items: center;\n\t}\n\n\t.dot {\n\t\twidth: var(--size-1-5);\n\t\theight: var(--size-1-5);\n\t\tmargin-right: var(--spacing-xs);\n\t\tborder-radius: 50%;\n\t\tbackground-color: var(--body-text-color);\n\t\topacity: 0.5;\n\t\tanimation: pulse 1.5s infinite;\n\t}\n\n\t.dot:nth-child(2) {\n\t\tanimation-delay: 0.2s;\n\t}\n\n\t.dot:nth-child(3) {\n\t\tanimation-delay: 0.4s;\n\t}\n\n\t@keyframes pulse {\n\t\t0%,\n\t\t100% {\n\t\t\topacity: 0.4;\n\t\t\ttransform: scale(1);\n\t\t}\n\t\t50% {\n\t\t\topacity: 1;\n\t\t\ttransform: scale(1.1);\n\t\t}\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { Image } from \"@gradio/image/shared\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport { File, Music, Video } from \"@gradio/icons\";\n\timport type { ExampleMessage } from \"../types\";\n\timport { createEventDispatcher } from \"svelte\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport type { FileData } from \"@gradio/client\";\n\n\texport let examples: ExampleMessage[] | null = null;\n\texport let placeholder: string | null = null;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let root: string;\n\n\tconst dispatch = createEventDispatcher<{\n\t\texample_select: SelectData;\n\t}>();\n\n\tfunction handle_example_select(\n\t\ti: number,\n\t\texample: ExampleMessage | string\n\t): void {\n\t\tconst example_obj =\n\t\t\ttypeof example === \"string\" ? { text: example } : example;\n\t\tdispatch(\"example_select\", {\n\t\t\tindex: i,\n\t\t\tvalue: { text: example_obj.text, files: example_obj.files }\n\t\t});\n\t}\n</script>\n\n<div class=\"placeholder-content\" role=\"complementary\">\n\t{#if placeholder !== null}\n\t\t<div class=\"placeholder\">\n\t\t\t<Markdown message={placeholder} {latex_delimiters} {root} />\n\t\t</div>\n\t{/if}\n\t{#if examples !== null}\n\t\t<div class=\"examples\" role=\"list\">\n\t\t\t{#each examples as example, i}\n\t\t\t\t<button\n\t\t\t\t\tclass=\"example\"\n\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\thandle_example_select(\n\t\t\t\t\t\t\ti,\n\t\t\t\t\t\t\ttypeof example === \"string\" ? { text: example } : example\n\t\t\t\t\t\t)}\n\t\t\t\t\taria-label={`Select example ${i + 1}: ${example.display_text || example.text}`}\n\t\t\t\t>\n\t\t\t\t\t<div class=\"example-content\">\n\t\t\t\t\t\t{#if example?.icon?.url}\n\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\tsrc={example.icon.url}\n\t\t\t\t\t\t\t\t\talt=\"Example icon\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example?.icon?.mime_type === \"text\"}\n\t\t\t\t\t\t\t<div class=\"example-icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t\t<span class=\"text-icon-aa\">Aa</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{:else if example.files !== undefined && example.files.length > 0}\n\t\t\t\t\t\t\t{#if example.files.length > 1}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icons-grid\"\n\t\t\t\t\t\t\t\t\trole=\"group\"\n\t\t\t\t\t\t\t\t\taria-label=\"Example attachments\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{#each example.files.slice(0, 4) as file, i}\n\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={file.orig_name || `Example image ${i + 1}`}\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else if file.mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={file.url}\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if i === 3 && example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"image-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`File: ${file.orig_name}`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t{#if file.mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t\t{/each}\n\t\t\t\t\t\t\t\t\t{#if example.files.length > 4}\n\t\t\t\t\t\t\t\t\t\t<div class=\"example-icon\">\n\t\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"file-overlay\"\n\t\t\t\t\t\t\t\t\t\t\t\trole=\"status\"\n\t\t\t\t\t\t\t\t\t\t\t\taria-label={`${example.files.length - 4} more files`}\n\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t+{example.files.length - 4}\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"image\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\talt={example.files[0].orig_name || \"Example image\"}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"video\")}\n\t\t\t\t\t\t\t\t<div class=\"example-image-container\">\n\t\t\t\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t\t\t\tclass=\"example-image\"\n\t\t\t\t\t\t\t\t\t\tsrc={example.files[0].url}\n\t\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else if example.files[0].mime_type?.includes(\"audio\")}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<Music />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\tclass=\"example-icon\"\n\t\t\t\t\t\t\t\t\taria-label={`File: ${example.files[0].orig_name}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<File />\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{/if}\n\n\t\t\t\t\t\t<div class=\"example-text-content\">\n\t\t\t\t\t\t\t<span class=\"example-text\"\n\t\t\t\t\t\t\t\t>{example.display_text || example.text}</span\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</button>\n\t\t\t{/each}\n\t\t</div>\n\t{/if}\n</div>\n\n<style>\n\t.placeholder-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\theight: 100%;\n\t}\n\n\t.placeholder {\n\t\talign-items: center;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t.examples :global(img) {\n\t\tpointer-events: none;\n\t}\n\n\t.examples {\n\t\tmargin: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(240px, 1fr));\n\t\tgap: var(--spacing-xl);\n\t\tmax-width: calc(min(4 * 240px + 5 * var(--spacing-xxl), 100%));\n\t}\n\n\t.example {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tpadding: var(--spacing-xxl);\n\t\tborder: none;\n\t\tborder-radius: var(--radius-lg);\n\t\tbackground-color: var(--block-background-fill);\n\t\tcursor: pointer;\n\t\ttransition: all 150ms ease-in-out;\n\t\twidth: 100%;\n\t\tgap: var(--spacing-sm);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\ttransform: translateY(0px);\n\t}\n\n\t.example:hover {\n\t\ttransform: translateY(-2px);\n\t\tbackground-color: var(--color-accent-soft);\n\t}\n\n\t.example-content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.example-text-content {\n\t\tmargin-top: auto;\n\t\ttext-align: left;\n\t}\n\n\t.example-text {\n\t\tfont-size: var(--text-md);\n\t\ttext-align: left;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.example-icons-grid {\n\t\tdisplay: flex;\n\t\tgap: var(--spacing-sm);\n\t\tmargin-bottom: var(--spacing-lg);\n\t\twidth: 100%;\n\t}\n\n\t.example-icon {\n\t\tflex-shrink: 0;\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: var(--radius-lg);\n\t\tborder: var(--block-border-width) solid var(--block-border-color);\n\t\tbackground-color: var(--block-background-fill);\n\t\tposition: relative;\n\t}\n\n\t.example-icon :global(svg) {\n\t\twidth: var(--size-4);\n\t\theight: var(--size-4);\n\t\tcolor: var(--color-text-secondary);\n\t}\n\n\t.text-icon-aa {\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tcolor: var(--color-text-secondary);\n\t\tline-height: 1;\n\t}\n\n\t.example-image-container {\n\t\twidth: var(--size-8);\n\t\theight: var(--size-8);\n\t\tborder-radius: var(--radius-lg);\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tmargin-bottom: var(--spacing-lg);\n\t}\n\n\t.example-image-container :global(img) {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\t.image-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-lg);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.file-overlay {\n\t\tposition: absolute;\n\t\tinset: 0;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: var(--text-sm);\n\t\tfont-weight: var(--weight-semibold);\n\t\tborder-radius: var(--radius-lg);\n\t}\n</style>\n", "<script lang=\"ts\">\n\timport { onDestroy } from \"svelte\";\n\timport { Copy, Check } from \"@gradio/icons\";\n\timport type { NormalisedMessage } from \"../types\";\n\timport { IconButton } from \"@gradio/atoms\";\n\n\tlet copied = false;\n\texport let value: NormalisedMessage[] | null;\n\n\tlet timer: NodeJS.Timeout;\n\n\tfunction copy_feedback(): void {\n\t\tcopied = true;\n\t\tif (timer) clearTimeout(timer);\n\t\ttimer = setTimeout(() => {\n\t\t\tcopied = false;\n\t\t}, 1000);\n\t}\n\n\tconst copy_conversation = (): void => {\n\t\tif (value) {\n\t\t\tconst conversation_value = value\n\t\t\t\t.map((message) => {\n\t\t\t\t\tif (message.type === \"text\") {\n\t\t\t\t\t\treturn `${message.role}: ${message.content}`;\n\t\t\t\t\t}\n\t\t\t\t\treturn `${message.role}: ${message.content.value.url}`;\n\t\t\t\t})\n\t\t\t\t.join(\"\\n\\n\");\n\n\t\t\tnavigator.clipboard.writeText(conversation_value).catch((err) => {\n\t\t\t\tconsole.error(\"Failed to copy conversation: \", err);\n\t\t\t});\n\t\t}\n\t};\n\n\tasync function handle_copy(): Promise<void> {\n\t\tif (\"clipboard\" in navigator) {\n\t\t\tcopy_conversation();\n\t\t\tcopy_feedback();\n\t\t}\n\t}\n\n\tonDestroy(() => {\n\t\tif (timer) clearTimeout(timer);\n\t});\n</script>\n\n<IconButton\n\tIcon={copied ? Check : Copy}\n\ton:click={handle_copy}\n\tlabel={copied ? \"Copied conversation\" : \"Copy conversation\"}\n></IconButton>\n", "<script lang=\"ts\">\n\timport {\n\t\tformat_chat_for_sharing,\n\t\ttype UndoRetryData,\n\t\tis_last_bot_message,\n\t\tgroup_messages,\n\t\tload_components,\n\t\tget_components_from_messages\n\t} from \"./utils\";\n\timport type { NormalisedMessage, Option } from \"../types\";\n\timport { copy } from \"@gradio/utils\";\n\timport type { CopyData } from \"@gradio/utils\";\n\timport Message from \"./Message.svelte\";\n\timport { DownloadLink } from \"@gradio/wasm/svelte\";\n\n\timport { dequal } from \"dequal/lite\";\n\timport {\n\t\tcreateEventDispatcher,\n\t\ttype SvelteComponent,\n\t\ttype ComponentType,\n\t\ttick,\n\t\tonMount\n\t} from \"svelte\";\n\timport { Image } from \"@gradio/image/shared\";\n\n\timport { Trash, Community, ScrollDownArrow } from \"@gradio/icons\";\n\timport { IconButtonWrapper, IconButton } from \"@gradio/atoms\";\n\timport type { SelectData, LikeData } from \"@gradio/utils\";\n\timport type { ExampleMessage } from \"../types\";\n\timport { MarkdownCode as Markdown } from \"@gradio/markdown-code\";\n\timport type { FileData, Client } from \"@gradio/client\";\n\timport type { I18nFormatter } from \"js/core/src/gradio_helper\";\n\timport Pending from \"./Pending.svelte\";\n\timport { ShareError } from \"@gradio/utils\";\n\timport { Gradio } from \"@gradio/utils\";\n\n\timport Examples from \"./Examples.svelte\";\n\n\texport let value: NormalisedMessage[] | null = [];\n\tlet old_value: NormalisedMessage[] | null = null;\n\n\timport CopyAll from \"./CopyAll.svelte\";\n\n\texport let _fetch: typeof fetch;\n\texport let load_component: Gradio[\"load_component\"];\n\texport let allow_file_downloads: boolean;\n\texport let display_consecutive_in_same_bubble: boolean;\n\n\tlet _components: Record<string, ComponentType<SvelteComponent>> = {};\n\n\tconst is_browser = typeof window !== \"undefined\";\n\n\tasync function update_components(): Promise<void> {\n\t\t_components = await load_components(\n\t\t\tget_components_from_messages(value),\n\t\t\t_components,\n\t\t\tload_component\n\t\t);\n\t}\n\n\t$: value, update_components();\n\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let pending_message = false;\n\texport let generating = false;\n\texport let selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let show_copy_all_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = false;\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let sanitize_html = true;\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let i18n: I18nFormatter;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let placeholder: string | null = null;\n\texport let upload: Client[\"upload\"];\n\texport let msg_format: \"tuples\" | \"messages\" = \"tuples\";\n\texport let examples: ExampleMessage[] | null = null;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let like_user_message = false;\n\texport let root: string;\n\n\tlet target: HTMLElement | null = null;\n\n\tonMount(() => {\n\t\ttarget = document.querySelector(\"div.gradio-container\");\n\t});\n\n\tlet div: HTMLDivElement;\n\n\tlet show_scroll_button = false;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t\tlike: LikeData;\n\t\tundo: UndoRetryData;\n\t\tretry: UndoRetryData;\n\t\tclear: undefined;\n\t\tshare: any;\n\t\terror: string;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tcopy: CopyData;\n\t}>();\n\n\tfunction is_at_bottom(): boolean {\n\t\treturn div && div.offsetHeight + div.scrollTop > div.scrollHeight - 100;\n\t}\n\n\tfunction scroll_to_bottom(): void {\n\t\tif (!div) return;\n\t\tdiv.scrollTo(0, div.scrollHeight);\n\t\tshow_scroll_button = false;\n\t}\n\n\tlet scroll_after_component_load = false;\n\tfunction on_child_component_load(): void {\n\t\tif (scroll_after_component_load) {\n\t\t\tscroll_to_bottom();\n\t\t\tscroll_after_component_load = false;\n\t\t}\n\t}\n\n\tasync function scroll_on_value_update(): Promise<void> {\n\t\tif (!autoscroll) return;\n\n\t\tif (is_at_bottom()) {\n\t\t\t// Child components may be loaded asynchronously,\n\t\t\t// so trigger the scroll again after they load.\n\t\t\tscroll_after_component_load = true;\n\n\t\t\tawait tick(); // Wait for the DOM to update so that the scrollHeight is correct\n\t\t\tscroll_to_bottom();\n\t\t} else {\n\t\t\tshow_scroll_button = true;\n\t\t}\n\t}\n\tonMount(() => {\n\t\tscroll_on_value_update();\n\t});\n\t$: if (value || pending_message || _components) {\n\t\tscroll_on_value_update();\n\t}\n\n\tonMount(() => {\n\t\tfunction handle_scroll(): void {\n\t\t\tif (is_at_bottom()) {\n\t\t\t\tshow_scroll_button = false;\n\t\t\t} else {\n\t\t\t\tscroll_after_component_load = false;\n\t\t\t}\n\t\t}\n\n\t\tdiv?.addEventListener(\"scroll\", handle_scroll);\n\t\treturn () => {\n\t\t\tdiv?.removeEventListener(\"scroll\", handle_scroll);\n\t\t};\n\t});\n\n\t$: {\n\t\tif (!dequal(value, old_value)) {\n\t\t\told_value = value;\n\t\t\tdispatch(\"change\");\n\t\t}\n\t}\n\t$: groupedMessages = value && group_messages(value, msg_format);\n\t$: options = value && get_last_bot_options();\n\n\tfunction handle_example_select(i: number, example: ExampleMessage): void {\n\t\tdispatch(\"example_select\", {\n\t\t\tindex: i,\n\t\t\tvalue: { text: example.text, files: example.files }\n\t\t});\n\t}\n\n\tfunction handle_like(\n\t\ti: number,\n\t\tmessage: NormalisedMessage,\n\t\tselected: string | null\n\t): void {\n\t\tif (selected === \"undo\" || selected === \"retry\") {\n\t\t\tconst val_ = value as NormalisedMessage[];\n\t\t\t// iterate through messages until we find the last user message\n\t\t\t// the index of this message is where the user needs to edit the chat history\n\t\t\tlet last_index = val_.length - 1;\n\t\t\twhile (val_[last_index].role === \"assistant\") {\n\t\t\t\tlast_index--;\n\t\t\t}\n\t\t\tdispatch(selected, {\n\t\t\t\tindex: val_[last_index].index,\n\t\t\t\tvalue: val_[last_index].content\n\t\t\t});\n\t\t\treturn;\n\t\t}\n\n\t\tif (msg_format === \"tuples\") {\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: message.index,\n\t\t\t\tvalue: message.content,\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t} else {\n\t\t\tif (!groupedMessages) return;\n\n\t\t\tconst message_group = groupedMessages[i];\n\t\t\tconst [first, last] = [\n\t\t\t\tmessage_group[0],\n\t\t\t\tmessage_group[message_group.length - 1]\n\t\t\t];\n\n\t\t\tdispatch(\"like\", {\n\t\t\t\tindex: [first.index, last.index] as [number, number],\n\t\t\t\tvalue: message_group.map((m) => m.content),\n\t\t\t\tliked: selected === \"like\"\n\t\t\t});\n\t\t}\n\t}\n\n\tfunction get_last_bot_options(): Option[] | undefined {\n\t\tif (!value || !groupedMessages || groupedMessages.length === 0)\n\t\t\treturn undefined;\n\t\tconst last_group = groupedMessages[groupedMessages.length - 1];\n\t\tif (last_group[0].role !== \"assistant\") return undefined;\n\t\treturn last_group[last_group.length - 1].options;\n\t}\n</script>\n\n{#if value !== null && value.length > 0}\n\t<IconButtonWrapper>\n\t\t{#if show_share_button}\n\t\t\t<IconButton\n\t\t\t\tIcon={Community}\n\t\t\t\ton:click={async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\tconst formatted = await format_chat_for_sharing(value);\n\t\t\t\t\t\tdispatch(\"share\", {\n\t\t\t\t\t\t\tdescription: formatted\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.error(e);\n\t\t\t\t\t\tlet message = e instanceof ShareError ? e.message : \"Share failed.\";\n\t\t\t\t\t\tdispatch(\"error\", message);\n\t\t\t\t\t}\n\t\t\t\t}}\n\t\t\t/>\n\t\t{/if}\n\t\t<IconButton Icon={Trash} on:click={() => dispatch(\"clear\")} label={\"Clear\"}\n\t\t></IconButton>\n\t\t{#if show_copy_all_button}\n\t\t\t<CopyAll {value} />\n\t\t{/if}\n\t</IconButtonWrapper>\n{/if}\n\n<div\n\tclass={layout === \"bubble\" ? \"bubble-wrap\" : \"panel-wrap\"}\n\tbind:this={div}\n\trole=\"log\"\n\taria-label=\"chatbot conversation\"\n\taria-live=\"polite\"\n>\n\t{#if value !== null && value.length > 0 && groupedMessages !== null}\n\t\t<div class=\"message-wrap\" use:copy>\n\t\t\t{#each groupedMessages as messages, i}\n\t\t\t\t{@const role = messages[0].role === \"user\" ? \"user\" : \"bot\"}\n\t\t\t\t{@const avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t{@const opposite_avatar_img = avatar_images[role === \"user\" ? 0 : 1]}\n\t\t\t\t<Message\n\t\t\t\t\t{messages}\n\t\t\t\t\t{display_consecutive_in_same_bubble}\n\t\t\t\t\t{opposite_avatar_img}\n\t\t\t\t\t{avatar_img}\n\t\t\t\t\t{role}\n\t\t\t\t\t{layout}\n\t\t\t\t\t{dispatch}\n\t\t\t\t\t{i18n}\n\t\t\t\t\t{_fetch}\n\t\t\t\t\t{line_breaks}\n\t\t\t\t\t{theme_mode}\n\t\t\t\t\t{target}\n\t\t\t\t\t{root}\n\t\t\t\t\t{upload}\n\t\t\t\t\t{selectable}\n\t\t\t\t\t{sanitize_html}\n\t\t\t\t\t{render_markdown}\n\t\t\t\t\t{rtl}\n\t\t\t\t\t{i}\n\t\t\t\t\t{value}\n\t\t\t\t\t{latex_delimiters}\n\t\t\t\t\t{_components}\n\t\t\t\t\t{generating}\n\t\t\t\t\t{msg_format}\n\t\t\t\t\tshow_like={role === \"user\" ? likeable && like_user_message : likeable}\n\t\t\t\t\tshow_retry={_retryable && is_last_bot_message(messages, value)}\n\t\t\t\t\tshow_undo={_undoable && is_last_bot_message(messages, value)}\n\t\t\t\t\t{show_copy_button}\n\t\t\t\t\thandle_action={(selected) => handle_like(i, messages[0], selected)}\n\t\t\t\t\tscroll={is_browser ? scroll : () => {}}\n\t\t\t\t\t{allow_file_downloads}\n\t\t\t\t\ton:copy={(e) => dispatch(\"copy\", e.detail)}\n\t\t\t\t/>\n\t\t\t{/each}\n\t\t\t{#if pending_message}\n\t\t\t\t<Pending {layout} {avatar_images} />\n\t\t\t{:else if options}\n\t\t\t\t<div class=\"options\">\n\t\t\t\t\t{#each options as option, index}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"option\"\n\t\t\t\t\t\t\ton:click={() =>\n\t\t\t\t\t\t\t\tdispatch(\"option_select\", {\n\t\t\t\t\t\t\t\t\tindex: index,\n\t\t\t\t\t\t\t\t\tvalue: option.value\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{option.label || option.value}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t{/if}\n\t\t</div>\n\t{:else}\n\t\t<Examples\n\t\t\t{examples}\n\t\t\t{placeholder}\n\t\t\t{latex_delimiters}\n\t\t\t{root}\n\t\t\ton:example_select={(e) => dispatch(\"example_select\", e.detail)}\n\t\t/>\n\t{/if}\n</div>\n\n{#if show_scroll_button}\n\t<div class=\"scroll-down-button-container\">\n\t\t<IconButton\n\t\t\tIcon={ScrollDownArrow}\n\t\t\tlabel=\"Scroll down\"\n\t\t\tsize=\"large\"\n\t\t\ton:click={scroll_to_bottom}\n\t\t/>\n\t</div>\n{/if}\n\n<style>\n\t.panel-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t}\n\n\t.bubble-wrap {\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\theight: 100%;\n\t\tpadding-top: var(--spacing-xxl);\n\t}\n\n\t@media (prefers-color-scheme: dark) {\n\t\t.bubble-wrap {\n\t\t\tbackground: var(--background-fill-secondary);\n\t\t}\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.message-wrap :global(.prose.chatbot.md) {\n\t\topacity: 0.8;\n\t\toverflow-wrap: break-word;\n\t}\n\n\t.message-wrap :global(.message-row .md img) {\n\t\tborder-radius: var(--radius-xl);\n\t\tmargin: var(--size-2);\n\t\twidth: 400px;\n\t\tmax-width: 30vw;\n\t\tmax-height: 30vw;\n\t}\n\n\t/* link styles */\n\t.message-wrap :global(.message a) {\n\t\tcolor: var(--color-text-link);\n\t\ttext-decoration: underline;\n\t}\n\n\t/* table styles */\n\t.message-wrap :global(.bot table),\n\t.message-wrap :global(.bot tr),\n\t.message-wrap :global(.bot td),\n\t.message-wrap :global(.bot th) {\n\t\tborder: 1px solid var(--border-color-primary);\n\t}\n\n\t.message-wrap :global(.user table),\n\t.message-wrap :global(.user tr),\n\t.message-wrap :global(.user td),\n\t.message-wrap :global(.user th) {\n\t\tborder: 1px solid var(--border-color-accent);\n\t}\n\n\t/* KaTeX */\n\t.message-wrap :global(span.katex) {\n\t\tfont-size: var(--text-lg);\n\t\tdirection: ltr;\n\t}\n\n\t.message-wrap :global(span.katex-display) {\n\t\tmargin-top: 0;\n\t}\n\n\t.message-wrap :global(pre) {\n\t\tposition: relative;\n\t}\n\n\t.message-wrap :global(.grid-wrap) {\n\t\tmax-height: 80% !important;\n\t\tmax-width: 600px;\n\t\tobject-fit: contain;\n\t}\n\n\t.message-wrap > div :global(p:not(:first-child)) {\n\t\tmargin-top: var(--spacing-xxl);\n\t}\n\n\t.message-wrap {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: var(--spacing-xxl);\n\t}\n\n\t.panel-wrap :global(.message-row:first-child) {\n\t\tpadding-top: calc(var(--spacing-xxl) * 2);\n\t}\n\n\t.scroll-down-button-container {\n\t\tposition: absolute;\n\t\tbottom: 10px;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tz-index: var(--layer-top);\n\t}\n\t.scroll-down-button-container :global(button) {\n\t\tborder-radius: 50%;\n\t\tbox-shadow: var(--shadow-drop);\n\t\ttransition:\n\t\t\tbox-shadow 0.2s ease-in-out,\n\t\t\ttransform 0.2s ease-in-out;\n\t}\n\t.scroll-down-button-container :global(button:hover) {\n\t\tbox-shadow:\n\t\t\tvar(--shadow-drop),\n\t\t\t0 2px 2px rgba(0, 0, 0, 0.05);\n\t\ttransform: translateY(-2px);\n\t}\n\n\t.options {\n\t\tmargin-left: auto;\n\t\tpadding: var(--spacing-xxl);\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n\t\tgap: var(--spacing-xxl);\n\t\tmax-width: calc(min(4 * 200px + 5 * var(--spacing-xxl), 100%));\n\t\tjustify-content: end;\n\t}\n\n\t.option {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: var(--spacing-xl);\n\t\tborder: 1px dashed var(--border-color-primary);\n\t\tborder-radius: var(--radius-md);\n\t\tbackground-color: var(--background-fill-secondary);\n\t\tcursor: pointer;\n\t\ttransition: var(--button-transition);\n\t\tmax-width: var(--size-56);\n\t\twidth: 100%;\n\t\tjustify-content: center;\n\t}\n\n\t.option:hover {\n\t\tbackground-color: var(--color-accent-soft);\n\t\tborder-color: var(--border-color-accent);\n\t}\n</style>\n", "<script context=\"module\" lang=\"ts\">\n\texport { default as BaseChatBot } from \"./shared/ChatBot.svelte\";\n</script>\n\n<script lang=\"ts\">\n\timport type { Gradio, SelectData, LikeData, CopyData } from \"@gradio/utils\";\n\n\timport ChatBot from \"./shared/ChatBot.svelte\";\n\timport type { UndoRetryData } from \"./shared/utils\";\n\timport { Block, BlockLabel } from \"@gradio/atoms\";\n\timport type { LoadingStatus } from \"@gradio/statustracker\";\n\timport { Chat } from \"@gradio/icons\";\n\timport type { FileData } from \"@gradio/client\";\n\timport { StatusTracker } from \"@gradio/statustracker\";\n\timport type {\n\t\tMessage,\n\t\tExampleMessage,\n\t\tTupleFormat,\n\t\tNormalisedMessage\n\t} from \"./types\";\n\n\timport { normalise_tuples, normalise_messages } from \"./shared/utils\";\n\n\texport let elem_id = \"\";\n\texport let elem_classes: string[] = [];\n\texport let visible = true;\n\texport let value: TupleFormat | Message[] = [];\n\texport let scale: number | null = null;\n\texport let min_width: number | undefined = undefined;\n\texport let label: string;\n\texport let show_label = true;\n\texport let root: string;\n\texport let _selectable = false;\n\texport let likeable = false;\n\texport let show_share_button = false;\n\texport let rtl = false;\n\texport let show_copy_button = true;\n\texport let show_copy_all_button = false;\n\texport let sanitize_html = true;\n\texport let layout: \"bubble\" | \"panel\" = \"bubble\";\n\texport let type: \"tuples\" | \"messages\" = \"tuples\";\n\texport let render_markdown = true;\n\texport let line_breaks = true;\n\texport let autoscroll = true;\n\texport let _retryable = false;\n\texport let _undoable = false;\n\texport let group_consecutive_messages = true;\n\texport let latex_delimiters: {\n\t\tleft: string;\n\t\tright: string;\n\t\tdisplay: boolean;\n\t}[];\n\texport let gradio: Gradio<{\n\t\tchange: typeof value;\n\t\tselect: SelectData;\n\t\tshare: ShareData;\n\t\terror: string;\n\t\tlike: LikeData;\n\t\tclear_status: LoadingStatus;\n\t\texample_select: SelectData;\n\t\toption_select: SelectData;\n\t\tretry: UndoRetryData;\n\t\tundo: UndoRetryData;\n\t\tclear: null;\n\t\tcopy: CopyData;\n\t}>;\n\n\tlet _value: NormalisedMessage[] | null = [];\n\n\t$: _value =\n\t\ttype === \"tuples\"\n\t\t\t? normalise_tuples(value as TupleFormat, root)\n\t\t\t: normalise_messages(value as Message[], root);\n\n\texport let avatar_images: [FileData | null, FileData | null] = [null, null];\n\texport let like_user_message = false;\n\texport let loading_status: LoadingStatus | undefined = undefined;\n\texport let height: number | string | undefined;\n\texport let resizeable: boolean;\n\texport let min_height: number | string | undefined;\n\texport let max_height: number | string | undefined;\n\texport let placeholder: string | null = null;\n\texport let examples: ExampleMessage[] | null = null;\n\texport let theme_mode: \"system\" | \"light\" | \"dark\";\n\texport let allow_file_downloads = true;\n</script>\n\n<Block\n\t{elem_id}\n\t{elem_classes}\n\t{visible}\n\tpadding={false}\n\t{scale}\n\t{min_width}\n\t{height}\n\t{resizeable}\n\t{min_height}\n\t{max_height}\n\tallow_overflow={true}\n\tflex={true}\n\toverflow_behavior=\"auto\"\n>\n\t{#if loading_status}\n\t\t<StatusTracker\n\t\t\tautoscroll={gradio.autoscroll}\n\t\t\ti18n={gradio.i18n}\n\t\t\t{...loading_status}\n\t\t\tshow_progress={loading_status.show_progress === \"hidden\"\n\t\t\t\t? \"hidden\"\n\t\t\t\t: \"minimal\"}\n\t\t\ton:clear_status={() => gradio.dispatch(\"clear_status\", loading_status)}\n\t\t/>\n\t{/if}\n\t<div class=\"wrapper\">\n\t\t{#if show_label}\n\t\t\t<BlockLabel\n\t\t\t\t{show_label}\n\t\t\t\tIcon={Chat}\n\t\t\t\tfloat={true}\n\t\t\t\tlabel={label || \"Chatbot\"}\n\t\t\t/>\n\t\t{/if}\n\t\t<ChatBot\n\t\t\ti18n={gradio.i18n}\n\t\t\tselectable={_selectable}\n\t\t\t{likeable}\n\t\t\t{show_share_button}\n\t\t\t{show_copy_all_button}\n\t\t\tvalue={_value}\n\t\t\t{latex_delimiters}\n\t\t\tdisplay_consecutive_in_same_bubble={group_consecutive_messages}\n\t\t\t{render_markdown}\n\t\t\t{theme_mode}\n\t\t\tpending_message={loading_status?.status === \"pending\"}\n\t\t\tgenerating={loading_status?.status === \"generating\"}\n\t\t\t{rtl}\n\t\t\t{show_copy_button}\n\t\t\t{like_user_message}\n\t\t\ton:change={() => gradio.dispatch(\"change\", value)}\n\t\t\ton:select={(e) => gradio.dispatch(\"select\", e.detail)}\n\t\t\ton:like={(e) => gradio.dispatch(\"like\", e.detail)}\n\t\t\ton:share={(e) => gradio.dispatch(\"share\", e.detail)}\n\t\t\ton:error={(e) => gradio.dispatch(\"error\", e.detail)}\n\t\t\ton:example_select={(e) => gradio.dispatch(\"example_select\", e.detail)}\n\t\t\ton:option_select={(e) => gradio.dispatch(\"option_select\", e.detail)}\n\t\t\ton:retry={(e) => gradio.dispatch(\"retry\", e.detail)}\n\t\t\ton:undo={(e) => gradio.dispatch(\"undo\", e.detail)}\n\t\t\ton:clear={() => {\n\t\t\t\tvalue = [];\n\t\t\t\tgradio.dispatch(\"clear\");\n\t\t\t}}\n\t\t\ton:copy={(e) => gradio.dispatch(\"copy\", e.detail)}\n\t\t\t{avatar_images}\n\t\t\t{sanitize_html}\n\t\t\t{line_breaks}\n\t\t\t{autoscroll}\n\t\t\t{layout}\n\t\t\t{placeholder}\n\t\t\t{examples}\n\t\t\t{_retryable}\n\t\t\t{_undoable}\n\t\t\tupload={(...args) => gradio.client.upload(...args)}\n\t\t\t_fetch={(...args) => gradio.client.fetch(...args)}\n\t\t\tload_component={gradio.load_component}\n\t\t\tmsg_format={type}\n\t\t\troot={gradio.root}\n\t\t\t{allow_file_downloads}\n\t\t/>\n\t</div>\n</Block>\n\n<style>\n\t.wrapper {\n\t\tdisplay: flex;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\talign-items: start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tflex-grow: 1;\n\t}\n\n\t:global(.progress-text) {\n\t\tright: auto;\n\t}\n</style>\n"], "names": ["insert", "target", "svg", "anchor", "append", "path0", "path1", "path2", "path3", "path", "format_chat_for_sharing", "chat", "message", "speaker_emoji", "html_content", "regexPatterns", "_", "regex", "match", "fileUrl", "newUrl", "uploadToHuggingFace", "url", "file_url", "redirect_src_url", "src", "root", "get_component_for_mime_type", "mime_type", "convert_file_message_to_component_message", "_file", "normalise_messages", "messages", "i", "normalise_tuples", "message_pair", "index", "role", "is_component_message", "is_last_bot_message", "all_messages", "is_bot", "last_index", "group_messages", "msg_format", "groupedMessages", "currentGroup", "currentRole", "load_components", "component_names", "_components", "load_component", "names", "components", "component_name", "name", "component", "get_components_from_messages", "switch_value", "ctx", "func", "dirty", "switch_instance_changes", "track", "type", "$$props", "value", "theme_mode", "props", "i18n", "upload", "_fetch", "allow_file_downloads", "display_icon_button_wrapper_top_corner", "div", "create_if_block", "button", "span0", "span1", "expanded", "title", "toggleExpanded", "ThumbDownActive", "ThumbDownDefault", "ThumbUpActive", "ThumbUpDefault", "iconbutton0_changes", "iconbutton1_changes", "handle_action", "selected", "$$invalidate", "Check", "Copy", "dispatch", "createEventDispatcher", "copied", "timer", "copy_feedback", "handle_copy", "textArea", "error", "onDestroy", "attr", "div_class_value", "current", "Retry", "Undo", "create_if_block_4", "create_if_block_3", "create_if_block_2", "create_if_block_1", "if_block", "is_all_text", "m", "all_text", "likeable", "show_retry", "show_undo", "show_copy_button", "position", "avatar", "generating", "layout", "copy_handler", "e", "click_handler", "click_handler_1", "message_text", "show_copy", "image_changes", "a", "a_href_value", "a_download_value", "div2", "div0", "div1", "set_data", "t1", "t1_value", "t3", "t3_value", "is_function", "markdown_changes", "button_aria_label_value", "get_message_label_data", "toggle_class", "if_block0", "create_if_block_6", "if_block1", "if_block2", "div2_class_value", "each_blocks", "avatar_img", "opposite_avatar_img", "render_markdown", "latex_delimiters", "sanitize_html", "selectable", "rtl", "line_breaks", "show_like", "scroll", "display_consecutive_in_same_bubble", "handle_select", "button_panel_props", "div6", "div5", "div4", "avatar_images", "div_aria_label_value", "src_url_equal", "video", "video_src_value", "each_value_1", "ensure_array_like", "create_if_block_5", "show_if", "create_if_block_9", "create_if_block_7", "show_if_1", "div0_aria_label_value", "span", "create_if_block_14", "examples", "placeholder", "handle_example_select", "example", "example_obj", "copy_conversation", "conversation_value", "err", "tick", "constants_0", "child_ctx", "constants_1", "constants_2", "Community", "iconbutton", "IconButton", "Trash", "div_1", "func_1", "message_changes", "t0_value", "t0", "ScrollDownArrow", "div_1_class_value", "null_to_empty", "old_value", "is_browser", "update_components", "pending_message", "show_share_button", "show_copy_all_button", "autoscroll", "_retryable", "_undoable", "like_user_message", "onMount", "show_scroll_button", "is_at_bottom", "scroll_to_bottom", "scroll_on_value_update", "handle_scroll", "handle_like", "val_", "message_group", "first", "last", "get_last_bot_options", "last_group", "formatted", "ShareError", "option", "example_select_handler", "$$value", "dequal", "options", "Cha<PERSON>", "blocklabel_changes", "chatbot_changes", "elem_id", "elem_classes", "visible", "scale", "min_width", "label", "show_label", "_selectable", "group_consecutive_messages", "gradio", "_value", "loading_status", "height", "resizeable", "min_height", "max_height", "clear_status_handler", "args", "change_handler"], "mappings": "q8DAAAA,GAgBKC,EAAAC,EAAAC,CAAA,EALJC,GAGCF,EAAAG,CAAA,EACDD,GAAyDF,EAAAI,CAAA,yzCCf1DN,GAqCKC,EAAAC,EAAAC,CAAA,EA5BJC,GAMOF,EAAAG,CAAA,EACPD,GAMOF,EAAAI,CAAA,EACPF,GAMOF,EAAAK,CAAA,EACPH,GAMOF,EAAAM,CAAA,wmBCpCRR,GAcKC,EAAAC,EAAAC,CAAA,EAPJC,GAMCF,EAAAO,CAAA,uGCCW,MAAAC,GAA0B,MACtCC,IAEe,MAAM,QAAQ,IAC5BA,EAAK,IAAI,MAAOC,GAAY,CAC3B,GAAIA,EAAQ,OAAS,SAAiB,MAAA,GACtC,IAAIC,EAAgBD,EAAQ,OAAS,OAAS,KAAO,KACjDE,EAAe,GAEf,GAAAF,EAAQ,OAAS,OAAQ,CAC5B,MAAMG,EAAgB,CACrB,MAAO,+BACP,MAAO,+BACP,MAAO,2DAAA,EAGRD,EAAeF,EAAQ,QAEvB,OAAS,CAACI,EAAGC,CAAK,IAAK,OAAO,QAAQF,CAAa,EAAG,CACjD,IAAAG,EAEJ,MAAQA,EAAQD,EAAM,KAAKL,EAAQ,OAAO,KAAO,MAAM,CACtD,MAAMO,EAAUD,EAAM,CAAC,GAAKA,EAAM,CAAC,EAC7BE,EAAS,MAAMC,GAAoBF,CAAc,EACxCL,EAAAA,EAAa,QAAQK,EAASC,CAAM,CACpD,CACD,CAAA,KACM,CACF,GAAA,CAACR,EAAQ,QAAQ,MAAc,MAAA,GAC7B,MAAAU,EACLV,EAAQ,QAAQ,YAAc,QAC3BA,EAAQ,QAAQ,OAAO,MAAM,KAC7BA,EAAQ,QAAQ,MACdW,EAAW,MAAMF,GAAoBC,CAAU,EACjDV,EAAQ,QAAQ,YAAc,QACjCE,EAAe,wBAAwBS,CAAQ,aACrCX,EAAQ,QAAQ,YAAc,QACzBE,EAAAS,EACLX,EAAQ,QAAQ,YAAc,UACxCE,EAAe,aAAaS,CAAQ,OAEtC,CAEO,MAAA,GAAGV,CAAa,KAAKC,CAAY,EAAA,CACxC,CAAA,GAEc,KAAK;AAAA,CAAI,EAQpBU,GAAmB,CAACC,EAAaC,IACtCD,EAAI,QAAQ,aAAc,QAAQC,CAAI,MAAM,EAE7C,SAASC,GACRC,EACS,CACT,OAAKA,EACDA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACpCA,EAAU,SAAS,OAAO,EAAU,QACjC,OAJgB,MAKxB,CAEA,SAASC,GACRjB,EACgB,CACV,MAAAkB,EAAQ,MAAM,QAAQlB,EAAQ,IAAI,EAAIA,EAAQ,KAAK,CAAC,EAAIA,EAAQ,KAC/D,MAAA,CACN,UAAWe,GAA4BG,GAAO,SAAS,EACvD,MAAOlB,EAAQ,KACf,SAAUA,EAAQ,SAClB,iBAAkB,CAAC,EACnB,MAAO,CAAC,CAAA,CAEV,CAEgB,SAAAmB,GACfC,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EACvBA,EAAS,IAAI,CAACpB,EAASqB,IACzB,OAAOrB,EAAQ,SAAY,SACvB,CACN,KAAMA,EAAQ,KACd,SAAUA,EAAQ,SAClB,QAASY,GAAiBZ,EAAQ,QAASc,CAAI,EAC/C,KAAM,OACN,MAAOO,EACP,QAASrB,EAAQ,OAAA,EAER,SAAUA,EAAQ,QACrB,CACN,QAASiB,GAA0CjB,EAAQ,OAAO,EAClE,SAAUA,EAAQ,SAClB,KAAMA,EAAQ,KACd,KAAM,YACN,MAAOqB,EACP,QAASrB,EAAQ,OAAA,EAGZ,CAAE,KAAM,YAAa,GAAGA,CAAQ,CACvC,CACF,CAEgB,SAAAsB,GACfF,EACAN,EAC6B,CAC7B,OAAIM,IAAa,KAAaA,EAClBA,EAAS,QAAQ,CAACG,EAAc,IACpCA,EAAa,IAAI,CAACvB,EAASwB,IAAU,CAC3C,GAAIxB,GAAW,KAAa,OAAA,KACtB,MAAAyB,EAAOD,GAAS,EAAI,OAAS,YAE/B,OAAA,OAAOxB,GAAY,SACf,CACN,KAAAyB,EACA,KAAM,OACN,QAASb,GAAiBZ,EAASc,CAAI,EACvC,SAAU,CAAE,MAAO,IAAK,EACxB,MAAO,CAAC,EAAGU,CAAK,CAAA,EAId,SAAUxB,EACN,CACN,QAASiB,GAA0CjB,CAAO,EAC1D,KAAAyB,EACA,KAAM,YACN,MAAO,CAAC,EAAGD,CAAK,CAAA,EAIX,CACN,KAAAC,EACA,QAASzB,EACT,KAAM,YACN,MAAO,CAAC,EAAGwB,CAAK,CAAA,CACjB,CACA,CACD,EACU,OAAQxB,GAAYA,GAAW,IAAI,CAC/C,CAEO,SAAS0B,GACf1B,EAC8B,CAC9B,OAAOA,EAAQ,OAAS,WACzB,CAEgB,SAAA2B,GACfP,EACAQ,EACU,CACV,MAAMC,EAAST,EAASA,EAAS,OAAS,CAAC,EAAE,OAAS,YAChDU,EAAaV,EAASA,EAAS,OAAS,CAAC,EAAE,MAMjD,OAFC,KAAK,UAAUU,CAAU,IACzB,KAAK,UAAUF,EAAaA,EAAa,OAAS,CAAC,EAAE,KAAK,GACzCC,CACnB,CAEgB,SAAAE,GACfX,EACAY,EACwB,CACxB,MAAMC,EAAyC,CAAA,EAC/C,IAAIC,EAAoC,CAAA,EACpCC,EAAkC,KAEtC,UAAWnC,KAAWoB,GACfpB,EAAQ,OAAS,aAAeA,EAAQ,OAAS,UAGnDA,EAAQ,OAASmC,EACpBD,EAAa,KAAKlC,CAAO,GAErBkC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAElCA,EAAe,CAAClC,CAAO,EACvBmC,EAAcnC,EAAQ,OAIpB,OAAAkC,EAAa,OAAS,GACzBD,EAAgB,KAAKC,CAAY,EAG3BD,CACR,CAEsB,eAAAG,GACrBC,EACAC,EACAC,EAC0D,CAC1D,IAAIC,EAAkB,CAAA,EAClBC,EAA+D,CAAA,EAEnD,OAAAJ,EAAA,QAASK,GAAmB,CAC3C,GAAIJ,EAAYI,CAAc,GAAKA,IAAmB,OACrD,OAGD,KAAM,CAAE,KAAAC,EAAM,UAAAC,CAAA,EAAcL,EAAeG,EAAgB,MAAM,EACjEF,EAAM,KAAKG,CAAI,EACfF,EAAW,KAAKG,CAAS,CACzB,CACA,GAE4C,MAAM,QAAQ,IAAIH,CAAU,GACvD,QAAQ,CAACG,EAAWvB,IAAM,CAC3CiB,EAAYE,EAAMnB,CAAC,CAAC,EAAIuB,EAAU,OAAA,CAClC,EAEMN,CACR,CAEO,SAASO,GACfzB,EACW,CACX,GAAI,CAACA,EAAU,MAAO,GAClB,IAAAqB,MAA8B,IACzB,OAAArB,EAAA,QAASpB,GAAY,CACzBA,EAAQ,OAAS,aACTyC,EAAA,IAAIzC,EAAQ,QAAQ,SAAS,CACzC,CACA,EACM,MAAM,KAAKyC,CAAU,CAC7B,kVCzKQ,IAAAK,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,2CAEO,qBAET,SAAQC,EAAA,2HANZ,GAAAC,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,oWAXf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,8CAEUA,EAAoB,CAAA,kLAJpC,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,yOAICA,EAAoB,CAAA,sNAnBpC,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,gCACX,SAAAA,KAAM,eACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,aACf,qBACO,2FAIGA,EAAoB,CAAA,kKARpC,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,sLACXE,EAAA,KAAAC,EAAA,SAAAH,KAAM,wBACTA,EAAK,CAAA,EAAC,OAASA,EAAK,CAAA,sIAMLA,EAAoB,CAAA,+KAtBpC,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,sDAET,qBACO,yCAGE,SAAUA,EAAK,CAAA,EAAC,QAAQ,2CAEvBA,EAAoB,CAAA,wKARpC,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,0PAMA,SAAUA,EAAK,CAAA,EAAC,0CAEfA,EAAoB,CAAA,kMAnBpC,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,uEAIN,cAAAA,KAAM,6CAEA,4HANf,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,yPAINE,EAAA,KAAAC,EAAA,cAAAH,KAAM,qJAnBf,IAAAD,EAAAC,KAAWA,EAAI,CAAA,CAAA,mGAGT,gDAIG,eACF,+BAEC,2HAVR,GAAAE,EAAA,GAAAH,KAAAA,EAAAC,KAAWA,EAAI,CAAA,CAAA,GAAA,yeAmDrB3D,GAAwBC,EAAA8D,EAAA5D,CAAA,oGArDrB,OAAAwD,OAAS,UAAS,EAebA,OAAS,OAAM,EAWfA,OAAS,QAAO,EAchBA,OAAS,QAAO,EAehBA,OAAS,QAAO,EAWhBA,OAAS,OAAM,sXA/Eb,GAAA,CAAA,KAAAK,CAAA,EAAAC,EACA,CAAA,WAAAZ,CAAA,EAAAY,EACA,CAAA,MAAAC,CAAA,EAAAD,EACA,CAAA,OAAAhE,CAAA,EAAAgE,EACA,CAAA,WAAAE,CAAA,EAAAF,EACA,CAAA,MAAAG,CAAA,EAAAH,EACA,CAAA,KAAAI,CAAA,EAAAJ,EACA,CAAA,OAAAK,CAAA,EAAAL,EACA,CAAA,OAAAM,CAAA,EAAAN,EACA,CAAA,qBAAAO,CAAA,EAAAP,GACA,uCAAAQ,EAAyC,EAAA,EAAAR,o0ECSnDjE,GAEKC,EAAAyE,EAAAvE,CAAA,iNAHDwD,EAAQ,CAAA,GAAAgB,GAAAhB,CAAA,0DARcA,EAAK,CAAA,CAAA,wJAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,kFAJ5D3D,GAeQC,EAAA2E,EAAAzE,CAAA,EAdPC,GAQKwE,EAAAF,CAAA,EAPJtE,GAAsCsE,EAAAG,CAAA,kBACtCzE,GAKMsE,EAAAI,CAAA,iDARsBnB,EAAc,CAAA,CAAA,kCAEhBA,EAAK,CAAA,CAAA,wBAEbA,EAAQ,CAAA,EAAG,YAAc,eAAe,EAMtDA,EAAQ,CAAA,kOAlBF,SAAAoB,EAAW,EAAA,EAAAd,EACX,CAAA,MAAAe,CAAA,EAAAf,EAEF,SAAAgB,GAAA,KACRF,EAAY,CAAAA,CAAA,mvCCLd/E,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAO,CAAA,w0CCVFT,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAO,CAAA,k+BCVFT,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAO,CAAA,o0CCVFT,GAWKC,EAAAC,EAAAC,CAAA,EAJJC,GAGCF,EAAAO,CAAA,qXCGK,KAAAkD,OAAa,UAAYuB,GAAkBC,GAC1C,MAAAxB,OAAa,UAAY,kBAAoB,UAC7C,MAAAA,OAAa,UACjB,sBACA,wEAQG,KAAAA,OAAa,OAASyB,GAAgBC,GACrC,MAAA1B,EAAa,CAAA,IAAA,OAAS,eAAiB,OACvC,MAAAA,OAAa,OACjB,sBACA,uKAhBGE,EAAA,IAAAyB,EAAA,KAAA3B,OAAa,UAAYuB,GAAkBC,IAC1CtB,EAAA,IAAAyB,EAAA,MAAA3B,OAAa,UAAY,kBAAoB,WAC7CE,EAAA,IAAAyB,EAAA,MAAA3B,OAAa,UACjB,sBACA,sDAQGE,EAAA,IAAA0B,EAAA,KAAA5B,OAAa,OAASyB,GAAgBC,IACrCxB,EAAA,IAAA0B,EAAA,MAAA5B,EAAa,CAAA,IAAA,OAAS,eAAiB,QACvCE,EAAA,IAAA0B,EAAA,MAAA5B,OAAa,OACjB,sBACA,0MAtBQ,GAAA,CAAA,cAAA6B,CAAA,EAAAvB,EAEPwB,EAAsC,kBAUzCC,EAAA,EAAAD,EAAW,SAAS,EACpBD,EAAcC,CAAQ,UAWtBC,EAAA,EAAAD,EAAW,MAAM,EACjBD,EAAcC,CAAQ,wdC/Be,EAAA,OAAA,wCACZ,EAAA,OAAA,gFAsDnB9B,EAAM,CAAA,EAAG,iBAAmB,oBAC7BA,EAAM,CAAA,EAAGgC,GAAQC,oBAFbjC,EAAW,CAAA,CAAA,mFACdA,EAAM,CAAA,EAAG,iBAAmB,6BAC7BA,EAAM,CAAA,EAAGgC,GAAQC,0HAnDjBC,EAAWC,SAKbC,EAAS,GACF,CAAA,MAAA7B,CAAA,EAAAD,EACP+B,EAEK,SAAAC,GAAA,KACRF,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGW,eAAAG,GAAA,IACV,cAAe,UAClBL,EAAS,OAAU,CAAA,MAAA3B,CAAA,CAAA,QACb,UAAU,UAAU,UAAUA,CAAK,EACzC+B,eAEME,EAAW,SAAS,cAAc,UAAU,EAClDA,EAAS,MAAQjC,EAEjBiC,EAAS,MAAM,SAAW,WAC1BA,EAAS,MAAM,KAAO,YAEtB,SAAS,KAAK,QAAQA,CAAQ,EAC9BA,EAAS,OAAA,MAGR,SAAS,YAAY,MAAM,EAC3BF,GACQ,OAAAG,EAAA,CACR,QAAQ,MAAMA,CAAK,UAEnBD,EAAS,OAAA,IAKZE,OAAAA,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,2hBCIC,gFAHNM,GAAA5B,EAAA,QAAA6B,EAAA,mBAAA5C,SAAWA,EAAM,CAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,gBAAA,UAFvB3D,GA+BKC,EAAAyE,EAAAvE,CAAA,wFA9BoB,CAAAqG,GAAA3C,EAAA,KAAA0C,KAAAA,EAAA,mBAAA5C,SAAWA,EAAM,CAAA,EAAA,qBAAmBA,EAC3D,CAAA,IAAA,MAAQ,eAAa,iLAKZA,EAAY,EAAA,CAAA,0GAAZA,EAAY,EAAA,kJAMb8C,0BAGI9C,EAAU,CAAA,6GAAVA,EAAU,CAAA,+JAMd+C,YAEI/C,EAAU,CAAA,6GAAVA,EAAU,CAAA,2XAnBjBA,EAAS,CAAA,GAAAgD,GAAAhD,CAAA,IAMTA,EAAU,CAAA,GAAAiD,GAAAjD,CAAA,IAQVA,EAAS,CAAA,GAAAkD,GAAAlD,CAAA,IAQTA,EAAQ,CAAA,GAAAmD,GAAAnD,CAAA,0LAtBRA,EAAS,CAAA,6GAMTA,EAAU,CAAA,2GAQVA,EAAS,CAAA,2GAQTA,EAAQ,CAAA,kRA5BXoD,GAAApD,EAAa,CAAA,GAAAA,EAAc,CAAA,GAAAA,MAAaA,EAAQ,CAAA,IAAAgB,GAAAhB,CAAA,0EAAhDA,EAAa,CAAA,GAAAA,EAAc,CAAA,GAAAA,MAAaA,EAAQ,CAAA,sLA1B3CqD,GACRpG,EAAAA,CAGE,OAAA,MAAM,QAAQA,CAAO,GACrBA,EAAQ,MAAOqG,GAAa,OAAAA,EAAE,SAAY,QAAQ,IACjD,MAAM,QAAQrG,CAAO,GAAA,OAAYA,EAAQ,SAAY,kBAIhDsG,GAAStG,EAAAA,CACb,OAAA,MAAM,QAAQA,CAAO,EACjBA,EAAQ,IAAKqG,GAAMA,EAAE,OAAO,EAAE,KAAK;AAAA,CAAI,EAExCrG,EAAQ,mCA3BL,CAAA,SAAAuG,CAAA,EAAAlD,EACA,CAAA,WAAAmD,CAAA,EAAAnD,EACA,CAAA,UAAAoD,CAAA,EAAApD,EACA,CAAA,iBAAAqD,CAAA,EAAArD,EACA,CAAA,QAAArD,CAAA,EAAAqD,EACA,CAAA,SAAAsD,CAAA,EAAAtD,EACA,CAAA,OAAAuD,CAAA,EAAAvD,EACA,CAAA,WAAAwD,CAAA,EAAAxD,EAEA,CAAA,cAAAuB,CAAA,EAAAvB,EACA,CAAA,OAAAyD,CAAA,EAAAzD,EACA,CAAA,SAAA4B,CAAA,EAAA5B,EAqCG,MAAA0D,EAAAC,GAAM/B,EAAS,OAAQ+B,EAAE,MAAM,EAOzBC,EAAA,IAAArC,EAAc,OAAO,EAQrBsC,EAAA,IAAAtC,EAAc,MAAM,0cAjCxCE,EAAA,GAAGqC,EAAef,GAAYpG,CAAO,EAAIsG,GAAStG,CAAO,EAAI,EAAA,mBAE7D8E,EAAA,EAAGsC,EAAYV,GAAoB1G,GAAWoG,GAAYpG,CAAO,CAAA,mBAC9D,CACD,MAAM,QAAQA,CAAO,GACtB0B,GAAqB1B,CAAO,GAC5BA,EAAQ,QAAQ,OAAO,2wDCiEW,IAAA+C,MAAY,QAAWA,EAAI,CAAA,EAAA,sGAD7D3D,GAEKC,EAAAyE,EAAAvE,CAAA,uCAD6B0D,EAAA,CAAA,EAAA,IAAAoE,EAAA,IAAAtE,MAAY,oBAAWA,EAAI,CAAA,EAAA,iJAsGlDA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,gBAKPA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,4SAtBP2C,EAAA4B,EAAA,OAAAC,EAAAxE,EAAQ,EAAA,EAAA,QAAQ,MAAM,GAAG,yBAErB2C,EAAA4B,EAAA,WAAAE,EAAA,OAAO,aACd,KACAzE,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,MAAM,uIAdX3D,GAiCKC,EAAAoI,EAAAlI,CAAA,EAhCJC,EAEKiI,EAAAC,CAAA,sBACLlI,EA4BKiI,EAAAE,CAAA,EA3BJnI,EAgBGmI,EAAAL,CAAA,EALF9H,EAIA8H,EAAArD,CAAA,gBAEDzE,EASAmI,EAAAzD,CAAA,6CAdInB,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,QAAM,KAAA6E,GAAAC,EAAAC,CAAA,GAXF,CAAAlC,GAAA3C,EAAA,CAAA,EAAA,IAAAsE,KAAAA,EAAAxE,EAAQ,EAAA,EAAA,QAAQ,MAAM,sBAElB,CAAA6C,GAAA3C,EAAA,CAAA,EAAA,IAAAuE,KAAAA,EAAA,OAAO,aACd,KACAzE,EAAO,EAAA,EAAC,QAAQ,OAAO,WACxBA,EAAQ,EAAA,EAAA,QAAQ,OAAO,KAAK,MAAM,GAAG,EAAE,IAAG,GAC1C,mDAUDA,EAAO,EAAA,EAAC,QAAQ,OAAO,WACvBA,EAAO,EAAA,EAAC,QAAQ,OAAO,MACvB,IAEC,MAAM,GAAG,EACT,IAAG,EACH,YAAW,EAAA,KAAA6E,GAAAG,EAAAC,CAAA,6KA3CRjF,EAAO,EAAA,EAAC,QAAQ,WACjBA,EAAO,EAAA,EAAC,QAAQ,qBACVA,EAAW,EAAA,QAChBA,EAAO,EAAA,EAAC,QAAQ,6CACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,2OAL5BA,EAAO,EAAA,EAAC,QAAQ,wBACjBA,EAAO,EAAA,EAAC,QAAQ,sCACVA,EAAW,EAAA,qBAChBA,EAAO,EAAA,EAAC,QAAQ,+DACiBA,EAAa,EAAA,EAAG,GACvDA,EAAkC,EAAA,qSApC9BA,EAAO,EAAA,GAAE,UAAU,MAAK,6GAD9B3D,GA2BKC,EAAAyE,EAAAvE,CAAA,iRATO,QAAAwD,MAAQ,8HAKRkF,GAAAlF,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,yEALNE,EAAA,CAAA,EAAA,KAAAiF,EAAA,QAAAnF,MAAQ,8TAfVA,EAAO,EAAA,EAAC,SAAS,eACdpB,GAAmB,CAAEoB,EAAO,EAAA,CAAA,EAAGA,EAAK,CAAA,CAAA,+HADvCA,EAAO,EAAA,EAAC,SAAS,4BACdpB,GAAmB,CAAEoB,EAAO,EAAA,CAAA,EAAGA,EAAK,CAAA,CAAA,gMAGpC,QAAAA,MAAQ,8HAKRkF,GAAAlF,QAAAA,EAAM,EAAA,EAAA,MAAA,KAAA,SAAA,yEALNE,EAAA,CAAA,EAAA,KAAAiF,EAAA,QAAAnF,MAAQ,iWARjBA,EAAO,EAAA,EAAC,OAAS,OAAM,EA6BlBA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,aAAaA,EAAW,EAAA,EAAA,EAgBxEA,EAAO,EAAA,EAAC,OAAS,aAAeA,EAAO,EAAA,EAAC,QAAQ,YAAc,OAAM,kLA7DjEA,EAAI,CAAA,CAAA,cAaZA,EAAG,EAAA,EAAG,MAAQ,KAAK,EACZ2C,EAAA1B,EAAA,aAAAmE,EAAApF,EAAO,CAAA,EAAA,eAAiBqF,GAAuBrF,EAAO,EAAA,CAAA,CAAA,gCAbpDsF,EAAArE,EAAA,SAAAjB,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,mCACFA,EAAe,CAAA,CAAA,kEAGnCA,EAAU,CAAA,EAAG,UAAY,SAAS,oBAC9BA,EAAG,EAAA,EAAG,QAAU,MAAM,4BAfxBA,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,iBAAA,yBACvC,EAAI,mCACMA,EAAe,CAAA,CAAA,kBAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,aACjCrB,GAAqBqB,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,EACtBsF,EAAAvE,EAAA,UAAAf,MAAgB,CAAC,UAPjC3D,GA4GKC,EAAAyE,EAAAvE,CAAA,EAnGJC,EAkGQsE,EAAAE,CAAA,uRAjGMjB,EAAI,CAAA,CAAA,yBAaZA,EAAG,EAAA,EAAG,MAAQ,uBACP,CAAA6C,GAAA3C,EAAA,CAAA,EAAA,IAAAkF,KAAAA,EAAApF,EAAO,CAAA,EAAA,eAAiBqF,GAAuBrF,EAAO,EAAA,CAAA,6CAbpDsF,EAAArE,EAAA,SAAAjB,EAAM,EAAA,IAAAA,EAAM,CAAA,EAAA,OAAS,CAAC,kDACFA,EAAe,CAAA,CAAA,iEAGnCA,EAAU,CAAA,EAAG,UAAY,SAAS,+BAC9BA,EAAG,EAAA,EAAG,QAAU,MAAM,wCAfxBA,EAAkC,EAAA,EAAU,GAAPA,EAAI,CAAA,GAAK,+EACvC,EAAI,uDACMA,EAAe,CAAA,CAAA,sCAChCA,EAAO,EAAA,EAAC,OAAS,WAAW,iCACjCrB,GAAqBqB,EAChC,EAAA,CAAA,GAAAA,MAAQ,QAAQ,YAAc,MAAM,sBACtBsF,EAAAvE,EAAA,UAAAf,MAAgB,CAAC,gHA0G7BA,EAAkB,EAAA,CAAA,wLAAlBA,EAAkB,EAAA,CAAA,CAAA,CAAA,gIAQRA,EAAkB,EAAA,CAAA,oKAAlBA,EAAkB,EAAA,CAAA,CAAA,CAAA,uIAxI9BuF,EAAAvF,OAAe,MAAIwF,GAAAxF,CAAA,OAcfA,EAAQ,CAAA,CAAA,uBAAb,OAAI1B,GAAA,4DAgHF,IAAAmH,EAAAzF,OAAW,SAAOmD,GAAAnD,CAAA,EASpB0F,EAAA1F,OAAW,UAAQgB,GAAAhB,CAAA,2JA3HdA,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,iBAAA,gBADtCA,EAAkC,EAAA,CAAA,2DAH5BsF,EAAAV,EAAA,iBAAA5E,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,EAZnC2C,EAAA+B,EAAA,QAAAiB,EAAA,eAAA3F,SAASA,EAAI,CAAA,EAAA,qBAAA,EACdsF,EAAAZ,EAAA,cAAA1E,OAAe,IAAI,EACVsF,EAAAZ,EAAA,uBAAA1E,OAAwB,IAAI,UAHzD3D,GA0IKC,EAAAoI,EAAAlI,CAAA,wBAhIJC,EA+HKiI,EAAAE,CAAA,EA1HJnI,EAmHKmI,EAAAD,CAAA,yHA7HD3E,OAAe,0HAcXA,EAAQ,CAAA,CAAA,oBAAb,OAAI1B,GAAA,EAAA,4GAAJ,OAAIA,EAAAsH,EAAA,OAAAtH,GAAA,yCAFC0B,EAAkC,EAAA,EAAGA,EAAI,CAAA,EAAG,EAAE,EAAA,sEADtCA,EAAkC,EAAA,CAAA,EAmH7CA,OAAW,2JAtHMsF,EAAAV,EAAA,iBAAA5E,EAAS,CAAA,EAAA,CAAC,EAAE,OAAS,WAAW,GAZnC,CAAA6C,GAAA3C,EAAA,CAAA,EAAA,IAAAyF,KAAAA,EAAA,eAAA3F,SAASA,EAAI,CAAA,EAAA,uDACdsF,EAAAZ,EAAA,cAAA1E,OAAe,IAAI,iBACVsF,EAAAZ,EAAA,uBAAA1E,OAAwB,IAAI,EAyIpDA,OAAW,wJAzHX,OAAI1B,GAAA,wLAnEC+G,GAAuBpI,EAAA,CAC3B,OAAAA,EAAQ,OAAS,OACbA,EAAQ,QAEfA,EAAQ,OAAS,aACjBA,EAAQ,QAAQ,YAAc,OAE1B,MAAM,QAAQA,EAAQ,QAAQ,KAAK,EACJ,2BAAAA,EAAQ,QAAQ,MAAM,CAAC,EAAE,WAAW,MAAM,GAAG,EAAE,IAAA,CAAA,8BAGtDA,EAAQ,QAAQ,OAAO,WAAW,MAAM,GAAG,EAAE,IACvE,CAAA,IAAAA,EAAQ,QAAQ,OAAO,WAAa,2BAGTA,EAAQ,QAAQ,WAAa,SAAS,sBA1D1D,GAAA,CAAA,MAAAsD,CAAA,EAAAD,EACA,CAAA,WAAAuF,CAAA,EAAAvF,GACA,oBAAAwF,EAAuC,IAAA,EAAAxF,GACvC,KAAA5B,EAAO,MAAA,EAAA4B,EACP,CAAA,SAAAjC,EAAA,EAAA,EAAAiC,EACA,CAAA,OAAAyD,CAAA,EAAAzD,EACA,CAAA,gBAAAyF,CAAA,EAAAzF,EACA,CAAA,iBAAA0F,CAAA,EAAA1F,EAKA,CAAA,cAAA2F,CAAA,EAAA3F,EACA,CAAA,WAAA4F,CAAA,EAAA5F,EACA,CAAA,OAAAM,CAAA,EAAAN,EACA,CAAA,IAAA6F,CAAA,EAAA7F,EACA,CAAA,SAAA4B,CAAA,EAAA5B,EACA,CAAA,KAAAI,CAAA,EAAAJ,EACA,CAAA,YAAA8F,CAAA,EAAA9F,EACA,CAAA,OAAAK,CAAA,EAAAL,EACA,CAAA,OAAAhE,CAAA,EAAAgE,EACA,CAAA,KAAAvC,CAAA,EAAAuC,EACA,CAAA,WAAAE,CAAA,EAAAF,EACA,CAAA,YAAAf,EAAA,EAAAe,EACA,CAAA,EAAAhC,EAAA,EAAAgC,EACA,CAAA,iBAAAqD,EAAA,EAAArD,EACA,CAAA,WAAAwD,EAAA,EAAAxD,EACA,CAAA,UAAA+F,EAAA,EAAA/F,EACA,CAAA,WAAAmD,EAAA,EAAAnD,EACA,CAAA,UAAAoD,CAAA,EAAApD,EACA,CAAA,WAAArB,EAAA,EAAAqB,EACA,CAAA,cAAAuB,EAAA,EAAAvB,EACA,CAAA,OAAAgG,EAAA,EAAAhG,EACA,CAAA,qBAAAO,EAAA,EAAAP,EACA,CAAA,mCAAAiG,EAAA,EAAAjG,EAEF,SAAAkG,GAAclI,EAAWrB,GAAA,CACjCiF,EAAS,SAAA,CACR,MAAOjF,GAAQ,MACf,MAAOA,GAAQ,UAoCb,IAAAwJ,gBAwGkBH,WAnDDE,GAAclI,GAAGrB,CAAO,QAC3BgH,KAAC,CACTA,GAAE,MAAQ,SACbuC,GAAclI,GAAGrB,CAAO,GA8FlB+G,GAAAC,GAAM/B,EAAS,OAAQ+B,EAAE,MAAM,ixCArJzClC,EAAA,GAAA0E,GAAA,CACF,cAAA5E,GACA,SAAUwE,GACV,WAAA5C,GACA,UAAAC,EACA,WAAAI,GACA,iBAAAH,GACA,QAAS1E,KAAe,SAAWZ,EAAS,CAAC,EAAIA,EACjD,SAAUK,IAAS,OAAS,QAAU,OACtC,OAAQmH,EACR,OAAA9B,EACA,SAAA7B,opHCzFkClC,EAAa,CAAA,EAAC,CAAC,EAAE,mHADnD3D,GAEKC,EAAAyE,EAAAvE,CAAA,mDAD6BwD,EAAa,CAAA,EAAC,CAAC,EAAE,0IAF/CA,EAAa,CAAA,EAAC,CAAC,IAAM,MAAIgB,GAAAhB,CAAA,6WAODA,EAAM,CAAA,EAAA,iBAAA,4GACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,8BAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,mDAVvD3D,GAwBKC,EAAAoK,EAAAlK,CAAA,yBAjBJC,GAgBKiK,EAAAC,CAAA,EARJlK,GAOKkK,EAAAC,CAAA,iBArBD5G,EAAa,CAAA,EAAC,CAAC,IAAM,8IAOGA,EAAM,CAAA,EAAA,mEACfA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,yCAChBA,EAAa,CAAA,EAAC,CAAC,IAAM,IAAI,0FAd3C,OAAA+D,EAAS,QAAA,EAAAzD,EACT,CAAA,cAAAuG,EAAA,CAAqD,KAAM,IAAI,CAAA,EAAAvG,+vBCApC,EAAA,OAAA,mNAiCjBN,EAAW,CAAA,wHAD/B3D,EAEKC,EAAAyE,EAAAvE,CAAA,uDADewD,EAAW,CAAA,kLAKvBA,EAAQ,CAAA,CAAA,uBAAb,OAAI1B,GAAA,wLADPjC,EAoIKC,EAAAyE,EAAAvE,CAAA,8EAnIGwD,EAAQ,CAAA,CAAA,oBAAb,OAAI1B,GAAA,EAAA,4GAAJ,OAAIA,EAAAsH,EAAA,OAAAtH,GAAA,0CAAJ,OAAIA,GAAA,0LAwBG,iDAAA0B,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,iBAkEnBA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,sBAQ5CA,EAAO,CAAA,EAAC,MAAM,CAAC,EAAE,WAAW,SAAS,OAAO,8eAtFtD3D,EAEKC,EAAAyE,EAAAvE,CAAA,4GAPEwD,EAAO,CAAA,EAAC,KAAK,yHAHpB3D,EAMKC,EAAAyE,EAAAvE,CAAA,mDAHEwD,EAAO,CAAA,EAAC,KAAK,iOAqGG2C,EAAA5B,EAAA,aAAA+F,EAAA,SAAA9G,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhD3D,EAKKC,EAAAyE,EAAAvE,CAAA,6BAHiB,CAAAqG,GAAA3C,EAAA,GAAA4G,KAAAA,EAAA,SAAA9G,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,4OAP1B2C,EAAA5B,EAAA,aAAA+F,EAAA,SAAA9G,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,EAAA,UAFhD3D,EAKKC,EAAAyE,EAAAvE,CAAA,6BAHiB,CAAAqG,GAAA3C,EAAA,GAAA4G,KAAAA,EAAA,SAAA9G,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,SAAS,0MAPzC+G,GAAAC,EAAA,IAAAC,EAAAjH,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,GAAA2C,EAAAqE,EAAA,MAAAC,CAAA,yFAH3B5K,EAMKC,EAAAyE,EAAAvE,CAAA,EALJC,EAICsE,EAAAiG,CAAA,UAFK9G,EAAA,GAAA,CAAA6G,GAAAC,EAAA,IAAAC,EAAAjH,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,GAAG,iHARpB,IAAAA,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,IACjB,IAAAA,KAAQ,MAAM,CAAC,EAAE,WAAa,kHAJrC3D,EAMKC,EAAAyE,EAAAvE,CAAA,uCAHE0D,EAAA,IAAAoE,EAAA,IAAAtE,EAAQ,CAAA,EAAA,MAAM,CAAC,EAAE,KACjBE,EAAA,IAAAoE,EAAA,IAAAtE,KAAQ,MAAM,CAAC,EAAE,WAAa,2IAjE7BkH,EAAAC,GAAAnH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,uBAA7B,OAAI1B,GAAA,4DAgDD,IAAA8E,EAAApD,EAAQ,CAAA,EAAA,MAAM,OAAS,GAACoH,GAAApH,CAAA,iMArD9B3D,EAgEKC,EAAAyE,EAAAvE,CAAA,8FA3DG0K,EAAAC,GAAAnH,KAAQ,MAAM,MAAM,EAAG,CAAC,CAAA,oBAA7B,OAAI1B,GAAA,EAAA,yGAAJ,OAAIA,EAAAsH,EAAA,OAAAtH,GAAA,YAgDD0B,EAAQ,CAAA,EAAA,MAAM,OAAS,yFAhD1B,OAAI1B,GAAA,6MAwCE+I,GAAA,OAAAA,EAAA,CAAA,CAAArH,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,yGAFhB2C,EAAA5B,EAAA,aAAA+F,EAAA,SAAA9G,MAAK,SAAS,EAAA,UAFpC3D,EASKC,EAAAyE,EAAAvE,CAAA,mJAPiB,CAAAqG,GAAA3C,EAAA,GAAA4G,KAAAA,EAAA,SAAA9G,MAAK,SAAS,qHAb9BA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAACsH,GAAAtH,CAAA,kFAHlC+G,GAAAC,EAAA,IAAAC,EAAAjH,MAAK,GAAG,GAAA2C,EAAAqE,EAAA,MAAAC,CAAA,yFAHf5K,EAeKC,EAAAyE,EAAAvE,CAAA,EAdJC,EAICsE,EAAAiG,CAAA,gCAFK9G,EAAA,GAAA,CAAA6G,GAAAC,EAAA,IAAAC,EAAAjH,MAAK,GAAG,gBAGTA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,iKApBjC,IAAAA,MAAK,IACL,IAAAA,EAAK,EAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,YAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,GAACuH,GAAAvH,CAAA,uHANzC3D,EAeKC,EAAAyE,EAAAvE,CAAA,6DAZE0D,EAAA,IAAAoE,EAAA,IAAAtE,MAAK,KACLE,EAAA,IAAAoE,EAAA,IAAAtE,EAAK,EAAA,EAAA,WAA8B,iBAAAA,KAAI,CAAC,cAEzCA,EAAC,CAAA,IAAK,GAAKA,KAAQ,MAAM,OAAS,ugBAuBnC+E,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,kCAD3B,GACE,0EAFc2C,EAAA5B,EAAA,aAAA+F,EAAA,GAAA9G,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxC3D,EAMKC,EAAAyE,EAAAvE,CAAA,wBADF0D,EAAA,GAAA6E,KAAAA,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAA6E,GAAAC,EAAAC,CAAA,EAFX7E,EAAA,GAAA4G,KAAAA,EAAA,GAAA9G,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,2EAfrC+E,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,kCAD3B,GACE,0EAFc2C,EAAA5B,EAAA,aAAA+F,EAAA,GAAA9G,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,UAHxC3D,EAMKC,EAAAyE,EAAAvE,CAAA,wBADF0D,EAAA,GAAA6E,KAAAA,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAA6E,GAAAC,EAAAC,CAAA,EAFX7E,EAAA,GAAA4G,KAAAA,EAAA,GAAA9G,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,8JAXtCqH,GAAA,OAAAA,EAAA,CAAA,CAAArH,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,QAiB3BwH,GAAA,OAAAA,EAAA,CAAA,CAAAxH,EAAK,EAAA,EAAA,WAAW,SAAS,OAAO,6UAqCtC+E,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,6CAD3B,GACE,yEAFc2C,EAAAgC,EAAA,aAAA8C,EAAA,GAAAzH,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,aAAA,oDAJzC3D,EAQKC,EAAAsI,EAAApI,CAAA,EAPJC,EAMKmI,EAAAD,CAAA,wBADFzE,EAAA,GAAA6E,KAAAA,EAAA/E,EAAQ,CAAA,EAAA,MAAM,OAAS,EAAC,KAAA6E,GAAAC,EAAAC,CAAA,EAFX7E,EAAA,GAAAuH,KAAAA,EAAA,GAAAzH,EAAQ,CAAA,EAAA,MAAM,OAAS,CAAC,qFA0CzC+E,GAAA/E,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,8DAlHnCA,EAAO,CAAA,GAAE,MAAM,IAAG,EAQbA,EAAS,CAAA,GAAA,MAAM,YAAc,OAAM,EAInCA,EAAO,CAAA,EAAC,QAAU,QAAaA,EAAO,CAAA,EAAC,MAAM,OAAS,EAAC,kXAfpCA,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,EAAA,UAP7E3D,EAgIQC,EAAA2E,EAAAzE,CAAA,EAvHPC,EAsHKwE,EAAA2D,CAAA,4BALJnI,EAIKmI,EAAAD,CAAA,EAHJlI,EAEAkI,EAAA+C,CAAA,qNADG,CAAA7E,GAAA3C,EAAA,IAAA6E,KAAAA,GAAA/E,EAAQ,CAAA,EAAA,cAAgBA,KAAQ,MAAI,KAAA6E,GAAAC,EAAAC,CAAA,qCArHX/E,EAAC,CAAA,EAAG,CAAC,KAAKA,KAAQ,cAAgBA,EAAO,CAAA,EAAC,IAAI,8HAf3EuF,EAAAvF,OAAgB,MAAI2H,GAAA3H,CAAA,EAKpByF,EAAAzF,OAAa,MAAIgB,GAAAhB,CAAA,sIANvB3D,EA6IKC,EAAAyE,EAAAvE,CAAA,sDA5ICwD,OAAgB,mGAKhBA,OAAa,8MAhCP,SAAA4H,EAAoC,IAAA,EAAAtH,GACpC,YAAAuH,EAA6B,IAAA,EAAAvH,EAC7B,CAAA,iBAAA0F,CAAA,EAAA1F,EAKA,CAAA,KAAAvC,CAAA,EAAAuC,QAEL4B,EAAWC,KAIR,SAAA2F,EACRxJ,EACAyJ,EAAA,CAEM,MAAAC,EAAA,OACED,GAAY,UAAa,KAAMA,CAAA,EAAYA,EACnD7F,EAAS,iBAAA,CACR,MAAO5D,EACP,MAAA,CAAS,KAAM0J,EAAY,KAAM,MAAOA,EAAY,SAiBjD,MAAA9D,EAAA,CAAA5F,EAAAyJ,IAAAD,EACCxJ,EACO,OAAAyJ,GAAY,SAAa,CAAA,KAAMA,CAAO,EAAKA,CAAA,21BChD9B,EAAA,OAAA,+EAgDpB/H,EAAM,CAAA,EAAGgC,GAAQC,SAEhBjC,EAAM,CAAA,EAAG,sBAAwB,qCAD9BA,EAAW,CAAA,CAAA,kFADfA,EAAM,CAAA,EAAGgC,GAAQC,kBAEhBjC,EAAM,CAAA,EAAG,sBAAwB,yIA7CpCoC,EAAS,GACF,CAAA,MAAA7B,CAAA,EAAAD,EAEP+B,EAEK,SAAAC,GAAA,KACRF,EAAS,EAAA,EACLC,GAAO,aAAaA,CAAK,EAC7BA,EAAQ,oBACPD,EAAS,EAAA,GACP,KAGE,MAAA6F,EAAA,IAAA,CACD,GAAA1H,EAAA,CACG,MAAA2H,EAAqB3H,EACzB,IAAKtD,GACDA,EAAQ,OAAS,UACVA,EAAQ,IAAI,KAAKA,EAAQ,OAAO,GAEjC,GAAAA,EAAQ,IAAI,KAAKA,EAAQ,QAAQ,MAAM,GAAG,EAEpD,EAAA,KAAK;AAAA;AAAA,CAAM,EAEb,UAAU,UAAU,UAAUiL,CAAkB,EAAE,MAAOC,GAAA,CACxD,QAAQ,MAAM,gCAAiCA,CAAG,MAKtC,eAAA5F,GAAA,CACV,cAAe,YAClB0F,IACA3F,KAIF,OAAAI,GAAA,IAAA,CACKL,GAAO,aAAaA,CAAK,4mBC3B7B,CAAA,sBAAAF,GAAA,KAAAiG,aAKM,EAAA,OAAA,oJA8PW,MAAAC,EAAAC,EAAS,EAAA,EAAA,CAAC,EAAE,OAAS,OAAS,OAAS,cACjC,MAAAC,EAAAD,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,UAC5B,MAAAE,EAAAF,MAAcA,EAAI,EAAA,IAAK,OAAS,EAAI,CAAC,0WApC7DG,EAAS,CAAA,CAAA,0aAFZzI,EAAiB,CAAA,GAAAwF,GAAAxF,CAAA,EAkBJ0I,EAAA,IAAAC,GAAA,CAAA,MAAA,CAAA,KAAAC,SAAiD,OAAO,CAAA,CAAA,6BAErE5I,EAAoB,EAAA,GAAAoH,GAAApH,CAAA,2JApBpBA,EAAiB,CAAA,gHAoBjBA,EAAoB,EAAA,4tBAejBA,EAAe,EAAA,CAAA,uBAApB,OAAI1B,GAAA,kGAuCD0B,EAAe,CAAA,EAAA,EAEVA,EAAO,EAAA,EAAA,iKA1ClB3D,GA0DKC,EAAAuM,EAAArM,CAAA,4JAzDGwD,EAAe,EAAA,CAAA,oBAApB,OAAI1B,GAAA,EAAA,yGAAJ,OAAIA,EAAAsH,EAAA,OAAAtH,GAAA,mMAAJ,OAAIA,GAAA,mnBA6BO,UAAA0B,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,EACzD,WAAAA,EAAc,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,EAClD,UAAAA,EAAa,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,yCAGnD,OAAAA,MAAa,OAAM8I,40BALhB5I,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,IAAA6I,EAAA,UAAA/I,QAAS,OAASA,MAAYA,EAAiB,EAAA,EAAGA,EAAQ,CAAA,GACzDE,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,IAAA6I,EAAA,WAAA/I,EAAc,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,GAClDE,EAAA,CAAA,EAAA,SAAAA,EAAA,CAAA,EAAA,IAAA6I,EAAA,UAAA/I,EAAa,EAAA,GAAApB,GAAoBoB,MAAUA,EAAK,CAAA,CAAA,gOAYpDA,EAAO,EAAA,CAAA,uBAAZ,OAAI,GAAA,gIADP3D,GAaKC,EAAAuM,EAAArM,CAAA,4EAZGwD,EAAO,EAAA,CAAA,oBAAZ,OAAI1B,GAAA,EAAA,mHAAJ,gXASC0K,GAAAhJ,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,yIAR9B3D,GASQC,EAAA2E,EAAAzE,CAAA,0DADN0D,EAAA,CAAA,EAAA,GAAA8I,KAAAA,GAAAhJ,EAAO,EAAA,EAAA,OAASA,MAAO,OAAK,KAAA6E,GAAAoE,EAAAD,CAAA,kFAoB3BE,qDAGIlJ,EAAgB,EAAA,CAAA,yGAL5B3D,GAOKC,EAAAuM,EAAArM,CAAA,oJAlHD+I,EAAAvF,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,GAACgD,GAAAhD,CAAA,8CAmCjCA,EAAK,CAAA,IAAK,MAAQA,EAAK,CAAA,EAAC,OAAS,GAAKA,EAAe,EAAA,IAAK,KAAI,gCAuE/DA,EAAkB,EAAA,GAAAgB,GAAAhB,CAAA,sEA7Ef2C,GAAAkG,EAAA,QAAAM,EAAAC,GAAApJ,QAAW,SAAW,cAAgB,YAAY,EAAA,iBAAA,wHAD1D3D,GA4EKC,EAAAuM,EAAArM,CAAA,uEAxGAwD,OAAU,MAAQA,EAAM,CAAA,EAAA,OAAS,oPA6B9B,CAAA6C,GAAA3C,EAAA,CAAA,EAAA,QAAAiJ,KAAAA,EAAAC,GAAApJ,QAAW,SAAW,cAAgB,YAAY,EAAA,qCA6ErDA,EAAkB,EAAA,gSAlTX,CAAA,MAAAO,EAAA,EAAA,EAAAD,EACP+I,EAAwC,KAIjC,CAAA,OAAAzI,CAAA,EAAAN,EACA,CAAA,eAAAd,CAAA,EAAAc,EACA,CAAA,qBAAAO,CAAA,EAAAP,EACA,CAAA,mCAAAiG,CAAA,EAAAjG,EAEPf,EAAA,CAAA,EAEE,MAAA+J,EAAA,OAAoB,OAAW,IAEtB,eAAAC,GAAA,CACdxH,EAAA,GAAAxC,EAAA,MAAoBF,GACnBS,GAA6BS,CAAK,EAClChB,EACAC,CAAA,CAAA,EAMS,GAAA,CAAA,iBAAAwG,CAAA,EAAA1F,GAKA,gBAAAkJ,EAAkB,EAAA,EAAAlJ,GAClB,WAAAwD,EAAa,EAAA,EAAAxD,GACb,WAAA4F,EAAa,EAAA,EAAA5F,GACb,SAAAkD,EAAW,EAAA,EAAAlD,GACX,kBAAAmJ,EAAoB,EAAA,EAAAnJ,GACpB,qBAAAoJ,EAAuB,EAAA,EAAApJ,GACvB,IAAA6F,EAAM,EAAA,EAAA7F,GACN,iBAAAqD,GAAmB,EAAA,EAAArD,EACnB,CAAA,cAAAuG,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAAvG,GAC/D,cAAA2F,GAAgB,EAAA,EAAA3F,GAChB,gBAAAyF,GAAkB,EAAA,EAAAzF,GAClB,YAAA8F,GAAc,EAAA,EAAA9F,GACd,WAAAqJ,GAAa,EAAA,EAAArJ,EACb,CAAA,WAAAE,CAAA,EAAAF,EACA,CAAA,KAAAI,EAAA,EAAAJ,GACA,OAAAyD,GAA6B,QAAA,EAAAzD,GAC7B,YAAAuH,GAA6B,IAAA,EAAAvH,EAC7B,CAAA,OAAAK,EAAA,EAAAL,GACA,WAAArB,GAAoC,QAAA,EAAAqB,GACpC,SAAAsH,GAAoC,IAAA,EAAAtH,GACpC,WAAAsJ,GAAa,EAAA,EAAAtJ,GACb,UAAAuJ,GAAY,EAAA,EAAAvJ,GACZ,kBAAAwJ,GAAoB,EAAA,EAAAxJ,EACpB,CAAA,KAAAvC,EAAA,EAAAuC,EAEPhE,GAA6B,KAEjCyN,GAAA,IAAA,MACCzN,GAAS,SAAS,cAAc,sBAAsB,CAAA,IAGnD,IAAAyE,EAEAiJ,GAAqB,SAEnB9H,EAAWC,KAcR,SAAA8H,IAAA,CACD,OAAAlJ,GAAOA,EAAI,aAAeA,EAAI,UAAYA,EAAI,aAAe,IAG5D,SAAAmJ,IAAA,CACHnJ,IACLA,EAAI,SAAS,EAAGA,EAAI,YAAY,OAChCiJ,GAAqB,EAAA,GAWP,eAAAG,IAAA,CACTR,KAEDM,GAAA,GAKG,MAAA7B,GAAA,EACN8B,WAEAF,GAAqB,EAAA,GAGvBD,GAAA,IAAA,CACCI,OAMDJ,GAAA,IAAA,CACU,SAAAK,GAAA,CACJH,GAAA,QACHD,GAAqB,EAAA,EAMvB,OAAAjJ,GAAK,iBAAiB,SAAUqJ,CAAa,OAE5CrJ,GAAK,oBAAoB,SAAUqJ,CAAa,KAoBzC,SAAAC,GACR/L,EACArB,GACA6E,GAAA,CAEI,GAAAA,KAAa,QAAUA,KAAa,QAAA,OACjCwI,GAAO/J,EAGT,IAAAxB,GAAauL,GAAK,OAAS,OACxBA,GAAKvL,EAAU,EAAE,OAAS,aAChCA,KAEDmD,EAASJ,GAAA,CACR,MAAOwI,GAAKvL,EAAU,EAAE,MACxB,MAAOuL,GAAKvL,EAAU,EAAE,oBAKtBE,KAAe,SAClBiD,EAAS,OAAA,CACR,MAAOjF,GAAQ,MACf,MAAOA,GAAQ,QACf,MAAO6E,KAAa,cAGhB,GAAA,CAAA5C,EAAA,OAEC,MAAAqL,GAAgBrL,EAAgBZ,CAAC,GAChCkM,GAAOC,EAAI,GACjBF,GAAc,CAAC,EACfA,GAAcA,GAAc,OAAS,CAAC,CAAA,EAGvCrI,EAAS,OAAA,CACR,MAAQ,CAAAsI,GAAM,MAAOC,GAAK,KAAK,EAC/B,MAAOF,GAAc,IAAKjH,IAAMA,GAAE,OAAO,EACzC,MAAOxB,KAAa,UAKd,SAAA4I,IAAA,KACHnK,GAAU,CAAArB,GAAmBA,EAAgB,SAAW,EACrD,aACFyL,EAAazL,EAAgBA,EAAgB,OAAS,CAAC,KACzDyL,EAAW,CAAC,EAAE,OAAS,mBACpBA,EAAWA,EAAW,OAAS,CAAC,EAAE,qCAY/BC,EAAS,MAAS7N,GAAwBwD,CAAK,EACrD2B,EAAS,QACR,CAAA,YAAa0I,CAAA,CAAA,QAEN3G,EAAC,CACT,QAAQ,MAAMA,CAAC,MACXhH,GAAUgH,aAAa4G,GAAa5G,EAAE,QAAU,gBACpD/B,EAAS,QAASjF,EAAO,IAKYkH,GAAA,IAAAjC,EAAS,OAAO,WAkDtCJ,KAAauI,GAAY/L,EAAGD,GAAS,CAAC,EAAGyD,EAAQ,EAGvDkC,GAAAC,GAAM/B,EAAS,OAAQ+B,EAAE,MAAM,aAWtC/B,EAAS,gBACD,CAAA,MAAAzD,EACP,MAAOqM,GAAO,KAAA,CAAA,EAeAC,GAAA9G,GAAM/B,EAAS,iBAAkB+B,EAAE,MAAM,6CAvEpDlD,EAAGiK,g0CAhNJzB,EAAA,4BA2FHhJ,GAASiJ,GAAmBjK,IAClC4K,yCAmBKc,GAAO1K,EAAO8I,CAAS,SAC3BA,EAAY9I,CAAA,EACZ2B,EAAS,QAAQ,2BAGnBH,EAAA,GAAG7C,EAAkBqB,GAASvB,GAAeuB,CAAiB,CAAA,mBAC9DwB,EAAA,GAAGmJ,EAAU3K,GAASmK,GAAA,CAAA,+wHCzER,WAAA1K,MAAO,YACb,CAAA,KAAAA,MAAO,IAAI,EACbA,EAAc,EAAA,iBACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,wNALS,WAAAA,MAAO,YACbE,EAAA,CAAA,EAAA,UAAA,CAAA,KAAAF,MAAO,IAAI,qBACbA,EAAc,EAAA,CAAA,iCACHA,EAAc,EAAA,EAAC,gBAAkB,SAC7C,SACA,iLAQImL,SACC,GACA,MAAAnL,MAAS,4GAATE,EAAA,CAAA,EAAA,KAAAkL,EAAA,MAAApL,MAAS,uIAjBdA,EAAc,EAAA,GAAAmD,GAAAnD,CAAA,IAYbA,EAAU,CAAA,GAAAgB,GAAAhB,CAAA,0BASR,KAAAA,MAAO,gBACDA,EAAW,CAAA,yEAIhBA,EAAM,EAAA,4DAEuBA,EAA0B,EAAA,yDAG7CA,EAAc,EAAA,GAAE,SAAW,qBAChCA,EAAc,EAAA,GAAE,SAAW,0PA6BvB,eAAAA,MAAO,0BACXA,EAAI,EAAA,EACV,KAAAA,MAAO,iaApDf3D,GAuDKC,EAAAyE,EAAAvE,CAAA,mDAlEAwD,EAAc,EAAA,yHAYbA,EAAU,CAAA,mHASRE,EAAA,CAAA,EAAA,WAAAmL,EAAA,KAAArL,MAAO,8BACDA,EAAW,CAAA,kIAIhBA,EAAM,EAAA,gGAEuBA,EAA0B,EAAA,0GAG7CA,EAAc,EAAA,GAAE,SAAW,yCAChCA,EAAc,EAAA,GAAE,SAAW,meA6BvBE,EAAA,CAAA,EAAA,WAAAmL,EAAA,eAAArL,MAAO,0CACXA,EAAI,EAAA,GACVE,EAAA,CAAA,EAAA,WAAAmL,EAAA,KAAArL,MAAO,wSA1EN,4GAOO,QACV,ikBA5EK,QAAAsL,EAAU,EAAA,EAAAhL,EACV,CAAA,aAAAiL,EAAA,EAAA,EAAAjL,GACA,QAAAkL,EAAU,EAAA,EAAAlL,EACV,CAAA,MAAAC,EAAA,EAAA,EAAAD,GACA,MAAAmL,EAAuB,IAAA,EAAAnL,GACvB,UAAAoL,EAAgC,MAAA,EAAApL,EAChC,CAAA,MAAAqL,CAAA,EAAArL,GACA,WAAAsL,EAAa,EAAA,EAAAtL,EACb,CAAA,KAAAvC,CAAA,EAAAuC,GACA,YAAAuL,EAAc,EAAA,EAAAvL,GACd,SAAAkD,EAAW,EAAA,EAAAlD,GACX,kBAAAmJ,EAAoB,EAAA,EAAAnJ,GACpB,IAAA6F,EAAM,EAAA,EAAA7F,GACN,iBAAAqD,EAAmB,EAAA,EAAArD,GACnB,qBAAAoJ,EAAuB,EAAA,EAAApJ,GACvB,cAAA2F,EAAgB,EAAA,EAAA3F,GAChB,OAAAyD,EAA6B,QAAA,EAAAzD,GAC7B,KAAAD,EAA8B,QAAA,EAAAC,GAC9B,gBAAAyF,EAAkB,EAAA,EAAAzF,GAClB,YAAA8F,GAAc,EAAA,EAAA9F,GACd,WAAAqJ,GAAa,EAAA,EAAArJ,GACb,WAAAsJ,GAAa,EAAA,EAAAtJ,GACb,UAAAuJ,GAAY,EAAA,EAAAvJ,GACZ,2BAAAwL,GAA6B,EAAA,EAAAxL,EAC7B,CAAA,iBAAA0F,EAAA,EAAA1F,EAKA,CAAA,OAAAyL,CAAA,EAAAzL,EAeP0L,GAAA,CAAA,EAOO,CAAA,cAAAnF,GAAA,CAAqD,KAAM,IAAI,CAAA,EAAAvG,GAC/D,kBAAAwJ,GAAoB,EAAA,EAAAxJ,GACpB,eAAA2L,GAA4C,MAAA,EAAA3L,EAC5C,CAAA,OAAA4L,EAAA,EAAA5L,EACA,CAAA,WAAA6L,EAAA,EAAA7L,EACA,CAAA,WAAA8L,EAAA,EAAA9L,EACA,CAAA,WAAA+L,EAAA,EAAA/L,GACA,YAAAuH,GAA6B,IAAA,EAAAvH,GAC7B,SAAAsH,GAAoC,IAAA,EAAAtH,EACpC,CAAA,WAAAE,EAAA,EAAAF,GACA,qBAAAO,EAAuB,EAAA,EAAAP,EA0BT,MAAAgM,GAAA,IAAAP,EAAO,SAAS,eAAgBE,EAAc,EAmDzDhM,EAAA,IAAAsM,IAASR,EAAO,OAAO,UAAUQ,CAAI,EACrCzD,GAAA,IAAAyD,IAASR,EAAO,OAAO,SAASQ,CAAI,EAxB/BC,GAAA,IAAAT,EAAO,SAAS,SAAUxL,CAAK,KACpC0D,GAAM8H,EAAO,SAAS,SAAU9H,EAAE,MAAM,KAC1CA,GAAM8H,EAAO,SAAS,OAAQ9H,EAAE,MAAM,KACrCA,GAAM8H,EAAO,SAAS,QAAS9H,EAAE,MAAM,KACvCA,GAAM8H,EAAO,SAAS,QAAS9H,EAAE,MAAM,KAC9BA,GAAM8H,EAAO,SAAS,iBAAkB9H,EAAE,MAAM,KACjDA,GAAM8H,EAAO,SAAS,gBAAiB9H,EAAE,MAAM,KACvDA,GAAM8H,EAAO,SAAS,QAAS9H,EAAE,MAAM,KACxCA,GAAM8H,EAAO,SAAS,OAAQ9H,EAAE,MAAM,cAE/C1D,EAAK,CAAA,CAAA,EACLwL,EAAO,SAAS,OAAO,MAEd9H,GAAM8H,EAAO,SAAS,OAAQ9H,EAAE,MAAM,siDAlFlDlC,EAAA,GAAGiK,GACF3L,IAAS,SACN9B,GAAiBgC,EAAsBxC,CAAI,EAC3CK,GAAmBmC,EAAoBxC,CAAI,CAAA"}