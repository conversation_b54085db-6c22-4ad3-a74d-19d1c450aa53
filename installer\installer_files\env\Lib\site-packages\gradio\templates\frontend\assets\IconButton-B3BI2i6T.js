import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";const{SvelteComponent:J,append:P,attr:c,bubble:K,check_outros:L,construct_svelte_component:S,create_component:j,create_slot:M,destroy_component:p,detach:F,element:B,flush:g,get_all_dirty_from_scope:N,get_slot_changes:O,group_outros:Q,init:R,insert:G,listen:T,mount_component:A,safe_not_equal:U,set_data:V,set_style:z,space:D,text:W,toggle_class:r,transition_in:v,transition_out:q,update_slot_base:X}=window.__gradio__svelte__internal;function E(l){let e,a;return{c(){e=B("span"),a=W(l[1]),c(e,"class","svelte-1h72pol")},m(o,i){G(o,e,i),P(e,a)},p(o,i){i&2&&V(a,o[1])},d(o){o&&F(e)}}}function Y(l){let e,a,o,i,b,f,m,k,u=l[2]&&E(l);var d=l[0];function w(t,s){return{}}d&&(i=S(d,w()));const _=l[14].default,h=M(_,l,l[13],null);return{c(){e=B("button"),u&&u.c(),a=D(),o=B("div"),i&&j(i.$$.fragment),b=D(),h&&h.c(),c(o,"class","svelte-1h72pol"),r(o,"small",l[4]==="small"),r(o,"large",l[4]==="large"),r(o,"medium",l[4]==="medium"),e.disabled=l[7],c(e,"aria-label",l[1]),c(e,"aria-haspopup",l[8]),c(e,"title",l[1]),c(e,"class","svelte-1h72pol"),r(e,"pending",l[3]),r(e,"padded",l[5]),r(e,"highlight",l[6]),r(e,"transparent",l[9]),z(e,"color",!l[7]&&l[11]?l[11]:"var(--block-label-text-color)"),z(e,"--bg-color",l[7]?"auto":l[10])},m(t,s){G(t,e,s),u&&u.m(e,null),P(e,a),P(e,o),i&&A(i,o,null),P(o,b),h&&h.m(o,null),f=!0,m||(k=T(e,"click",l[15]),m=!0)},p(t,[s]){if(t[2]?u?u.p(t,s):(u=E(t),u.c(),u.m(e,a)):u&&(u.d(1),u=null),s&1&&d!==(d=t[0])){if(i){Q();const I=i;q(I.$$.fragment,1,0,()=>{p(I,1)}),L()}d?(i=S(d,w()),j(i.$$.fragment),v(i.$$.fragment,1),A(i,o,b)):i=null}h&&h.p&&(!f||s&8192)&&X(h,_,t,t[13],f?O(_,t[13],s,null):N(t[13]),null),(!f||s&16)&&r(o,"small",t[4]==="small"),(!f||s&16)&&r(o,"large",t[4]==="large"),(!f||s&16)&&r(o,"medium",t[4]==="medium"),(!f||s&128)&&(e.disabled=t[7]),(!f||s&2)&&c(e,"aria-label",t[1]),(!f||s&256)&&c(e,"aria-haspopup",t[8]),(!f||s&2)&&c(e,"title",t[1]),(!f||s&8)&&r(e,"pending",t[3]),(!f||s&32)&&r(e,"padded",t[5]),(!f||s&64)&&r(e,"highlight",t[6]),(!f||s&512)&&r(e,"transparent",t[9]),s&2176&&z(e,"color",!t[7]&&t[11]?t[11]:"var(--block-label-text-color)"),s&1152&&z(e,"--bg-color",t[7]?"auto":t[10])},i(t){f||(i&&v(i.$$.fragment,t),v(h,t),f=!0)},o(t){i&&q(i.$$.fragment,t),q(h,t),f=!1},d(t){t&&F(e),u&&u.d(),i&&p(i),h&&h.d(t),m=!1,k()}}}function Z(l,e,a){let o,{$$slots:i={},$$scope:b}=e,{Icon:f}=e,{label:m=""}=e,{show_label:k=!1}=e,{pending:u=!1}=e,{size:d="small"}=e,{padded:w=!0}=e,{highlight:_=!1}=e,{disabled:h=!1}=e,{hasPopup:t=!1}=e,{color:s="var(--block-label-text-color)"}=e,{transparent:I=!1}=e,{background:C="var(--block-background-fill)"}=e;function H(n){K.call(this,l,n)}return l.$$set=n=>{"Icon"in n&&a(0,f=n.Icon),"label"in n&&a(1,m=n.label),"show_label"in n&&a(2,k=n.show_label),"pending"in n&&a(3,u=n.pending),"size"in n&&a(4,d=n.size),"padded"in n&&a(5,w=n.padded),"highlight"in n&&a(6,_=n.highlight),"disabled"in n&&a(7,h=n.disabled),"hasPopup"in n&&a(8,t=n.hasPopup),"color"in n&&a(12,s=n.color),"transparent"in n&&a(9,I=n.transparent),"background"in n&&a(10,C=n.background),"$$scope"in n&&a(13,b=n.$$scope)},l.$$.update=()=>{l.$$.dirty&4160&&a(11,o=_?"var(--color-accent)":s)},[f,m,k,u,d,w,_,h,t,I,C,o,s,b,i,H]}class x extends J{constructor(e){super(),R(this,e,Z,Y,U,{Icon:0,label:1,show_label:2,pending:3,size:4,padded:5,highlight:6,disabled:7,hasPopup:8,color:12,transparent:9,background:10})}get Icon(){return this.$$.ctx[0]}set Icon(e){this.$$set({Icon:e}),g()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),g()}get show_label(){return this.$$.ctx[2]}set show_label(e){this.$$set({show_label:e}),g()}get pending(){return this.$$.ctx[3]}set pending(e){this.$$set({pending:e}),g()}get size(){return this.$$.ctx[4]}set size(e){this.$$set({size:e}),g()}get padded(){return this.$$.ctx[5]}set padded(e){this.$$set({padded:e}),g()}get highlight(){return this.$$.ctx[6]}set highlight(e){this.$$set({highlight:e}),g()}get disabled(){return this.$$.ctx[7]}set disabled(e){this.$$set({disabled:e}),g()}get hasPopup(){return this.$$.ctx[8]}set hasPopup(e){this.$$set({hasPopup:e}),g()}get color(){return this.$$.ctx[12]}set color(e){this.$$set({color:e}),g()}get transparent(){return this.$$.ctx[9]}set transparent(e){this.$$set({transparent:e}),g()}get background(){return this.$$.ctx[10]}set background(e){this.$$set({background:e}),g()}}export{x as I};
//# sourceMappingURL=IconButton-B3BI2i6T.js.map
