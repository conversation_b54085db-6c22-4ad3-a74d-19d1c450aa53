{"version": 3, "file": "Index-BMLc4VxK.js", "sources": ["../../../../js/timer/Index.svelte"], "sourcesContent": ["<script lang=\"ts\">\n\timport type { Gradio } from \"@gradio/utils\";\n\timport { onDestroy } from \"svelte\";\n\texport let gradio: Gradio<{\n\t\ttick: never;\n\t}>;\n\texport let value = 1;\n\texport let active = true;\n\tlet old_value: number;\n\tlet old_active: boolean;\n\tlet interval: NodeJS.Timeout;\n\n\t$: if (old_value !== value || active !== old_active) {\n\t\tif (interval) clearInterval(interval);\n\t\tif (active) {\n\t\t\tinterval = setInterval(() => {\n\t\t\t\tif (document.visibilityState === \"visible\") gradio.dispatch(\"tick\");\n\t\t\t}, value * 1000);\n\t\t}\n\t\told_value = value;\n\t\told_active = active;\n\t}\n\n\tonDestroy(() => {\n\t\tif (interval) clearInterval(interval);\n\t});\n</script>\n"], "names": ["gradio", "$$props", "value", "active", "old_value", "old_active", "interval", "onDestroy"], "mappings": "uGAE2B,EAAA,OAAA,6CACf,GAAA,CAAA,OAAAA,CAAA,EAAAC,GAGA,MAAAC,EAAQ,CAAA,EAAAD,GACR,OAAAE,EAAS,EAAA,EAAAF,EAChBG,EACAC,EACAC,EAaJ,OAAAC,EAAA,IAAA,CACKD,GAAU,cAAcA,CAAQ,0IAZ9BF,IAAcF,GAASC,IAAWE,KACpCC,GAAU,cAAcA,CAAQ,EAChCH,OACHG,EAAW,iBACN,SAAS,kBAAoB,WAAWN,EAAO,SAAS,MAAM,GAChEE,EAAQ,UAEZE,EAAYF,CAAA,MACZG,EAAaF,CAAA"}