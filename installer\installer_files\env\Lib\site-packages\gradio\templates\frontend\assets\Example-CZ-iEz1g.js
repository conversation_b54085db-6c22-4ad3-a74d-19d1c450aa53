const{SvelteComponent:f,append:d,attr:g,detach:o,element:_,flush:c,init:v,insert:h,noop:r,safe_not_equal:y,set_data:m,text:b,toggle_class:u}=window.__gradio__svelte__internal;function S(s){let e,a=(s[0]!==null?s[0].toLocaleString():"")+"",n;return{c(){e=_("div"),n=b(a),g(e,"class","svelte-1ayixqk"),u(e,"table",s[1]==="table"),u(e,"gallery",s[1]==="gallery"),u(e,"selected",s[2])},m(t,l){h(t,e,l),d(e,n)},p(t,[l]){l&1&&a!==(a=(t[0]!==null?t[0].toLocaleString():"")+"")&&m(n,a),l&2&&u(e,"table",t[1]==="table"),l&2&&u(e,"gallery",t[1]==="gallery"),l&4&&u(e,"selected",t[2])},i:r,o:r,d(t){t&&o(e)}}}function q(s,e,a){let{value:n}=e,{type:t}=e,{selected:l=!1}=e;return s.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,l=i.selected)},[n,t,l]}class w extends f{constructor(e){super(),v(this,e,q,S,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),c()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),c()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),c()}}export{w as default};
//# sourceMappingURL=Example-CZ-iEz1g.js.map
