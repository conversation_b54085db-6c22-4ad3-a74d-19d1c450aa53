import{B as L}from"./Block-CB3nIXHA.js";import{I as M}from"./Info-QztxykAE.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{S as O}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./MarkdownCode-DYQlapxH.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./IconButton-B3BI2i6T.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:P,append:C,attr:v,detach:Q,element:B,flush:I,init:R,insert:V,listen:E,noop:D,run_all:W,safe_not_equal:X,set_data:Y,space:Z,text:x,toggle_class:N}=window.__gradio__svelte__internal,{createEventDispatcher:y}=window.__gradio__svelte__internal;function p(n){let e,t,s,i,u,_,h;return{c(){e=B("label"),t=B("input"),s=Z(),i=B("span"),u=x(n[1]),t.disabled=n[2],v(t,"type","checkbox"),v(t,"name","test"),v(t,"data-testid","checkbox"),v(t,"class","svelte-5ncdh7"),v(i,"class","svelte-5ncdh7"),v(e,"class","svelte-5ncdh7"),N(e,"disabled",n[2])},m(f,c){V(f,e,c),C(e,t),t.checked=n[0],C(e,s),C(e,i),C(i,u),_||(h=[E(t,"change",n[6]),E(t,"keydown",n[3]),E(t,"input",n[4])],_=!0)},p(f,[c]){c&4&&(t.disabled=f[2]),c&1&&(t.checked=f[0]),c&2&&Y(u,f[1]),c&4&&N(e,"disabled",f[2])},i:D,o:D,d(f){f&&Q(e),_=!1,W(h)}}}function $(n,e,t){let s,{value:i=!1}=e,{label:u="Checkbox"}=e,{interactive:_}=e;const h=y();async function f(r){r.key==="Enter"&&(t(0,i=!i),h("select",{index:0,value:r.currentTarget.checked,selected:r.currentTarget.checked}))}async function c(r){t(0,i=r.currentTarget.checked),h("select",{index:0,value:r.currentTarget.checked,selected:r.currentTarget.checked})}function b(){i=this.checked,t(0,i)}return n.$$set=r=>{"value"in r&&t(0,i=r.value),"label"in r&&t(1,u=r.label),"interactive"in r&&t(5,_=r.interactive)},n.$$.update=()=>{n.$$.dirty&1&&h("change",i),n.$$.dirty&32&&t(2,s=!_)},[i,u,s,f,c,_,b]}class ee extends P{constructor(e){super(),R(this,e,$,p,X,{value:0,label:1,interactive:5})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),I()}get label(){return this.$$.ctx[1]}set label(e){this.$$set({label:e}),I()}get interactive(){return this.$$.ctx[5]}set interactive(e){this.$$set({interactive:e}),I()}}const te=ee,{SvelteComponent:ie,add_flush_callback:ne,assign:se,bind:le,binding_callbacks:ae,check_outros:ce,create_component:S,destroy_component:T,detach:U,flush:d,get_spread_object:ue,get_spread_update:re,group_outros:oe,init:_e,insert:z,mount_component:q,safe_not_equal:fe,space:A,transition_in:g,transition_out:k}=window.__gradio__svelte__internal,{afterUpdate:he}=window.__gradio__svelte__internal;function F(n){let e,t;return e=new M({props:{root:n[6],info:n[5]}}),{c(){S(e.$$.fragment)},m(s,i){q(e,s,i),t=!0},p(s,i){const u={};i&64&&(u.root=s[6]),i&32&&(u.info=s[5]),e.$set(u)},i(s){t||(g(e.$$.fragment,s),t=!0)},o(s){k(e.$$.fragment,s),t=!1},d(s){T(e,s)}}}function de(n){let e,t,s,i,u,_;const h=[{autoscroll:n[11].autoscroll},{i18n:n[11].i18n},n[10]];let f={};for(let l=0;l<h.length;l+=1)f=se(f,h[l]);e=new O({props:f}),e.$on("clear_status",n[15]);let c=n[5]&&F(n);function b(l){n[16](l)}let r={label:n[4],interactive:n[12]};return n[0]!==void 0&&(r.value=n[0]),i=new te({props:r}),ae.push(()=>le(i,"value",b)),i.$on("change",n[13]),i.$on("select",n[17]),{c(){S(e.$$.fragment),t=A(),c&&c.c(),s=A(),S(i.$$.fragment)},m(l,o){q(e,l,o),z(l,t,o),c&&c.m(l,o),z(l,s,o),q(i,l,o),_=!0},p(l,o){const w=o&3072?re(h,[o&2048&&{autoscroll:l[11].autoscroll},o&2048&&{i18n:l[11].i18n},o&1024&&ue(l[10])]):{};e.$set(w),l[5]?c?(c.p(l,o),o&32&&g(c,1)):(c=F(l),c.c(),g(c,1),c.m(s.parentNode,s)):c&&(oe(),k(c,1,1,()=>{c=null}),ce());const m={};o&16&&(m.label=l[4]),o&4096&&(m.interactive=l[12]),!u&&o&1&&(u=!0,m.value=l[0],ne(()=>u=!1)),i.$set(m)},i(l){_||(g(e.$$.fragment,l),g(c),g(i.$$.fragment,l),_=!0)},o(l){k(e.$$.fragment,l),k(c),k(i.$$.fragment,l),_=!1},d(l){l&&(U(t),U(s)),T(e,l),c&&c.d(l),T(i,l)}}}function me(n){let e,t;return e=new L({props:{visible:n[3],elem_id:n[1],elem_classes:n[2],container:n[7],scale:n[8],min_width:n[9],$$slots:{default:[de]},$$scope:{ctx:n}}}),{c(){S(e.$$.fragment)},m(s,i){q(e,s,i),t=!0},p(s,[i]){const u={};i&8&&(u.visible=s[3]),i&2&&(u.elem_id=s[1]),i&4&&(u.elem_classes=s[2]),i&128&&(u.container=s[7]),i&256&&(u.scale=s[8]),i&512&&(u.min_width=s[9]),i&269425&&(u.$$scope={dirty:i,ctx:s}),e.$set(u)},i(s){t||(g(e.$$.fragment,s),t=!0)},o(s){k(e.$$.fragment,s),t=!1},d(s){T(e,s)}}}function ge(n,e,t){let{elem_id:s=""}=e,{elem_classes:i=[]}=e,{visible:u=!0}=e,{value:_=!1}=e,{value_is_output:h=!1}=e,{label:f="Checkbox"}=e,{info:c=void 0}=e,{root:b}=e,{container:r=!0}=e,{scale:l=null}=e,{min_width:o=void 0}=e,{loading_status:w}=e,{gradio:m}=e,{interactive:j}=e;function G(){m.dispatch("change"),h||m.dispatch("input")}he(()=>{t(14,h=!1)});const H=()=>m.dispatch("clear_status",w);function J(a){_=a,t(0,_)}const K=a=>m.dispatch("select",a.detail);return n.$$set=a=>{"elem_id"in a&&t(1,s=a.elem_id),"elem_classes"in a&&t(2,i=a.elem_classes),"visible"in a&&t(3,u=a.visible),"value"in a&&t(0,_=a.value),"value_is_output"in a&&t(14,h=a.value_is_output),"label"in a&&t(4,f=a.label),"info"in a&&t(5,c=a.info),"root"in a&&t(6,b=a.root),"container"in a&&t(7,r=a.container),"scale"in a&&t(8,l=a.scale),"min_width"in a&&t(9,o=a.min_width),"loading_status"in a&&t(10,w=a.loading_status),"gradio"in a&&t(11,m=a.gradio),"interactive"in a&&t(12,j=a.interactive)},[_,s,i,u,f,c,b,r,l,o,w,m,j,G,h,H,J,K]}class De extends ie{constructor(e){super(),_e(this,e,ge,me,fe,{elem_id:1,elem_classes:2,visible:3,value:0,value_is_output:14,label:4,info:5,root:6,container:7,scale:8,min_width:9,loading_status:10,gradio:11,interactive:12})}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),d()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),d()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),d()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),d()}get value_is_output(){return this.$$.ctx[14]}set value_is_output(e){this.$$set({value_is_output:e}),d()}get label(){return this.$$.ctx[4]}set label(e){this.$$set({label:e}),d()}get info(){return this.$$.ctx[5]}set info(e){this.$$set({info:e}),d()}get root(){return this.$$.ctx[6]}set root(e){this.$$set({root:e}),d()}get container(){return this.$$.ctx[7]}set container(e){this.$$set({container:e}),d()}get scale(){return this.$$.ctx[8]}set scale(e){this.$$set({scale:e}),d()}get min_width(){return this.$$.ctx[9]}set min_width(e){this.$$set({min_width:e}),d()}get loading_status(){return this.$$.ctx[10]}set loading_status(e){this.$$set({loading_status:e}),d()}get gradio(){return this.$$.ctx[11]}set gradio(e){this.$$set({gradio:e}),d()}get interactive(){return this.$$.ctx[12]}set interactive(e){this.$$set({interactive:e}),d()}}export{te as BaseCheckbox,De as default};
//# sourceMappingURL=Index-BLSQY0-a.js.map
