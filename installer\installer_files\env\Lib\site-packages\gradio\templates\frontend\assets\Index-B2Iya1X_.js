import Oe from"./ImagePreview-BIR76Np-.js";import{I as We}from"./ImageUploader-D3sNPugw.js";import{W as Ct}from"./ImageUploader-D3sNPugw.js";import{B as ee}from"./Block-CB3nIXHA.js";import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{E as je}from"./Empty-B_fwEKaS.js";import{I as qe}from"./FullscreenButton-BWloBRD_.js";import{U as te}from"./UploadText-BVcQ46sk.js";import{S as ie}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./index-BkoKOheB.js";import{I as At}from"./Image-BRVH1yXn.js";import{default as Ht}from"./Example-DqJU7ngj.js";import"./utils-BsGrhMNe.js";import"./BlockLabel-CCoHIDM7.js";import"./IconButton-B3BI2i6T.js";import"./ShareButton-HkxSYutN.js";import"./Community-Dw1micSV.js";import"./Download-DVtk-Jv3.js";import"./IconButtonWrapper-BMUxHqmv.js";import"./utils-Gtzs_Zla.js";import"./DownloadLink-IzUam-rM.js";import"./context-TgWPFwN2.js";import"./file-url-DgijyRSD.js";/* empty css                                                   *//* empty css                                                   */import"./prism-python-B8dcvKZU.js";import"./svelte/svelte.js";import"./Clear-By3xiIwg.js";import"./SelectSource-cjMqi4BC.js";import"./Upload-DXgDHKDd.js";import"./DropdownArrow-CRmyeEUc.js";import"./Square-oAGqOwsh.js";import"./index-C30MtwUc.js";import"./StreamingBar-CCUnT5tf.js";import"./Upload-CRdEJrCZ.js";/* empty css                                             *//* empty css                                              */const{SvelteComponent:Ce,add_flush_callback:E,assign:se,bind:P,binding_callbacks:N,check_outros:ne,create_component:k,destroy_component:I,detach:G,empty:le,flush:f,get_spread_object:re,get_spread_update:ae,group_outros:oe,init:Te,insert:H,mount_component:B,safe_not_equal:Ae,space:_e,transition_in:b,transition_out:w}=window.__gradio__svelte__internal,{afterUpdate:Ge}=window.__gradio__svelte__internal;function He(i){let e,s;return e=new ee({props:{visible:i[5],variant:i[0]===null?"dashed":"solid",border_mode:i[29]?"focus":"base",padding:!1,elem_id:i[3],elem_classes:i[4],height:i[10]||void 0,width:i[11],allow_overflow:!1,container:i[14],scale:i[15],min_width:i[16],$$slots:{default:[Xe]},$$scope:{ctx:i}}}),e.$on("dragenter",i[32]),e.$on("dragleave",i[32]),e.$on("dragover",i[32]),e.$on("drop",i[33]),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},p(t,l){const o={};l[0]&32&&(o.visible=t[5]),l[0]&1&&(o.variant=t[0]===null?"dashed":"solid"),l[0]&536870912&&(o.border_mode=t[29]?"focus":"base"),l[0]&8&&(o.elem_id=t[3]),l[0]&16&&(o.elem_classes=t[4]),l[0]&1024&&(o.height=t[10]||void 0),l[0]&2048&&(o.width=t[11]),l[0]&16384&&(o.container=t[14]),l[0]&32768&&(o.scale=t[15]),l[0]&65536&&(o.min_width=t[16]),l[0]&2129932999|l[1]&1073741825&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Ke(i){let e,s;return e=new ee({props:{visible:i[5],variant:"solid",border_mode:i[29]?"focus":"base",padding:!1,elem_id:i[3],elem_classes:i[4],height:i[10]||void 0,width:i[11],allow_overflow:!1,container:i[14],scale:i[15],min_width:i[16],$$slots:{default:[Ye]},$$scope:{ctx:i}}}),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},p(t,l){const o={};l[0]&32&&(o.visible=t[5]),l[0]&536870912&&(o.border_mode=t[29]?"focus":"base"),l[0]&8&&(o.elem_id=t[3]),l[0]&16&&(o.elem_classes=t[4]),l[0]&1024&&(o.height=t[10]||void 0),l[0]&2048&&(o.width=t[11]),l[0]&16384&&(o.container=t[14]),l[0]&32768&&(o.scale=t[15]),l[0]&65536&&(o.min_width=t[16]),l[0]&84025797|l[1]&1073741824&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Le(i){let e,s;return e=new je({props:{unpadded_box:!0,size:"large",$$slots:{default:[Re]},$$scope:{ctx:i}}}),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},p(t,l){const o={};l[1]&1073741824&&(o.$$scope={dirty:l,ctx:t}),e.$set(o)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Me(i){let e,s;return e=new te({props:{i18n:i[26].i18n,type:"clipboard",mode:"short"}}),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},p(t,l){const o={};l[0]&67108864&&(o.i18n=t[26].i18n),e.$set(o)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Qe(i){let e,s;return e=new te({props:{i18n:i[26].i18n,type:"image",placeholder:i[23]}}),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},p(t,l){const o={};l[0]&67108864&&(o.i18n=t[26].i18n),l[0]&8388608&&(o.placeholder=t[23]),e.$set(o)},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Re(i){let e,s;return e=new qe({}),{c(){k(e.$$.fragment)},m(t,l){B(e,t,l),s=!0},i(t){s||(b(e.$$.fragment,t),s=!0)},o(t){w(e.$$.fragment,t),s=!1},d(t){I(e,t)}}}function Ve(i){let e,s,t,l;const o=[Qe,Me,Le],m=[];function _(r,g){return r[30]==="upload"||!r[30]?0:r[30]==="clipboard"?1:2}return e=_(i),s=m[e]=o[e](i),{c(){s.c(),t=le()},m(r,g){m[e].m(r,g),H(r,t,g),l=!0},p(r,g){let c=e;e=_(r),e===c?m[e].p(r,g):(oe(),w(m[c],1,1,()=>{m[c]=null}),ne(),s=m[e],s?s.p(r,g):(s=m[e]=o[e](r),s.c()),b(s,1),s.m(t.parentNode,t))},i(r){l||(b(s),l=!0)},o(r){w(s),l=!1},d(r){r&&G(t),m[e].d(r)}}}function Xe(i){let e,s,t,l,o,m,_,r,g,c;const D=[{autoscroll:i[26].autoscroll},{i18n:i[26].i18n},i[2]];let p={};for(let a=0;a<D.length;a+=1)p=se(p,D[a]);e=new ie({props:p}),e.$on("clear_status",i[42]);function F(a){i[45](a)}function O(a){i[46](a)}function W(a){i[47](a)}function j(a){i[48](a)}function q(a){i[49](a)}function C(a){i[50](a)}let v={selectable:i[13],root:i[9],sources:i[18],label:i[6],show_label:i[7],pending:i[21],streaming:i[20],mirror_webcam:i[22],stream_every:i[12],webcam_constraints:i[25],max_file_size:i[26].max_file_size,i18n:i[26].i18n,upload:i[43],stream_handler:i[26].client?.stream,$$slots:{default:[Ve]},$$scope:{ctx:i}};return i[27]!==void 0&&(v.uploading=i[27]),i[30]!==void 0&&(v.active_source=i[30]),i[0]!==void 0&&(v.value=i[0]),i[29]!==void 0&&(v.dragging=i[29]),i[28]!==void 0&&(v.modify_stream=i[28]),i[1]!==void 0&&(v.set_time_limit=i[1]),t=new We({props:v}),i[44](t),N.push(()=>P(t,"uploading",F)),N.push(()=>P(t,"active_source",O)),N.push(()=>P(t,"value",W)),N.push(()=>P(t,"dragging",j)),N.push(()=>P(t,"modify_stream",q)),N.push(()=>P(t,"set_time_limit",C)),t.$on("edit",i[51]),t.$on("clear",i[52]),t.$on("stream",i[53]),t.$on("drag",i[54]),t.$on("upload",i[55]),t.$on("select",i[56]),t.$on("share",i[57]),t.$on("error",i[58]),t.$on("close_stream",i[59]),{c(){k(e.$$.fragment),s=_e(),k(t.$$.fragment)},m(a,u){B(e,a,u),H(a,s,u),B(t,a,u),c=!0},p(a,u){const T=u[0]&67108868?ae(D,[u[0]&67108864&&{autoscroll:a[26].autoscroll},u[0]&67108864&&{i18n:a[26].i18n},u[0]&4&&re(a[2])]):{};e.$set(T);const h={};u[0]&8192&&(h.selectable=a[13]),u[0]&512&&(h.root=a[9]),u[0]&262144&&(h.sources=a[18]),u[0]&64&&(h.label=a[6]),u[0]&128&&(h.show_label=a[7]),u[0]&2097152&&(h.pending=a[21]),u[0]&1048576&&(h.streaming=a[20]),u[0]&4194304&&(h.mirror_webcam=a[22]),u[0]&4096&&(h.stream_every=a[12]),u[0]&33554432&&(h.webcam_constraints=a[25]),u[0]&67108864&&(h.max_file_size=a[26].max_file_size),u[0]&67108864&&(h.i18n=a[26].i18n),u[0]&67108864&&(h.upload=a[43]),u[0]&67108864&&(h.stream_handler=a[26].client?.stream),u[0]&1149239296|u[1]&1073741824&&(h.$$scope={dirty:u,ctx:a}),!l&&u[0]&134217728&&(l=!0,h.uploading=a[27],E(()=>l=!1)),!o&&u[0]&1073741824&&(o=!0,h.active_source=a[30],E(()=>o=!1)),!m&&u[0]&1&&(m=!0,h.value=a[0],E(()=>m=!1)),!_&&u[0]&536870912&&(_=!0,h.dragging=a[29],E(()=>_=!1)),!r&&u[0]&268435456&&(r=!0,h.modify_stream=a[28],E(()=>r=!1)),!g&&u[0]&2&&(g=!0,h.set_time_limit=a[1],E(()=>g=!1)),t.$set(h)},i(a){c||(b(e.$$.fragment,a),b(t.$$.fragment,a),c=!0)},o(a){w(e.$$.fragment,a),w(t.$$.fragment,a),c=!1},d(a){a&&G(s),I(e,a),i[44](null),I(t,a)}}}function Ye(i){let e,s,t,l;const o=[{autoscroll:i[26].autoscroll},{i18n:i[26].i18n},i[2]];let m={};for(let _=0;_<o.length;_+=1)m=se(m,o[_]);return e=new ie({props:m}),t=new Oe({props:{value:i[0],label:i[6],show_label:i[7],show_download_button:i[8],selectable:i[13],show_share_button:i[17],i18n:i[26].i18n,show_fullscreen_button:i[24]}}),t.$on("select",i[39]),t.$on("share",i[40]),t.$on("error",i[41]),{c(){k(e.$$.fragment),s=_e(),k(t.$$.fragment)},m(_,r){B(e,_,r),H(_,s,r),B(t,_,r),l=!0},p(_,r){const g=r[0]&67108868?ae(o,[r[0]&67108864&&{autoscroll:_[26].autoscroll},r[0]&67108864&&{i18n:_[26].i18n},r[0]&4&&re(_[2])]):{};e.$set(g);const c={};r[0]&1&&(c.value=_[0]),r[0]&64&&(c.label=_[6]),r[0]&128&&(c.show_label=_[7]),r[0]&256&&(c.show_download_button=_[8]),r[0]&8192&&(c.selectable=_[13]),r[0]&131072&&(c.show_share_button=_[17]),r[0]&67108864&&(c.i18n=_[26].i18n),r[0]&16777216&&(c.show_fullscreen_button=_[24]),t.$set(c)},i(_){l||(b(e.$$.fragment,_),b(t.$$.fragment,_),l=!0)},o(_){w(e.$$.fragment,_),w(t.$$.fragment,_),l=!1},d(_){_&&G(s),I(e,_),I(t,_)}}}function Ze(i){let e,s,t,l;const o=[Ke,He],m=[];function _(r,g){return r[19]?1:0}return e=_(i),s=m[e]=o[e](i),{c(){s.c(),t=le()},m(r,g){m[e].m(r,g),H(r,t,g),l=!0},p(r,g){let c=e;e=_(r),e===c?m[e].p(r,g):(oe(),w(m[c],1,1,()=>{m[c]=null}),ne(),s=m[e],s?s.p(r,g):(s=m[e]=o[e](r),s.c()),b(s,1),s.m(t.parentNode,t))},i(r){l||(b(s),l=!0)},o(r){w(s),l=!1},d(r){r&&G(t),m[e].d(r)}}}function ye(i,e,s){let t="closed",l=()=>{};function o(n){t=n,l(n)}const m=()=>t;let{set_time_limit:_}=e,{value_is_output:r=!1}=e,{elem_id:g=""}=e,{elem_classes:c=[]}=e,{visible:D=!0}=e,{value:p=null}=e,F=null,{label:O}=e,{show_label:W}=e,{show_download_button:j}=e,{root:q}=e,{height:C}=e,{width:v}=e,{stream_every:a}=e,{_selectable:u=!1}=e,{container:T=!0}=e,{scale:h=null}=e,{min_width:Q=void 0}=e,{loading_status:z}=e,{show_share_button:R=!1}=e,{sources:V=["upload","clipboard","webcam"]}=e,{interactive:K}=e,{streaming:X}=e,{pending:Y}=e,{mirror_webcam:Z}=e,{placeholder:y=void 0}=e,{show_fullscreen_button:$}=e,{input_ready:L}=e,{webcam_constraints:x=void 0}=e,A=!1,{gradio:d}=e;Ge(()=>{s(34,r=!1)});let U,M=null,J;const ue=n=>{const S=n;S.preventDefault(),S.stopPropagation(),S.type==="dragenter"||S.type==="dragover"?s(29,U=!0):S.type==="dragleave"&&s(29,U=!1)},fe=n=>{if(K){const S=n;S.preventDefault(),S.stopPropagation(),s(29,U=!1),J&&J.loadFilesFromDrop(S)}},me=({detail:n})=>d.dispatch("select",n),ce=({detail:n})=>d.dispatch("share",n),he=({detail:n})=>d.dispatch("error",n),ge=()=>d.dispatch("clear_status",z),de=(...n)=>d.client.upload(...n);function be(n){N[n?"unshift":"push"](()=>{J=n,s(31,J)})}function we(n){A=n,s(27,A)}function pe(n){M=n,s(30,M)}function ve(n){p=n,s(0,p)}function ke(n){U=n,s(29,U)}function Ie(n){l=n,s(28,l)}function Be(n){_=n,s(1,_)}const Se=()=>d.dispatch("edit"),ze=()=>{d.dispatch("clear")},Ue=({detail:n})=>d.dispatch("stream",n),Ne=({detail:n})=>s(29,U=n),De=()=>d.dispatch("upload"),Ee=({detail:n})=>d.dispatch("select",n),Pe=({detail:n})=>d.dispatch("share",n),Fe=({detail:n})=>{s(2,z=z||{}),s(2,z.status="error",z),d.dispatch("error",n)},Je=()=>{d.dispatch("close_stream","stream")};return i.$$set=n=>{"set_time_limit"in n&&s(1,_=n.set_time_limit),"value_is_output"in n&&s(34,r=n.value_is_output),"elem_id"in n&&s(3,g=n.elem_id),"elem_classes"in n&&s(4,c=n.elem_classes),"visible"in n&&s(5,D=n.visible),"value"in n&&s(0,p=n.value),"label"in n&&s(6,O=n.label),"show_label"in n&&s(7,W=n.show_label),"show_download_button"in n&&s(8,j=n.show_download_button),"root"in n&&s(9,q=n.root),"height"in n&&s(10,C=n.height),"width"in n&&s(11,v=n.width),"stream_every"in n&&s(12,a=n.stream_every),"_selectable"in n&&s(13,u=n._selectable),"container"in n&&s(14,T=n.container),"scale"in n&&s(15,h=n.scale),"min_width"in n&&s(16,Q=n.min_width),"loading_status"in n&&s(2,z=n.loading_status),"show_share_button"in n&&s(17,R=n.show_share_button),"sources"in n&&s(18,V=n.sources),"interactive"in n&&s(19,K=n.interactive),"streaming"in n&&s(20,X=n.streaming),"pending"in n&&s(21,Y=n.pending),"mirror_webcam"in n&&s(22,Z=n.mirror_webcam),"placeholder"in n&&s(23,y=n.placeholder),"show_fullscreen_button"in n&&s(24,$=n.show_fullscreen_button),"input_ready"in n&&s(35,L=n.input_ready),"webcam_constraints"in n&&s(25,x=n.webcam_constraints),"gradio"in n&&s(26,d=n.gradio)},i.$$.update=()=>{i.$$.dirty[0]&134217728&&s(35,L=!A),i.$$.dirty[0]&67108865|i.$$.dirty[1]&136&&JSON.stringify(p)!==JSON.stringify(F)&&(s(38,F=p),d.dispatch("change"),r||d.dispatch("input"))},[p,_,z,g,c,D,O,W,j,q,C,v,a,u,T,h,Q,R,V,K,X,Y,Z,y,$,x,d,A,l,U,M,J,ue,fe,r,L,o,m,F,me,ce,he,ge,de,be,we,pe,ve,ke,Ie,Be,Se,ze,Ue,Ne,De,Ee,Pe,Fe,Je]}class Wt extends Ce{constructor(e){super(),Te(this,e,ye,Ze,Ae,{modify_stream_state:36,get_stream_state:37,set_time_limit:1,value_is_output:34,elem_id:3,elem_classes:4,visible:5,value:0,label:6,show_label:7,show_download_button:8,root:9,height:10,width:11,stream_every:12,_selectable:13,container:14,scale:15,min_width:16,loading_status:2,show_share_button:17,sources:18,interactive:19,streaming:20,pending:21,mirror_webcam:22,placeholder:23,show_fullscreen_button:24,input_ready:35,webcam_constraints:25,gradio:26},null,[-1,-1])}get modify_stream_state(){return this.$$.ctx[36]}get get_stream_state(){return this.$$.ctx[37]}get set_time_limit(){return this.$$.ctx[1]}set set_time_limit(e){this.$$set({set_time_limit:e}),f()}get value_is_output(){return this.$$.ctx[34]}set value_is_output(e){this.$$set({value_is_output:e}),f()}get elem_id(){return this.$$.ctx[3]}set elem_id(e){this.$$set({elem_id:e}),f()}get elem_classes(){return this.$$.ctx[4]}set elem_classes(e){this.$$set({elem_classes:e}),f()}get visible(){return this.$$.ctx[5]}set visible(e){this.$$set({visible:e}),f()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),f()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),f()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),f()}get show_download_button(){return this.$$.ctx[8]}set show_download_button(e){this.$$set({show_download_button:e}),f()}get root(){return this.$$.ctx[9]}set root(e){this.$$set({root:e}),f()}get height(){return this.$$.ctx[10]}set height(e){this.$$set({height:e}),f()}get width(){return this.$$.ctx[11]}set width(e){this.$$set({width:e}),f()}get stream_every(){return this.$$.ctx[12]}set stream_every(e){this.$$set({stream_every:e}),f()}get _selectable(){return this.$$.ctx[13]}set _selectable(e){this.$$set({_selectable:e}),f()}get container(){return this.$$.ctx[14]}set container(e){this.$$set({container:e}),f()}get scale(){return this.$$.ctx[15]}set scale(e){this.$$set({scale:e}),f()}get min_width(){return this.$$.ctx[16]}set min_width(e){this.$$set({min_width:e}),f()}get loading_status(){return this.$$.ctx[2]}set loading_status(e){this.$$set({loading_status:e}),f()}get show_share_button(){return this.$$.ctx[17]}set show_share_button(e){this.$$set({show_share_button:e}),f()}get sources(){return this.$$.ctx[18]}set sources(e){this.$$set({sources:e}),f()}get interactive(){return this.$$.ctx[19]}set interactive(e){this.$$set({interactive:e}),f()}get streaming(){return this.$$.ctx[20]}set streaming(e){this.$$set({streaming:e}),f()}get pending(){return this.$$.ctx[21]}set pending(e){this.$$set({pending:e}),f()}get mirror_webcam(){return this.$$.ctx[22]}set mirror_webcam(e){this.$$set({mirror_webcam:e}),f()}get placeholder(){return this.$$.ctx[23]}set placeholder(e){this.$$set({placeholder:e}),f()}get show_fullscreen_button(){return this.$$.ctx[24]}set show_fullscreen_button(e){this.$$set({show_fullscreen_button:e}),f()}get input_ready(){return this.$$.ctx[35]}set input_ready(e){this.$$set({input_ready:e}),f()}get webcam_constraints(){return this.$$.ctx[25]}set webcam_constraints(e){this.$$set({webcam_constraints:e}),f()}get gradio(){return this.$$.ctx[26]}set gradio(e){this.$$set({gradio:e}),f()}}export{Ht as BaseExample,At as BaseImage,We as BaseImageUploader,Oe as BaseStaticImage,Ct as Webcam,Wt as default};
//# sourceMappingURL=Index-B2Iya1X_.js.map
