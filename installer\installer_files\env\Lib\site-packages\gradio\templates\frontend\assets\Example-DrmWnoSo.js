/* empty css                                              */const{SvelteComponent:d,append:g,attr:_,detach:c,element:o,flush:u,init:y,insert:v,noop:f,safe_not_equal:h,set_data:m,text:b,toggle_class:r}=window.__gradio__svelte__internal;function A(l){let e,a=(l[0]?Array.isArray(l[0])?l[0].join(", "):l[0]:"")+"",n;return{c(){e=o("div"),n=b(a),_(e,"class","svelte-rgtszb"),r(e,"table",l[1]==="table"),r(e,"gallery",l[1]==="gallery"),r(e,"selected",l[2])},m(t,s){v(t,e,s),g(e,n)},p(t,[s]){s&1&&a!==(a=(t[0]?Array.isArray(t[0])?t[0].join(", "):t[0]:"")+"")&&m(n,a),s&2&&r(e,"table",t[1]==="table"),s&2&&r(e,"gallery",t[1]==="gallery"),s&4&&r(e,"selected",t[2])},i:f,o:f,d(t){t&&c(e)}}}function j(l,e,a){let{value:n}=e,{type:t}=e,{selected:s=!1}=e;return l.$$set=i=>{"value"in i&&a(0,n=i.value),"type"in i&&a(1,t=i.type),"selected"in i&&a(2,s=i.selected)},[n,t,s]}class p extends d{constructor(e){super(),y(this,e,j,A,h,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{p as default};
//# sourceMappingURL=Example-DrmWnoSo.js.map
