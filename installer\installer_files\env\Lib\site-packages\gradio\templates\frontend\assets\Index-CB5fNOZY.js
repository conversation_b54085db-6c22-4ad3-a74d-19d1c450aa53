import"./IconButtonWrapper.svelte_svelte_type_style_lang-BiUMvbOz.js";import{u as bn,S as Wl,a as Yl}from"./utils-BsGrhMNe.js";import{F as dn}from"./File-BQ_9P3Ye.js";import{I as vt}from"./Image-BRVH1yXn.js";import{M as gn}from"./MarkdownCode-DYQlapxH.js";/* empty css                                                   *//* empty css                                                   */import"./MarkdownCode.svelte_svelte_type_style_lang-CNW7HO6-.js";import{I as ve}from"./IconButton-B3BI2i6T.js";import{C as Et}from"./Check-BiRlaMNo.js";import{C as It}from"./Copy-CxQ9EyK2.js";import{U as Kl}from"./Undo-DCjBnnSO.js";import{I as ql}from"./IconButtonWrapper-BMUxHqmv.js";import{d as Ql}from"./index-CnqicUFC.js";import{C as Xl}from"./Community-Dw1micSV.js";import{T as xl}from"./Trash-RbZEwH-j.js";import{M as Ml}from"./Music-CDm0RGMk.js";import{B as ei}from"./Block-CB3nIXHA.js";import{B as ti}from"./BlockLabel-CCoHIDM7.js";import{S as ni}from"./index-DrEzyPwM.js";import"./StreamingBar.svelte_svelte_type_style_lang-CxOfZBE-.js";import"./file-url-DgijyRSD.js";import"./context-TgWPFwN2.js";import"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";import"./Clear-By3xiIwg.js";const{SvelteComponent:li,append:pn,attr:G,detach:ii,init:oi,insert:si,noop:xt,safe_not_equal:ai,svg_element:en}=window.__gradio__svelte__internal;function ri(l){let e,t,n;return{c(){e=en("svg"),t=en("path"),n=en("path"),G(t,"fill","currentColor"),G(t,"d","M17.74 30L16 29l4-7h6a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h9v2H6a4 4 0 0 1-4-4V8a4 4 0 0 1 4-4h20a4 4 0 0 1 4 4v12a4 4 0 0 1-4 4h-4.84Z"),G(n,"fill","currentColor"),G(n,"d","M8 10h16v2H8zm0 6h10v2H8z"),G(e,"xmlns","http://www.w3.org/2000/svg"),G(e,"xmlns:xlink","http://www.w3.org/1999/xlink"),G(e,"aria-hidden","true"),G(e,"role","img"),G(e,"class","iconify iconify--carbon"),G(e,"width","100%"),G(e,"height","100%"),G(e,"preserveAspectRatio","xMidYMid meet"),G(e,"viewBox","0 0 32 32")},m(i,o){si(i,e,o),pn(e,t),pn(e,n)},p:xt,i:xt,o:xt,d(i){i&&ii(e)}}}class _i extends li{constructor(e){super(),oi(this,e,null,ri,ai,{})}}const{SvelteComponent:ui,append:Mt,attr:M,detach:fi,init:ci,insert:mi,noop:tn,safe_not_equal:hi,svg_element:ht}=window.__gradio__svelte__internal;function di(l){let e,t,n,i,o;return{c(){e=ht("svg"),t=ht("path"),n=ht("path"),i=ht("path"),o=ht("path"),M(t,"d","M19.1679 9C18.0247 6.46819 15.3006 4.5 11.9999 4.5C8.31459 4.5 5.05104 7.44668 4.54932 11"),M(t,"stroke","currentColor"),M(t,"stroke-width","1.5"),M(t,"stroke-linecap","round"),M(t,"stroke-linejoin","round"),M(n,"d","M16 9H19.4C19.7314 9 20 8.73137 20 8.4V5"),M(n,"stroke","currentColor"),M(n,"stroke-width","1.5"),M(n,"stroke-linecap","round"),M(n,"stroke-linejoin","round"),M(i,"d","M4.88146 15C5.92458 17.5318 8.64874 19.5 12.0494 19.5C15.7347 19.5 18.9983 16.5533 19.5 13"),M(i,"stroke","currentColor"),M(i,"stroke-width","1.5"),M(i,"stroke-linecap","round"),M(i,"stroke-linejoin","round"),M(o,"d","M8.04932 15H4.64932C4.31795 15 4.04932 15.2686 4.04932 15.6V19"),M(o,"stroke","currentColor"),M(o,"stroke-width","1.5"),M(o,"stroke-linecap","round"),M(o,"stroke-linejoin","round"),M(e,"width","100%"),M(e,"height","100%"),M(e,"stroke-width","1.5"),M(e,"viewBox","0 0 24 24"),M(e,"fill","none"),M(e,"xmlns","http://www.w3.org/2000/svg"),M(e,"color","currentColor")},m(s,r){mi(s,e,r),Mt(e,t),Mt(e,n),Mt(e,i),Mt(e,o)},p:tn,i:tn,o:tn,d(s){s&&fi(e)}}}class gi extends ui{constructor(e){super(),ci(this,e,null,di,hi,{})}}const{SvelteComponent:bi,append:pi,attr:ae,detach:wi,init:vi,insert:ki,noop:nn,safe_not_equal:$i,svg_element:wn}=window.__gradio__svelte__internal;function yi(l){let e,t;return{c(){e=wn("svg"),t=wn("path"),ae(t,"d","M12 20L12 4M12 20L7 15M12 20L17 15"),ae(t,"stroke","currentColor"),ae(t,"stroke-width","2"),ae(t,"stroke-linecap","round"),ae(t,"stroke-linejoin","round"),ae(e,"width","100%"),ae(e,"height","100%"),ae(e,"viewBox","0 0 24 24"),ae(e,"fill","none"),ae(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){ki(n,e,i),pi(e,t)},p:nn,i:nn,o:nn,d(n){n&&wi(e)}}}class Ci extends bi{constructor(e){super(),vi(this,e,null,yi,$i,{})}}const zi=async l=>(await Promise.all(l.map(async t=>{if(t.role==="system")return"";let n=t.role==="user"?"😃":"🤖",i="";if(t.type==="text"){const o={audio:/<audio.*?src="(\/file=.*?)"/g,video:/<video.*?src="(\/file=.*?)"/g,image:/<img.*?src="(\/file=.*?)".*?\/>|!\[.*?\]\((\/file=.*?)\)/g};i=t.content;for(let[s,r]of Object.entries(o)){let a;for(;(a=r.exec(t.content))!==null;){const _=a[1]||a[2],u=await bn(_);i=i.replace(_,u)}}}else{if(!t.content.value)return"";const o=t.content.component==="video"?t.content.value?.video.path:t.content.value,s=await bn(o);t.content.component==="audio"?i=`<audio controls src="${s}"></audio>`:t.content.component==="video"?i=s:t.content.component==="image"&&(i=`<img src="${s}" />`)}return`${n}: ${i}`}))).join(`
`),Vl=(l,e)=>l.replace('src="/file',`src="${e}file`);function Si(l){return l?l.includes("audio")?"audio":l.includes("video")?"video":l.includes("image")?"image":"file":"file"}function Ll(l){const e=Array.isArray(l.file)?l.file[0]:l.file;return{component:Si(e?.mime_type),value:l.file,alt_text:l.alt_text,constructor_args:{},props:{}}}function Hi(l,e){return l===null?l:l.map((t,n)=>typeof t.content=="string"?{role:t.role,metadata:t.metadata,content:Vl(t.content,e),type:"text",index:n,options:t.options}:"file"in t.content?{content:Ll(t.content),metadata:t.metadata,role:t.role,type:"component",index:n,options:t.options}:{type:"component",...t})}function qi(l,e){return l===null?l:l.flatMap((n,i)=>n.map((o,s)=>{if(o==null)return null;const r=s==0?"user":"assistant";return typeof o=="string"?{role:r,type:"text",content:Vl(o,e),metadata:{title:null},index:[i,s]}:"file"in o?{content:Ll(o),role:r,type:"component",index:[i,s]}:{role:r,content:o,type:"component",index:[i,s]}})).filter(n=>n!=null)}function fn(l){return l.type==="component"}function st(l,e){const t=l[l.length-1].role==="assistant",n=l[l.length-1].index;return JSON.stringify(n)===JSON.stringify(e[e.length-1].index)&&t}function Mi(l,e){const t=[];let n=[],i=null;for(const o of l)(o.role==="assistant"||o.role==="user")&&(o.role===i?n.push(o):(n.length>0&&t.push(n),n=[o],i=o.role));return n.length>0&&t.push(n),t}async function Vi(l,e,t){let n=[],i=[];return l.forEach(s=>{if(e[s]||s==="file")return;const{name:r,component:a}=t(s,"base");n.push(r),i.push(a)}),(await Promise.all(i)).forEach((s,r)=>{e[n[r]]=s.default}),e}function Li(l){if(!l)return[];let e=new Set;return l.forEach(t=>{t.type==="component"&&e.add(t.content.component)}),Array.from(e)}const{SvelteComponent:ji,attr:Bi,bubble:it,check_outros:et,construct_svelte_component:K,create_component:Q,destroy_component:X,detach:qe,element:Ni,empty:tt,flush:te,group_outros:nt,init:Ei,insert:Me,mount_component:x,noop:Ii,safe_not_equal:Ai,transition_in:R,transition_out:O}=window.__gradio__svelte__internal;function Ti(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,label:"chatbot-image",show_share_button:!0,i18n:s[6],gradio:{dispatch:Ji}}}}return i&&(e=K(i,o(l)),e.$on("load",l[16])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[16]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&64&&(a.i18n=s[6]),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Di(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,label:"chatbot-image",show_download_button:s[9],display_icon_button_wrapper_top_corner:s[10],i18n:s[6]}}}return i&&(e=K(i,o(l)),e.$on("load",l[15])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[15]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&512&&(a.show_download_button=s[9]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&64&&(a.i18n=s[6]),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Zi(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{autoplay:s[5].autoplay,value:s[2].video||s[2],show_label:!1,show_share_button:!0,i18n:s[6],upload:s[7],display_icon_button_wrapper_top_corner:s[10],show_download_button:s[9],$$slots:{default:[Ri]},$$scope:{ctx:s}}}}return i&&(e=K(i,o(l)),e.$on("load",l[14])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[14]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&32&&(a.autoplay=s[5].autoplay),r&4&&(a.value=s[2].video||s[2]),r&64&&(a.i18n=s[6]),r&128&&(a.upload=s[7]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&512&&(a.show_download_button=s[9]),r&131072&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Fi(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],show_label:!1,show_share_button:!0,i18n:s[6],label:"",waveform_settings:{autoplay:s[5].autoplay},waveform_options:{},show_download_button:s[9],display_icon_button_wrapper_top_corner:s[10]}}}return i&&(e=K(i,o(l)),e.$on("load",l[13])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[13]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&64&&(a.i18n=s[6]),r&32&&(a.waveform_settings={autoplay:s[5].autoplay}),r&512&&(a.show_download_button=s[9]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Ui(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],target:s[3],theme_mode:s[4],bokeh_version:s[5].bokeh_version,caption:"",show_actions_button:!0}}}return i&&(e=K(i,o(l)),e.$on("load",l[12])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[12]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&8&&(a.target=s[3]),r&16&&(a.theme_mode=s[4]),r&32&&(a.bokeh_version=s[5].bokeh_version),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Pi(l){let e,t,n;var i=l[1][l[0]];function o(s,r){return{props:{value:s[2],display_icon_button_wrapper_top_corner:s[10],show_label:!1,i18n:s[6],label:"",_fetch:s[8],allow_preview:!1,interactive:!1,mode:"minimal",fixed_height:1}}}return i&&(e=K(i,o(l)),e.$on("load",l[11])),{c(){e&&Q(e.$$.fragment),t=tt()},m(s,r){e&&x(e,s,r),Me(s,t,r),n=!0},p(s,r){if(r&3&&i!==(i=s[1][s[0]])){if(e){nt();const a=e;O(a.$$.fragment,1,0,()=>{X(a,1)}),et()}i?(e=K(i,o(s)),e.$on("load",s[11]),Q(e.$$.fragment),R(e.$$.fragment,1),x(e,t.parentNode,t)):e=null}else if(i){const a={};r&4&&(a.value=s[2]),r&1024&&(a.display_icon_button_wrapper_top_corner=s[10]),r&64&&(a.i18n=s[6]),r&256&&(a._fetch=s[8]),e.$set(a)}},i(s){n||(e&&R(e.$$.fragment,s),n=!0)},o(s){e&&O(e.$$.fragment,s),n=!1},d(s){s&&qe(t),e&&X(e,s)}}}function Ri(l){let e;return{c(){e=Ni("track"),Bi(e,"kind","captions")},m(t,n){Me(t,e,n)},p:Ii,d(t){t&&qe(e)}}}function Oi(l){let e,t,n,i;const o=[Pi,Ui,Fi,Zi,Di,Ti],s=[];function r(a,_){return a[0]==="gallery"?0:a[0]==="plot"?1:a[0]==="audio"?2:a[0]==="video"?3:a[0]==="image"?4:a[0]==="html"?5:-1}return~(e=r(l))&&(t=s[e]=o[e](l)),{c(){t&&t.c(),n=tt()},m(a,_){~e&&s[e].m(a,_),Me(a,n,_),i=!0},p(a,[_]){let u=e;e=r(a),e===u?~e&&s[e].p(a,_):(t&&(nt(),O(s[u],1,1,()=>{s[u]=null}),et()),~e?(t=s[e],t?t.p(a,_):(t=s[e]=o[e](a),t.c()),R(t,1),t.m(n.parentNode,n)):t=null)},i(a){i||(R(t),i=!0)},o(a){O(t),i=!1},d(a){a&&qe(n),~e&&s[e].d(a)}}}const Ji=()=>{};function Gi(l,e,t){let{type:n}=e,{components:i}=e,{value:o}=e,{target:s}=e,{theme_mode:r}=e,{props:a}=e,{i18n:_}=e,{upload:u}=e,{_fetch:c}=e,{allow_file_downloads:b}=e,{display_icon_button_wrapper_top_corner:h=!1}=e;function d($){it.call(this,l,$)}function p($){it.call(this,l,$)}function k($){it.call(this,l,$)}function m($){it.call(this,l,$)}function w($){it.call(this,l,$)}function v($){it.call(this,l,$)}return l.$$set=$=>{"type"in $&&t(0,n=$.type),"components"in $&&t(1,i=$.components),"value"in $&&t(2,o=$.value),"target"in $&&t(3,s=$.target),"theme_mode"in $&&t(4,r=$.theme_mode),"props"in $&&t(5,a=$.props),"i18n"in $&&t(6,_=$.i18n),"upload"in $&&t(7,u=$.upload),"_fetch"in $&&t(8,c=$._fetch),"allow_file_downloads"in $&&t(9,b=$.allow_file_downloads),"display_icon_button_wrapper_top_corner"in $&&t(10,h=$.display_icon_button_wrapper_top_corner)},[n,i,o,s,r,a,_,u,c,b,h,d,p,k,m,w,v]}class Wi extends ji{constructor(e){super(),Ei(this,e,Gi,Oi,Ai,{type:0,components:1,value:2,target:3,theme_mode:4,props:5,i18n:6,upload:7,_fetch:8,allow_file_downloads:9,display_icon_button_wrapper_top_corner:10})}get type(){return this.$$.ctx[0]}set type(e){this.$$set({type:e}),te()}get components(){return this.$$.ctx[1]}set components(e){this.$$set({components:e}),te()}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),te()}get target(){return this.$$.ctx[3]}set target(e){this.$$set({target:e}),te()}get theme_mode(){return this.$$.ctx[4]}set theme_mode(e){this.$$set({theme_mode:e}),te()}get props(){return this.$$.ctx[5]}set props(e){this.$$set({props:e}),te()}get i18n(){return this.$$.ctx[6]}set i18n(e){this.$$set({i18n:e}),te()}get upload(){return this.$$.ctx[7]}set upload(e){this.$$set({upload:e}),te()}get _fetch(){return this.$$.ctx[8]}set _fetch(e){this.$$set({_fetch:e}),te()}get allow_file_downloads(){return this.$$.ctx[9]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),te()}get display_icon_button_wrapper_top_corner(){return this.$$.ctx[10]}set display_icon_button_wrapper_top_corner(e){this.$$set({display_icon_button_wrapper_top_corner:e}),te()}}const{SvelteComponent:Yi,append:ot,attr:gt,check_outros:Ki,create_slot:Qi,detach:jl,element:bt,flush:vn,get_all_dirty_from_scope:Xi,get_slot_changes:xi,group_outros:eo,init:to,insert:Bl,listen:no,safe_not_equal:lo,set_data:io,set_style:kn,space:$n,text:oo,transition_in:jt,transition_out:cn,update_slot_base:so}=window.__gradio__svelte__internal;function yn(l){let e,t;const n=l[4].default,i=Qi(n,l,l[3],null);return{c(){e=bt("div"),i&&i.c(),gt(e,"class","content svelte-1e60bn1")},m(o,s){Bl(o,e,s),i&&i.m(e,null),t=!0},p(o,s){i&&i.p&&(!t||s&8)&&so(i,n,o,o[3],t?xi(n,o[3],s,null):Xi(o[3]),null)},i(o){t||(jt(i,o),t=!0)},o(o){cn(i,o),t=!1},d(o){o&&jl(e),i&&i.d(o)}}}function ao(l){let e,t,n,i,o,s,r,a,_,u,c=l[0]&&yn(l);return{c(){e=bt("button"),t=bt("div"),n=bt("span"),i=oo(l[1]),o=$n(),s=bt("span"),s.textContent="▼",r=$n(),c&&c.c(),gt(n,"class","title-text svelte-1e60bn1"),gt(s,"class","arrow svelte-1e60bn1"),kn(s,"transform",l[0]?"rotate(0)":"rotate(90deg)"),gt(t,"class","title svelte-1e60bn1"),gt(e,"class","box svelte-1e60bn1")},m(b,h){Bl(b,e,h),ot(e,t),ot(t,n),ot(n,i),ot(t,o),ot(t,s),ot(e,r),c&&c.m(e,null),a=!0,_||(u=no(e,"click",l[2]),_=!0)},p(b,[h]){(!a||h&2)&&io(i,b[1]),h&1&&kn(s,"transform",b[0]?"rotate(0)":"rotate(90deg)"),b[0]?c?(c.p(b,h),h&1&&jt(c,1)):(c=yn(b),c.c(),jt(c,1),c.m(e,null)):c&&(eo(),cn(c,1,1,()=>{c=null}),Ki())},i(b){a||(jt(c),a=!0)},o(b){cn(c),a=!1},d(b){b&&jl(e),c&&c.d(),_=!1,u()}}}function ro(l,e,t){let{$$slots:n={},$$scope:i}=e,{expanded:o=!1}=e,{title:s}=e;function r(){t(0,o=!o)}return l.$$set=a=>{"expanded"in a&&t(0,o=a.expanded),"title"in a&&t(1,s=a.title),"$$scope"in a&&t(3,i=a.$$scope)},[o,s,r,i,n]}class _o extends Yi{constructor(e){super(),to(this,e,ro,ao,lo,{expanded:0,title:1})}get expanded(){return this.$$.ctx[0]}set expanded(e){this.$$set({expanded:e}),vn()}get title(){return this.$$.ctx[1]}set title(e){this.$$set({title:e}),vn()}}const{SvelteComponent:uo,append:fo,attr:Re,detach:co,init:mo,insert:ho,noop:ln,safe_not_equal:go,svg_element:Cn}=window.__gradio__svelte__internal;function bo(l){let e,t;return{c(){e=Cn("svg"),t=Cn("path"),Re(t,"d","M11.25 6.61523H9.375V1.36523H11.25V6.61523ZM3.375 1.36523H8.625V6.91636L7.48425 8.62748L7.16737 10.8464C7.14108 11.0248 7.05166 11.1879 6.91535 11.3061C6.77904 11.4242 6.60488 11.4896 6.4245 11.4902H6.375C6.07672 11.4899 5.79075 11.3713 5.57983 11.1604C5.36892 10.9495 5.2503 10.6635 5.25 10.3652V8.11523H2.25C1.85233 8.11474 1.47109 7.95654 1.18989 7.67535C0.908691 7.39415 0.750496 7.01291 0.75 6.61523V3.99023C0.750992 3.29435 1.02787 2.62724 1.51994 2.13517C2.01201 1.64311 2.67911 1.36623 3.375 1.36523Z"),Re(t,"fill","currentColor"),Re(e,"width","100%"),Re(e,"height","100%"),Re(e,"viewBox","0 0 12 12"),Re(e,"fill","none"),Re(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){ho(n,e,i),fo(e,t)},p:ln,i:ln,o:ln,d(n){n&&co(e)}}}class zn extends uo{constructor(e){super(),mo(this,e,null,bo,go,{})}}const{SvelteComponent:po,append:wo,attr:Oe,detach:vo,init:ko,insert:$o,noop:on,safe_not_equal:yo,svg_element:Sn}=window.__gradio__svelte__internal;function Co(l){let e,t;return{c(){e=Sn("svg"),t=Sn("path"),Oe(t,"d","M2.25 8.11523H4.5V10.3652C4.5003 10.6635 4.61892 10.9495 4.82983 11.1604C5.04075 11.3713 5.32672 11.4899 5.625 11.4902H6.42488C6.60519 11.4895 6.77926 11.4241 6.91549 11.3059C7.05172 11.1878 7.14109 11.0248 7.16737 10.8464L7.48425 8.62748L8.82562 6.61523H11.25V1.36523H3.375C2.67911 1.36623 2.01201 1.64311 1.51994 2.13517C1.02787 2.62724 0.750992 3.29435 0.75 3.99023V6.61523C0.750496 7.01291 0.908691 7.39415 1.18989 7.67535C1.47109 7.95654 1.85233 8.11474 2.25 8.11523ZM9 2.11523H10.5V5.86523H9V2.11523ZM1.5 3.99023C1.5006 3.49314 1.69833 3.01657 2.04983 2.66507C2.40133 2.31356 2.8779 2.11583 3.375 2.11523H8.25V6.12661L6.76575 8.35298L6.4245 10.7402H5.625C5.52554 10.7402 5.43016 10.7007 5.35983 10.6304C5.28951 10.5601 5.25 10.4647 5.25 10.3652V7.36523H2.25C2.05118 7.36494 1.86059 7.28582 1.72 7.14524C1.57941 7.00465 1.5003 6.81406 1.5 6.61523V3.99023Z"),Oe(t,"fill","currentColor"),Oe(e,"width","100%"),Oe(e,"height","100%"),Oe(e,"viewBox","0 0 12 12"),Oe(e,"fill","none"),Oe(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){$o(n,e,i),wo(e,t)},p:on,i:on,o:on,d(n){n&&vo(e)}}}class Hn extends po{constructor(e){super(),ko(this,e,null,Co,yo,{})}}const{SvelteComponent:zo,append:So,attr:Je,detach:Ho,init:qo,insert:Mo,noop:sn,safe_not_equal:Vo,svg_element:qn}=window.__gradio__svelte__internal;function Lo(l){let e,t;return{c(){e=qn("svg"),t=qn("path"),Je(t,"d","M0.75 6.24023H2.625V11.4902H0.75V6.24023ZM8.625 11.4902H3.375V5.93911L4.51575 4.22798L4.83263 2.00911C4.85892 1.83065 4.94834 1.66754 5.08465 1.5494C5.22096 1.43125 5.39512 1.36591 5.5755 1.36523H5.625C5.92328 1.36553 6.20925 1.48415 6.42017 1.69507C6.63108 1.90598 6.7497 2.19196 6.75 2.49023V4.74023H9.75C10.1477 4.74073 10.5289 4.89893 10.8101 5.18012C11.0913 5.46132 11.2495 5.84256 11.25 6.24023V8.86523C11.249 9.56112 10.9721 10.2282 10.4801 10.7203C9.98799 11.2124 9.32089 11.4892 8.625 11.4902Z"),Je(t,"fill","currentColor"),Je(e,"width","100%"),Je(e,"height","100%"),Je(e,"viewBox","0 0 12 12"),Je(e,"fill","none"),Je(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){Mo(n,e,i),So(e,t)},p:sn,i:sn,o:sn,d(n){n&&Ho(e)}}}class Mn extends zo{constructor(e){super(),qo(this,e,null,Lo,Vo,{})}}const{SvelteComponent:jo,append:Bo,attr:Ge,detach:No,init:Eo,insert:Io,noop:an,safe_not_equal:Ao,svg_element:Vn}=window.__gradio__svelte__internal;function To(l){let e,t;return{c(){e=Vn("svg"),t=Vn("path"),Ge(t,"d","M9.75 4.74023H7.5V2.49023C7.4997 2.19196 7.38108 1.90598 7.17017 1.69507C6.95925 1.48415 6.67328 1.36553 6.375 1.36523H5.57512C5.39481 1.366 5.22074 1.43138 5.08451 1.54952C4.94828 1.66766 4.85891 1.83072 4.83262 2.00911L4.51575 4.22798L3.17438 6.24023H0.75V11.4902H8.625C9.32089 11.4892 9.98799 11.2124 10.4801 10.7203C10.9721 10.2282 11.249 9.56112 11.25 8.86523V6.24023C11.2495 5.84256 11.0913 5.46132 10.8101 5.18012C10.5289 4.89893 10.1477 4.74073 9.75 4.74023ZM3 10.7402H1.5V6.99023H3V10.7402ZM10.5 8.86523C10.4994 9.36233 10.3017 9.8389 9.95017 10.1904C9.59867 10.5419 9.1221 10.7396 8.625 10.7402H3.75V6.72886L5.23425 4.50248L5.5755 2.11523H6.375C6.47446 2.11523 6.56984 2.15474 6.64017 2.22507C6.71049 2.2954 6.75 2.39078 6.75 2.49023V5.49023H9.75C9.94882 5.49053 10.1394 5.56965 10.28 5.71023C10.4206 5.85082 10.4997 6.04141 10.5 6.24023V8.86523Z"),Ge(t,"fill","currentColor"),Ge(e,"width","100%"),Ge(e,"height","100%"),Ge(e,"viewBox","0 0 12 12"),Ge(e,"fill","none"),Ge(e,"xmlns","http://www.w3.org/2000/svg")},m(n,i){Io(n,e,i),Bo(e,t)},p:an,i:an,o:an,d(n){n&&No(e)}}}class Ln extends jo{constructor(e){super(),Eo(this,e,null,To,Ao,{})}}const{SvelteComponent:Do,create_component:jn,destroy_component:Bn,detach:Zo,flush:Fo,init:Uo,insert:Po,mount_component:Nn,safe_not_equal:Ro,space:Oo,transition_in:En,transition_out:In}=window.__gradio__svelte__internal;function Jo(l){let e,t,n,i;return e=new ve({props:{Icon:l[1]==="dislike"?zn:Hn,label:l[1]==="dislike"?"clicked dislike":"dislike",color:l[1]==="dislike"?"var(--color-accent)":"var(--block-label-text-color)"}}),e.$on("click",l[2]),n=new ve({props:{Icon:l[1]==="like"?Mn:Ln,label:l[1]==="like"?"clicked like":"like",color:l[1]==="like"?"var(--color-accent)":"var(--block-label-text-color)"}}),n.$on("click",l[3]),{c(){jn(e.$$.fragment),t=Oo(),jn(n.$$.fragment)},m(o,s){Nn(e,o,s),Po(o,t,s),Nn(n,o,s),i=!0},p(o,[s]){const r={};s&2&&(r.Icon=o[1]==="dislike"?zn:Hn),s&2&&(r.label=o[1]==="dislike"?"clicked dislike":"dislike"),s&2&&(r.color=o[1]==="dislike"?"var(--color-accent)":"var(--block-label-text-color)"),e.$set(r);const a={};s&2&&(a.Icon=o[1]==="like"?Mn:Ln),s&2&&(a.label=o[1]==="like"?"clicked like":"like"),s&2&&(a.color=o[1]==="like"?"var(--color-accent)":"var(--block-label-text-color)"),n.$set(a)},i(o){i||(En(e.$$.fragment,o),En(n.$$.fragment,o),i=!0)},o(o){In(e.$$.fragment,o),In(n.$$.fragment,o),i=!1},d(o){o&&Zo(t),Bn(e,o),Bn(n,o)}}}function Go(l,e,t){let{handle_action:n}=e,i=null;const o=()=>{t(1,i="dislike"),n(i)},s=()=>{t(1,i="like"),n(i)};return l.$$set=r=>{"handle_action"in r&&t(0,n=r.handle_action)},[n,i,o,s]}class Wo extends Do{constructor(e){super(),Uo(this,e,Go,Jo,Ro,{handle_action:0})}get handle_action(){return this.$$.ctx[0]}set handle_action(e){this.$$set({handle_action:e}),Fo()}}const{SvelteComponent:Yo,create_component:Ko,destroy_component:Qo,flush:Xo,init:xo,mount_component:es,safe_not_equal:ts,transition_in:ns,transition_out:ls}=window.__gradio__svelte__internal,{createEventDispatcher:is}=window.__gradio__svelte__internal,{onDestroy:os}=window.__gradio__svelte__internal;function ss(l){let e,t;return e=new ve({props:{label:l[0]?"Copied message":"Copy message",Icon:l[0]?Et:It}}),e.$on("click",l[1]),{c(){Ko(e.$$.fragment)},m(n,i){es(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.label=n[0]?"Copied message":"Copy message"),i&1&&(o.Icon=n[0]?Et:It),e.$set(o)},i(n){t||(ns(e.$$.fragment,n),t=!0)},o(n){ls(e.$$.fragment,n),t=!1},d(n){Qo(e,n)}}}function as(l,e,t){const n=is();let i=!1,{value:o}=e,s;function r(){t(0,i=!0),s&&clearTimeout(s),s=setTimeout(()=>{t(0,i=!1)},2e3)}async function a(){if("clipboard"in navigator)n("copy",{value:o}),await navigator.clipboard.writeText(o),r();else{const _=document.createElement("textarea");_.value=o,_.style.position="absolute",_.style.left="-999999px",document.body.prepend(_),_.select();try{document.execCommand("copy"),r()}catch(u){console.error(u)}finally{_.remove()}}}return os(()=>{s&&clearTimeout(s)}),l.$$set=_=>{"value"in _&&t(2,o=_.value)},[i,a,o]}class rs extends Yo{constructor(e){super(),xo(this,e,as,ss,ts,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),Xo()}}const{SvelteComponent:_s,attr:An,check_outros:pt,create_component:kt,destroy_component:$t,detach:at,element:us,empty:Nl,flush:ne,group_outros:wt,init:fs,insert:rt,mount_component:yt,safe_not_equal:cs,space:rn,transition_in:A,transition_out:P}=window.__gradio__svelte__internal;function Tn(l){let e,t,n,i;return t=new ql({props:{top_panel:!1,$$slots:{default:[ms]},$$scope:{ctx:l}}}),{c(){e=us("div"),kt(t.$$.fragment),An(e,"class",n="message-buttons-"+l[3]+" "+l[7]+" message-buttons "+(l[4]!==null&&"with-avatar")+" svelte-5c5t6x")},m(o,s){rt(o,e,s),yt(t,e,null),i=!0},p(o,s){const r={};s&132967&&(r.$$scope={dirty:s,ctx:o}),t.$set(r),(!i||s&152&&n!==(n="message-buttons-"+o[3]+" "+o[7]+" message-buttons "+(o[4]!==null&&"with-avatar")+" svelte-5c5t6x"))&&An(e,"class",n)},i(o){i||(A(t.$$.fragment,o),i=!0)},o(o){P(t.$$.fragment,o),i=!1},d(o){o&&at(e),$t(t)}}}function Dn(l){let e,t;return e=new rs({props:{value:l[10]}}),e.$on("copy",l[13]),{c(){kt(e.$$.fragment)},m(n,i){yt(e,n,i),t=!0},p(n,i){const o={};i&1024&&(o.value=n[10]),e.$set(o)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function Zn(l){let e,t;return e=new ve({props:{Icon:gi,label:"Retry",disabled:l[5]}}),e.$on("click",l[14]),{c(){kt(e.$$.fragment)},m(n,i){yt(e,n,i),t=!0},p(n,i){const o={};i&32&&(o.disabled=n[5]),e.$set(o)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function Fn(l){let e,t;return e=new ve({props:{label:"Undo",Icon:Kl,disabled:l[5]}}),e.$on("click",l[15]),{c(){kt(e.$$.fragment)},m(n,i){yt(e,n,i),t=!0},p(n,i){const o={};i&32&&(o.disabled=n[5]),e.$set(o)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function Un(l){let e,t;return e=new Wo({props:{handle_action:l[6]}}),{c(){kt(e.$$.fragment)},m(n,i){yt(e,n,i),t=!0},p(n,i){const o={};i&64&&(o.handle_action=n[6]),e.$set(o)},i(n){t||(A(e.$$.fragment,n),t=!0)},o(n){P(e.$$.fragment,n),t=!1},d(n){$t(e,n)}}}function ms(l){let e,t,n,i,o,s=l[9]&&Dn(l),r=l[1]&&Zn(l),a=l[2]&&Fn(l),_=l[0]&&Un(l);return{c(){s&&s.c(),e=rn(),r&&r.c(),t=rn(),a&&a.c(),n=rn(),_&&_.c(),i=Nl()},m(u,c){s&&s.m(u,c),rt(u,e,c),r&&r.m(u,c),rt(u,t,c),a&&a.m(u,c),rt(u,n,c),_&&_.m(u,c),rt(u,i,c),o=!0},p(u,c){u[9]?s?(s.p(u,c),c&512&&A(s,1)):(s=Dn(u),s.c(),A(s,1),s.m(e.parentNode,e)):s&&(wt(),P(s,1,1,()=>{s=null}),pt()),u[1]?r?(r.p(u,c),c&2&&A(r,1)):(r=Zn(u),r.c(),A(r,1),r.m(t.parentNode,t)):r&&(wt(),P(r,1,1,()=>{r=null}),pt()),u[2]?a?(a.p(u,c),c&4&&A(a,1)):(a=Fn(u),a.c(),A(a,1),a.m(n.parentNode,n)):a&&(wt(),P(a,1,1,()=>{a=null}),pt()),u[0]?_?(_.p(u,c),c&1&&A(_,1)):(_=Un(u),_.c(),A(_,1),_.m(i.parentNode,i)):_&&(wt(),P(_,1,1,()=>{_=null}),pt())},i(u){o||(A(s),A(r),A(a),A(_),o=!0)},o(u){P(s),P(r),P(a),P(_),o=!1},d(u){u&&(at(e),at(t),at(n),at(i)),s&&s.d(u),r&&r.d(u),a&&a.d(u),_&&_.d(u)}}}function hs(l){let e,t,n=(l[9]||l[1]||l[2]||l[0])&&Tn(l);return{c(){n&&n.c(),e=Nl()},m(i,o){n&&n.m(i,o),rt(i,e,o),t=!0},p(i,[o]){i[9]||i[1]||i[2]||i[0]?n?(n.p(i,o),o&519&&A(n,1)):(n=Tn(i),n.c(),A(n,1),n.m(e.parentNode,e)):n&&(wt(),P(n,1,1,()=>{n=null}),pt())},i(i){t||(A(n),t=!0)},o(i){P(n),t=!1},d(i){i&&at(e),n&&n.d(i)}}}function Pn(l){return Array.isArray(l)&&l.every(e=>typeof e.content=="string")||!Array.isArray(l)&&typeof l.content=="string"}function ds(l){return Array.isArray(l)?l.map(e=>e.content).join(`
`):l.content}function gs(l,e,t){let n,i,{likeable:o}=e,{show_retry:s}=e,{show_undo:r}=e,{show_copy_button:a}=e,{message:_}=e,{position:u}=e,{avatar:c}=e,{generating:b}=e,{handle_action:h}=e,{layout:d}=e,{dispatch:p}=e;const k=v=>p("copy",v.detail),m=()=>h("retry"),w=()=>h("undo");return l.$$set=v=>{"likeable"in v&&t(0,o=v.likeable),"show_retry"in v&&t(1,s=v.show_retry),"show_undo"in v&&t(2,r=v.show_undo),"show_copy_button"in v&&t(11,a=v.show_copy_button),"message"in v&&t(12,_=v.message),"position"in v&&t(3,u=v.position),"avatar"in v&&t(4,c=v.avatar),"generating"in v&&t(5,b=v.generating),"handle_action"in v&&t(6,h=v.handle_action),"layout"in v&&t(7,d=v.layout),"dispatch"in v&&t(8,p=v.dispatch)},l.$$.update=()=>{l.$$.dirty&4096&&t(10,n=Pn(_)?ds(_):""),l.$$.dirty&6144&&t(9,i=a&&_&&Pn(_)),l.$$.dirty&4096&&!Array.isArray(_)&&fn(_)&&_.content.value?.url},[o,s,r,u,c,b,h,d,p,i,n,a,_,k,m,w]}class El extends _s{constructor(e){super(),fs(this,e,gs,hs,cs,{likeable:0,show_retry:1,show_undo:2,show_copy_button:11,message:12,position:3,avatar:4,generating:5,handle_action:6,layout:7,dispatch:8})}get likeable(){return this.$$.ctx[0]}set likeable(e){this.$$set({likeable:e}),ne()}get show_retry(){return this.$$.ctx[1]}set show_retry(e){this.$$set({show_retry:e}),ne()}get show_undo(){return this.$$.ctx[2]}set show_undo(e){this.$$set({show_undo:e}),ne()}get show_copy_button(){return this.$$.ctx[11]}set show_copy_button(e){this.$$set({show_copy_button:e}),ne()}get message(){return this.$$.ctx[12]}set message(e){this.$$set({message:e}),ne()}get position(){return this.$$.ctx[3]}set position(e){this.$$set({position:e}),ne()}get avatar(){return this.$$.ctx[4]}set avatar(e){this.$$set({avatar:e}),ne()}get generating(){return this.$$.ctx[5]}set generating(e){this.$$set({generating:e}),ne()}get handle_action(){return this.$$.ctx[6]}set handle_action(e){this.$$set({handle_action:e}),ne()}get layout(){return this.$$.ctx[7]}set layout(e){this.$$set({layout:e}),ne()}get dispatch(){return this.$$.ctx[8]}set dispatch(e){this.$$set({dispatch:e}),ne()}}const{SvelteComponent:bs,append:U,assign:Il,attr:H,check_outros:_t,create_component:Ve,destroy_component:Le,destroy_each:ps,detach:We,element:W,empty:ws,ensure_array_like:Rn,flush:z,get_spread_object:Al,get_spread_update:Tl,group_outros:ut,init:vs,insert:Ye,is_function:Dl,listen:On,mount_component:je,null_to_empty:Jn,run_all:ks,safe_not_equal:$s,set_data:Gn,set_style:dt,space:ft,text:Wn,toggle_class:L,transition_in:j,transition_out:T}=window.__gradio__svelte__internal;function Yn(l,e,t){const n=l.slice();return n[37]=e[t],n[39]=t,n}function Kn(l){let e,t,n;return t=new vt({props:{class:"avatar-image",src:l[1]?.url,alt:l[3]+" avatar"}}),{c(){e=W("div"),Ve(t.$$.fragment),H(e,"class","avatar-container svelte-1uhz716")},m(i,o){Ye(i,e,o),je(t,e,null),n=!0},p(i,o){const s={};o[0]&2&&(s.src=i[1]?.url),o[0]&8&&(s.alt=i[3]+" avatar"),t.$set(s)},i(i){n||(j(t.$$.fragment,i),n=!0)},o(i){T(t.$$.fragment,i),n=!1},d(i){i&&We(e),Le(t)}}}function ys(l){let e,t,n,i,o,s,r,a=(l[37].content.value?.orig_name||l[37].content.value?.path.split("/").pop()||"file")+"",_,u,c,b,h,d=(l[37].content.value?.orig_name||l[37].content.value?.path||"").split(".").pop().toUpperCase()+"",p,k;return n=new dn({}),{c(){e=W("div"),t=W("div"),Ve(n.$$.fragment),i=ft(),o=W("div"),s=W("a"),r=W("span"),_=Wn(a),b=ft(),h=W("span"),p=Wn(d),H(t,"class","file-icon svelte-1uhz716"),H(r,"class","file-name svelte-1uhz716"),H(s,"data-testid","chatbot-file"),H(s,"class","file-link svelte-1uhz716"),H(s,"href",u=l[37].content.value.url),H(s,"target","_blank"),H(s,"download",c=window.__is_colab__?null:l[37].content.value?.orig_name||l[37].content.value?.path.split("/").pop()||"file"),H(h,"class","file-type svelte-1uhz716"),H(o,"class","file-info svelte-1uhz716"),H(e,"class","file-container svelte-1uhz716")},m(m,w){Ye(m,e,w),U(e,t),je(n,t,null),U(e,i),U(e,o),U(o,s),U(s,r),U(r,_),U(o,b),U(o,h),U(h,p),k=!0},p(m,w){(!k||w[0]&16)&&a!==(a=(m[37].content.value?.orig_name||m[37].content.value?.path.split("/").pop()||"file")+"")&&Gn(_,a),(!k||w[0]&16&&u!==(u=m[37].content.value.url))&&H(s,"href",u),(!k||w[0]&16&&c!==(c=window.__is_colab__?null:m[37].content.value?.orig_name||m[37].content.value?.path.split("/").pop()||"file"))&&H(s,"download",c),(!k||w[0]&16)&&d!==(d=(m[37].content.value?.orig_name||m[37].content.value?.path||"").split(".").pop().toUpperCase()+"")&&Gn(p,d)},i(m){k||(j(n.$$.fragment,m),k=!0)},o(m){T(n.$$.fragment,m),k=!1},d(m){m&&We(e),Le(n)}}}function Cs(l){let e,t;return e=new Wi({props:{target:l[16],theme_mode:l[18],props:l[37].content.props,type:l[37].content.component,components:l[19],value:l[37].content.value,display_icon_button_wrapper_top_corner:l[39]>0&&l[23],i18n:l[13],upload:l[15],_fetch:l[10],allow_file_downloads:l[22]}}),e.$on("load",l[33]),{c(){Ve(e.$$.fragment)},m(n,i){je(e,n,i),t=!0},p(n,i){const o={};i[0]&65536&&(o.target=n[16]),i[0]&262144&&(o.theme_mode=n[18]),i[0]&16&&(o.props=n[37].content.props),i[0]&16&&(o.type=n[37].content.component),i[0]&524288&&(o.components=n[19]),i[0]&16&&(o.value=n[37].content.value),i[0]&8388608&&(o.display_icon_button_wrapper_top_corner=n[39]>0&&n[23]),i[0]&8192&&(o.i18n=n[13]),i[0]&32768&&(o.upload=n[15]),i[0]&1024&&(o._fetch=n[10]),i[0]&4194304&&(o.allow_file_downloads=n[22]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){Le(e,n)}}}function zs(l){let e,t,n,i;const o=[Hs,Ss],s=[];function r(a,_){return a[37]?.metadata?.title?0:1}return t=r(l),n=s[t]=o[t](l),{c(){e=W("div"),n.c(),H(e,"class","message-content svelte-1uhz716")},m(a,_){Ye(a,e,_),s[t].m(e,null),i=!0},p(a,_){let u=t;t=r(a),t===u?s[t].p(a,_):(ut(),T(s[u],1,1,()=>{s[u]=null}),_t(),n=s[t],n?n.p(a,_):(n=s[t]=o[t](a),n.c()),j(n,1),n.m(e,null))},i(a){i||(j(n),i=!0)},o(a){T(n),i=!1},d(a){a&&We(e),s[t].d()}}}function Ss(l){let e,t;return e=new gn({props:{message:l[37].content,latex_delimiters:l[7],sanitize_html:l[8],render_markdown:l[6],line_breaks:l[14],root:l[17]}}),e.$on("load",function(){Dl(l[21])&&l[21].apply(this,arguments)}),{c(){Ve(e.$$.fragment)},m(n,i){je(e,n,i),t=!0},p(n,i){l=n;const o={};i[0]&16&&(o.message=l[37].content),i[0]&128&&(o.latex_delimiters=l[7]),i[0]&256&&(o.sanitize_html=l[8]),i[0]&64&&(o.render_markdown=l[6]),i[0]&16384&&(o.line_breaks=l[14]),i[0]&131072&&(o.root=l[17]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){Le(e,n)}}}function Hs(l){let e,t;return e=new _o({props:{title:l[37].metadata.title,expanded:st([l[37]],l[0]),$$slots:{default:[qs]},$$scope:{ctx:l}}}),{c(){Ve(e.$$.fragment)},m(n,i){je(e,n,i),t=!0},p(n,i){const o={};i[0]&16&&(o.title=n[37].metadata.title),i[0]&17&&(o.expanded=st([n[37]],n[0])),i[0]&2245072|i[1]&512&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){Le(e,n)}}}function qs(l){let e,t;return e=new gn({props:{message:l[37].content,latex_delimiters:l[7],sanitize_html:l[8],render_markdown:l[6],line_breaks:l[14],root:l[17]}}),e.$on("load",function(){Dl(l[21])&&l[21].apply(this,arguments)}),{c(){Ve(e.$$.fragment)},m(n,i){je(e,n,i),t=!0},p(n,i){l=n;const o={};i[0]&16&&(o.message=l[37].content),i[0]&128&&(o.latex_delimiters=l[7]),i[0]&256&&(o.sanitize_html=l[8]),i[0]&64&&(o.render_markdown=l[6]),i[0]&16384&&(o.line_breaks=l[14]),i[0]&131072&&(o.root=l[17]),e.$set(o)},i(n){t||(j(e.$$.fragment,n),t=!0)},o(n){T(e.$$.fragment,n),t=!1},d(n){Le(e,n)}}}function Qn(l){let e,t,n,i,o,s,r,a,_,u,c;const b=[zs,Cs,ys],h=[];function d(m,w){return m[37].type==="text"?0:m[37].type==="component"&&m[37].content.component in m[19]?1:m[37].type==="component"&&m[37].content.component==="file"?2:-1}~(n=d(l))&&(i=h[n]=b[n](l));function p(){return l[34](l[37])}function k(...m){return l[35](l[37],...m)}return{c(){e=W("div"),t=W("button"),i&&i.c(),r=ft(),H(t,"data-testid",l[3]),H(t,"dir",o=l[11]?"rtl":"ltr"),H(t,"aria-label",s=l[3]+"'s message: "+el(l[37])),H(t,"class","svelte-1uhz716"),L(t,"latest",l[20]===l[0].length-1),L(t,"message-markdown-disabled",!l[6]),L(t,"selectable",l[9]),dt(t,"user-select","text"),dt(t,"cursor",l[9]?"pointer":"default"),dt(t,"text-align",l[11]?"right":"left"),H(e,"class",a="message "+(l[23]?"":l[3])+" svelte-1uhz716"),L(e,"panel-full-width",!0),L(e,"message-markdown-disabled",!l[6]),L(e,"component",l[37].type==="component"),L(e,"html",fn(l[37])&&l[37].content.component==="html"),L(e,"thought",l[39]>0)},m(m,w){Ye(m,e,w),U(e,t),~n&&h[n].m(t,null),U(e,r),_=!0,u||(c=[On(t,"click",p),On(t,"keydown",k)],u=!0)},p(m,w){l=m;let v=n;n=d(l),n===v?~n&&h[n].p(l,w):(i&&(ut(),T(h[v],1,1,()=>{h[v]=null}),_t()),~n?(i=h[n],i?i.p(l,w):(i=h[n]=b[n](l),i.c()),j(i,1),i.m(t,null)):i=null),(!_||w[0]&8)&&H(t,"data-testid",l[3]),(!_||w[0]&2048&&o!==(o=l[11]?"rtl":"ltr"))&&H(t,"dir",o),(!_||w[0]&24&&s!==(s=l[3]+"'s message: "+el(l[37])))&&H(t,"aria-label",s),(!_||w[0]&1048577)&&L(t,"latest",l[20]===l[0].length-1),(!_||w[0]&64)&&L(t,"message-markdown-disabled",!l[6]),(!_||w[0]&512)&&L(t,"selectable",l[9]),w[0]&512&&dt(t,"cursor",l[9]?"pointer":"default"),w[0]&2048&&dt(t,"text-align",l[11]?"right":"left"),(!_||w[0]&8388616&&a!==(a="message "+(l[23]?"":l[3])+" svelte-1uhz716"))&&H(e,"class",a),(!_||w[0]&8388616)&&L(e,"panel-full-width",!0),(!_||w[0]&8388680)&&L(e,"message-markdown-disabled",!l[6]),(!_||w[0]&8388632)&&L(e,"component",l[37].type==="component"),(!_||w[0]&8388632)&&L(e,"html",fn(l[37])&&l[37].content.component==="html"),(!_||w[0]&8388616)&&L(e,"thought",l[39]>0)},i(m){_||(j(i),_=!0)},o(m){T(i),_=!1},d(m){m&&We(e),~n&&h[n].d(),u=!1,ks(c)}}}function Xn(l){let e,t;const n=[l[24]];let i={};for(let o=0;o<n.length;o+=1)i=Il(i,n[o]);return e=new El({props:i}),e.$on("copy",l[36]),{c(){Ve(e.$$.fragment)},m(o,s){je(e,o,s),t=!0},p(o,s){const r=s[0]&16777216?Tl(n,[Al(o[24])]):{};e.$set(r)},i(o){t||(j(e.$$.fragment,o),t=!0)},o(o){T(e.$$.fragment,o),t=!1},d(o){Le(e,o)}}}function xn(l){let e,t;const n=[l[24]];let i={};for(let o=0;o<n.length;o+=1)i=Il(i,n[o]);return e=new El({props:i}),{c(){Ve(e.$$.fragment)},m(o,s){je(e,o,s),t=!0},p(o,s){const r=s[0]&16777216?Tl(n,[Al(o[24])]):{};e.$set(r)},i(o){t||(j(e.$$.fragment,o),t=!0)},o(o){T(e.$$.fragment,o),t=!1},d(o){Le(e,o)}}}function Ms(l){let e,t,n,i,o,s,r,a,_,u,c=l[1]!==null&&Kn(l),b=Rn(l[4]),h=[];for(let m=0;m<b.length;m+=1)h[m]=Qn(Yn(l,b,m));const d=m=>T(h[m],1,1,()=>{h[m]=null});let p=l[5]==="panel"&&Xn(l),k=l[5]==="bubble"&&xn(l);return{c(){e=W("div"),c&&c.c(),t=ft(),n=W("div"),i=W("div");for(let m=0;m<h.length;m+=1)h[m].c();s=ft(),p&&p.c(),a=ft(),k&&k.c(),_=ws(),H(i,"class",o=Jn(l[23]?l[3]:"")+" svelte-1uhz716"),L(i,"message",l[23]),H(n,"class","flex-wrap svelte-1uhz716"),L(n,"role",l[3]),L(n,"component-wrap",l[4][0].type==="component"),H(e,"class",r="message-row "+l[5]+" "+l[3]+"-row svelte-1uhz716"),L(e,"with_avatar",l[1]!==null),L(e,"with_opposite_avatar",l[2]!==null)},m(m,w){Ye(m,e,w),c&&c.m(e,null),U(e,t),U(e,n),U(n,i);for(let v=0;v<h.length;v+=1)h[v]&&h[v].m(i,null);U(n,s),p&&p.m(n,null),Ye(m,a,w),k&&k.m(m,w),Ye(m,_,w),u=!0},p(m,w){if(m[1]!==null?c?(c.p(m,w),w[0]&2&&j(c,1)):(c=Kn(m),c.c(),j(c,1),c.m(e,t)):c&&(ut(),T(c,1,1,()=>{c=null}),_t()),w[0]&50327513){b=Rn(m[4]);let v;for(v=0;v<b.length;v+=1){const $=Yn(m,b,v);h[v]?(h[v].p($,w),j(h[v],1)):(h[v]=Qn($),h[v].c(),j(h[v],1),h[v].m(i,null))}for(ut(),v=b.length;v<h.length;v+=1)d(v);_t()}(!u||w[0]&8388616&&o!==(o=Jn(m[23]?m[3]:"")+" svelte-1uhz716"))&&H(i,"class",o),(!u||w[0]&8388616)&&L(i,"message",m[23]),m[5]==="panel"?p?(p.p(m,w),w[0]&32&&j(p,1)):(p=Xn(m),p.c(),j(p,1),p.m(n,null)):p&&(ut(),T(p,1,1,()=>{p=null}),_t()),(!u||w[0]&8)&&L(n,"role",m[3]),(!u||w[0]&16)&&L(n,"component-wrap",m[4][0].type==="component"),(!u||w[0]&40&&r!==(r="message-row "+m[5]+" "+m[3]+"-row svelte-1uhz716"))&&H(e,"class",r),(!u||w[0]&42)&&L(e,"with_avatar",m[1]!==null),(!u||w[0]&44)&&L(e,"with_opposite_avatar",m[2]!==null),m[5]==="bubble"?k?(k.p(m,w),w[0]&32&&j(k,1)):(k=xn(m),k.c(),j(k,1),k.m(_.parentNode,_)):k&&(ut(),T(k,1,1,()=>{k=null}),_t())},i(m){if(!u){j(c);for(let w=0;w<b.length;w+=1)j(h[w]);j(p),j(k),u=!0}},o(m){T(c),h=h.filter(Boolean);for(let w=0;w<h.length;w+=1)T(h[w]);T(p),T(k),u=!1},d(m){m&&(We(e),We(a),We(_)),c&&c.d(),ps(h,m),p&&p.d(),k&&k.d(m)}}}function el(l){return l.type==="text"?l.content:l.type==="component"&&l.content.component==="file"?Array.isArray(l.content.value)?`file of extension type: ${l.content.value[0].orig_name?.split(".").pop()}`:`file of extension type: ${l.content.value?.orig_name?.split(".").pop()}`+(l.content.value?.orig_name??""):`a component of type ${l.content.component??"unknown"}`}function Vs(l,e,t){let{value:n}=e,{avatar_img:i}=e,{opposite_avatar_img:o=null}=e,{role:s="user"}=e,{messages:r=[]}=e,{layout:a}=e,{render_markdown:_}=e,{latex_delimiters:u}=e,{sanitize_html:c}=e,{selectable:b}=e,{_fetch:h}=e,{rtl:d}=e,{dispatch:p}=e,{i18n:k}=e,{line_breaks:m}=e,{upload:w}=e,{target:v}=e,{root:$}=e,{theme_mode:Y}=e,{_components:ke}=e,{i:oe}=e,{show_copy_button:_e}=e,{generating:ue}=e,{show_like:fe}=e,{show_retry:se}=e,{show_undo:N}=e,{msg_format:ce}=e,{handle_action:me}=e,{scroll:he}=e,{allow_file_downloads:de}=e,{display_consecutive_in_same_bubble:ge}=e;function be(g,ee){p("select",{index:ee.index,value:ee.content})}let $e;const De=()=>he(),Ze=g=>be(oe,g),Fe=(g,ee)=>{ee.key==="Enter"&&be(oe,g)},Ue=g=>p("copy",g.detail);return l.$$set=g=>{"value"in g&&t(0,n=g.value),"avatar_img"in g&&t(1,i=g.avatar_img),"opposite_avatar_img"in g&&t(2,o=g.opposite_avatar_img),"role"in g&&t(3,s=g.role),"messages"in g&&t(4,r=g.messages),"layout"in g&&t(5,a=g.layout),"render_markdown"in g&&t(6,_=g.render_markdown),"latex_delimiters"in g&&t(7,u=g.latex_delimiters),"sanitize_html"in g&&t(8,c=g.sanitize_html),"selectable"in g&&t(9,b=g.selectable),"_fetch"in g&&t(10,h=g._fetch),"rtl"in g&&t(11,d=g.rtl),"dispatch"in g&&t(12,p=g.dispatch),"i18n"in g&&t(13,k=g.i18n),"line_breaks"in g&&t(14,m=g.line_breaks),"upload"in g&&t(15,w=g.upload),"target"in g&&t(16,v=g.target),"root"in g&&t(17,$=g.root),"theme_mode"in g&&t(18,Y=g.theme_mode),"_components"in g&&t(19,ke=g._components),"i"in g&&t(20,oe=g.i),"show_copy_button"in g&&t(26,_e=g.show_copy_button),"generating"in g&&t(27,ue=g.generating),"show_like"in g&&t(28,fe=g.show_like),"show_retry"in g&&t(29,se=g.show_retry),"show_undo"in g&&t(30,N=g.show_undo),"msg_format"in g&&t(31,ce=g.msg_format),"handle_action"in g&&t(32,me=g.handle_action),"scroll"in g&&t(21,he=g.scroll),"allow_file_downloads"in g&&t(22,de=g.allow_file_downloads),"display_consecutive_in_same_bubble"in g&&t(23,ge=g.display_consecutive_in_same_bubble)},l.$$.update=()=>{l.$$.dirty[0]&2080378938|l.$$.dirty[1]&3&&t(24,$e={handle_action:me,likeable:fe,show_retry:se,show_undo:N,generating:ue,show_copy_button:_e,message:ce==="tuples"?r[0]:r,position:s==="user"?"right":"left",avatar:i,layout:a,dispatch:p})},[n,i,o,s,r,a,_,u,c,b,h,d,p,k,m,w,v,$,Y,ke,oe,he,de,ge,$e,be,_e,ue,fe,se,N,ce,me,De,Ze,Fe,Ue]}class Ls extends bs{constructor(e){super(),vs(this,e,Vs,Ms,$s,{value:0,avatar_img:1,opposite_avatar_img:2,role:3,messages:4,layout:5,render_markdown:6,latex_delimiters:7,sanitize_html:8,selectable:9,_fetch:10,rtl:11,dispatch:12,i18n:13,line_breaks:14,upload:15,target:16,root:17,theme_mode:18,_components:19,i:20,show_copy_button:26,generating:27,show_like:28,show_retry:29,show_undo:30,msg_format:31,handle_action:32,scroll:21,allow_file_downloads:22,display_consecutive_in_same_bubble:23},null,[-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),z()}get avatar_img(){return this.$$.ctx[1]}set avatar_img(e){this.$$set({avatar_img:e}),z()}get opposite_avatar_img(){return this.$$.ctx[2]}set opposite_avatar_img(e){this.$$set({opposite_avatar_img:e}),z()}get role(){return this.$$.ctx[3]}set role(e){this.$$set({role:e}),z()}get messages(){return this.$$.ctx[4]}set messages(e){this.$$set({messages:e}),z()}get layout(){return this.$$.ctx[5]}set layout(e){this.$$set({layout:e}),z()}get render_markdown(){return this.$$.ctx[6]}set render_markdown(e){this.$$set({render_markdown:e}),z()}get latex_delimiters(){return this.$$.ctx[7]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),z()}get sanitize_html(){return this.$$.ctx[8]}set sanitize_html(e){this.$$set({sanitize_html:e}),z()}get selectable(){return this.$$.ctx[9]}set selectable(e){this.$$set({selectable:e}),z()}get _fetch(){return this.$$.ctx[10]}set _fetch(e){this.$$set({_fetch:e}),z()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),z()}get dispatch(){return this.$$.ctx[12]}set dispatch(e){this.$$set({dispatch:e}),z()}get i18n(){return this.$$.ctx[13]}set i18n(e){this.$$set({i18n:e}),z()}get line_breaks(){return this.$$.ctx[14]}set line_breaks(e){this.$$set({line_breaks:e}),z()}get upload(){return this.$$.ctx[15]}set upload(e){this.$$set({upload:e}),z()}get target(){return this.$$.ctx[16]}set target(e){this.$$set({target:e}),z()}get root(){return this.$$.ctx[17]}set root(e){this.$$set({root:e}),z()}get theme_mode(){return this.$$.ctx[18]}set theme_mode(e){this.$$set({theme_mode:e}),z()}get _components(){return this.$$.ctx[19]}set _components(e){this.$$set({_components:e}),z()}get i(){return this.$$.ctx[20]}set i(e){this.$$set({i:e}),z()}get show_copy_button(){return this.$$.ctx[26]}set show_copy_button(e){this.$$set({show_copy_button:e}),z()}get generating(){return this.$$.ctx[27]}set generating(e){this.$$set({generating:e}),z()}get show_like(){return this.$$.ctx[28]}set show_like(e){this.$$set({show_like:e}),z()}get show_retry(){return this.$$.ctx[29]}set show_retry(e){this.$$set({show_retry:e}),z()}get show_undo(){return this.$$.ctx[30]}set show_undo(e){this.$$set({show_undo:e}),z()}get msg_format(){return this.$$.ctx[31]}set msg_format(e){this.$$set({msg_format:e}),z()}get handle_action(){return this.$$.ctx[32]}set handle_action(e){this.$$set({handle_action:e}),z()}get scroll(){return this.$$.ctx[21]}set scroll(e){this.$$set({scroll:e}),z()}get allow_file_downloads(){return this.$$.ctx[22]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),z()}get display_consecutive_in_same_bubble(){return this.$$.ctx[23]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),z()}}const{SvelteComponent:js,append:_n,attr:Ce,check_outros:Bs,create_component:Ns,destroy_component:Es,detach:Zl,element:Bt,flush:tl,group_outros:Is,init:As,insert:Fl,mount_component:Ts,safe_not_equal:Ds,space:Zs,toggle_class:Vt,transition_in:Nt,transition_out:mn}=window.__gradio__svelte__internal;function nl(l){let e,t,n;return t=new vt({props:{class:"avatar-image",src:l[1][1].url,alt:"bot avatar"}}),{c(){e=Bt("div"),Ns(t.$$.fragment),Ce(e,"class","avatar-container svelte-1u5aj92")},m(i,o){Fl(i,e,o),Ts(t,e,null),n=!0},p(i,o){const s={};o&2&&(s.src=i[1][1].url),t.$set(s)},i(i){n||(Nt(t.$$.fragment,i),n=!0)},o(i){mn(t.$$.fragment,i),n=!1},d(i){i&&Zl(e),Es(t)}}}function Fs(l){let e,t,n,i,o,s,r=l[1][1]!==null&&nl(l);return{c(){e=Bt("div"),r&&r.c(),t=Zs(),n=Bt("div"),i=Bt("div"),i.innerHTML='<span class="sr-only">Loading content</span> <div class="dots svelte-1u5aj92"><div class="dot svelte-1u5aj92"></div> <div class="dot svelte-1u5aj92"></div> <div class="dot svelte-1u5aj92"></div></div>',Ce(i,"class","message-content svelte-1u5aj92"),Ce(n,"class",o="message bot pending "+l[0]+" svelte-1u5aj92"),Ce(n,"role","status"),Ce(n,"aria-label","Loading response"),Ce(n,"aria-live","polite"),Vt(n,"with_avatar",l[1][1]!==null),Vt(n,"with_opposite_avatar",l[1][0]!==null),Ce(e,"class","container svelte-1u5aj92")},m(a,_){Fl(a,e,_),r&&r.m(e,null),_n(e,t),_n(e,n),_n(n,i),s=!0},p(a,[_]){a[1][1]!==null?r?(r.p(a,_),_&2&&Nt(r,1)):(r=nl(a),r.c(),Nt(r,1),r.m(e,t)):r&&(Is(),mn(r,1,1,()=>{r=null}),Bs()),(!s||_&1&&o!==(o="message bot pending "+a[0]+" svelte-1u5aj92"))&&Ce(n,"class",o),(!s||_&3)&&Vt(n,"with_avatar",a[1][1]!==null),(!s||_&3)&&Vt(n,"with_opposite_avatar",a[1][0]!==null)},i(a){s||(Nt(r),s=!0)},o(a){mn(r),s=!1},d(a){a&&Zl(e),r&&r.d()}}}function Us(l,e,t){let{layout:n="bubble"}=e,{avatar_images:i=[null,null]}=e;return l.$$set=o=>{"layout"in o&&t(0,n=o.layout),"avatar_images"in o&&t(1,i=o.avatar_images)},[n,i]}class Ps extends js{constructor(e){super(),As(this,e,Us,Fs,Ds,{layout:0,avatar_images:1})}get layout(){return this.$$.ctx[0]}set layout(e){this.$$set({layout:e}),tl()}get avatar_images(){return this.$$.ctx[1]}set avatar_images(e){this.$$set({avatar_images:e}),tl()}}const{SvelteComponent:Rs,append:D,attr:y,check_outros:Se,create_component:Be,destroy_component:Ne,destroy_each:Ul,detach:Z,element:E,empty:Pl,ensure_array_like:At,flush:Lt,group_outros:He,init:Os,insert:F,listen:Js,mount_component:Ee,noop:Ke,safe_not_equal:Gs,set_data:Ft,space:ct,src_url_equal:Tt,text:xe,transition_in:q,transition_out:B}=window.__gradio__svelte__internal,{createEventDispatcher:Ws}=window.__gradio__svelte__internal;function ll(l,e,t){const n=l.slice();return n[7]=e[t],n[9]=t,n}function il(l,e,t){const n=l.slice();return n[10]=e[t],n[9]=t,n}function ol(l){let e,t,n;return t=new gn({props:{message:l[1],latex_delimiters:l[2],root:l[3]}}),{c(){e=E("div"),Be(t.$$.fragment),y(e,"class","placeholder svelte-9pi8y1")},m(i,o){F(i,e,o),Ee(t,e,null),n=!0},p(i,o){const s={};o&2&&(s.message=i[1]),o&4&&(s.latex_delimiters=i[2]),o&8&&(s.root=i[3]),t.$set(s)},i(i){n||(q(t.$$.fragment,i),n=!0)},o(i){B(t.$$.fragment,i),n=!1},d(i){i&&Z(e),Ne(t)}}}function sl(l){let e,t,n=At(l[0]),i=[];for(let s=0;s<n.length;s+=1)i[s]=fl(ll(l,n,s));const o=s=>B(i[s],1,1,()=>{i[s]=null});return{c(){e=E("div");for(let s=0;s<i.length;s+=1)i[s].c();y(e,"class","examples svelte-9pi8y1"),y(e,"role","list")},m(s,r){F(s,e,r);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);t=!0},p(s,r){if(r&17){n=At(s[0]);let a;for(a=0;a<n.length;a+=1){const _=ll(s,n,a);i[a]?(i[a].p(_,r),q(i[a],1)):(i[a]=fl(_),i[a].c(),q(i[a],1),i[a].m(e,null))}for(He(),a=n.length;a<i.length;a+=1)o(a);Se()}},i(s){if(!t){for(let r=0;r<n.length;r+=1)q(i[r]);t=!0}},o(s){i=i.filter(Boolean);for(let r=0;r<i.length;r+=1)B(i[r]);t=!1},d(s){s&&Z(e),Ul(i,s)}}}function Ys(l){let e,t,n,i,o,s,r;const a=[na,ta,ea,xs,Xs],_=[];function u(c,b){return b&1&&(e=null),b&1&&(t=null),b&1&&(n=null),c[7].files.length>1?0:(e==null&&(e=!!c[7].files[0].mime_type?.includes("image")),e?1:(t==null&&(t=!!c[7].files[0].mime_type?.includes("video")),t?2:(n==null&&(n=!!c[7].files[0].mime_type?.includes("audio")),n?3:4)))}return i=u(l,-1),o=_[i]=a[i](l),{c(){o.c(),s=Pl()},m(c,b){_[i].m(c,b),F(c,s,b),r=!0},p(c,b){let h=i;i=u(c,b),i===h?_[i].p(c,b):(He(),B(_[h],1,1,()=>{_[h]=null}),Se(),o=_[i],o?o.p(c,b):(o=_[i]=a[i](c),o.c()),q(o,1),o.m(s.parentNode,s))},i(c){r||(q(o),r=!0)},o(c){B(o),r=!1},d(c){c&&Z(s),_[i].d(c)}}}function Ks(l){let e;return{c(){e=E("div"),e.innerHTML='<span class="text-icon-aa svelte-9pi8y1">Aa</span>',y(e,"class","example-icon svelte-9pi8y1"),y(e,"aria-hidden","true")},m(t,n){F(t,e,n)},p:Ke,i:Ke,o:Ke,d(t){t&&Z(e)}}}function Qs(l){let e,t,n;return t=new vt({props:{class:"example-image",src:l[7].icon.url,alt:"Example icon"}}),{c(){e=E("div"),Be(t.$$.fragment),y(e,"class","example-image-container svelte-9pi8y1")},m(i,o){F(i,e,o),Ee(t,e,null),n=!0},p(i,o){const s={};o&1&&(s.src=i[7].icon.url),t.$set(s)},i(i){n||(q(t.$$.fragment,i),n=!0)},o(i){B(t.$$.fragment,i),n=!1},d(i){i&&Z(e),Ne(t)}}}function Xs(l){let e,t,n,i;return t=new dn({}),{c(){e=E("div"),Be(t.$$.fragment),y(e,"class","example-icon svelte-9pi8y1"),y(e,"aria-label",n=`File: ${l[7].files[0].orig_name}`)},m(o,s){F(o,e,s),Ee(t,e,null),i=!0},p(o,s){(!i||s&1&&n!==(n=`File: ${o[7].files[0].orig_name}`))&&y(e,"aria-label",n)},i(o){i||(q(t.$$.fragment,o),i=!0)},o(o){B(t.$$.fragment,o),i=!1},d(o){o&&Z(e),Ne(t)}}}function xs(l){let e,t,n,i;return t=new Ml({}),{c(){e=E("div"),Be(t.$$.fragment),y(e,"class","example-icon svelte-9pi8y1"),y(e,"aria-label",n=`File: ${l[7].files[0].orig_name}`)},m(o,s){F(o,e,s),Ee(t,e,null),i=!0},p(o,s){(!i||s&1&&n!==(n=`File: ${o[7].files[0].orig_name}`))&&y(e,"aria-label",n)},i(o){i||(q(t.$$.fragment,o),i=!0)},o(o){B(t.$$.fragment,o),i=!1},d(o){o&&Z(e),Ne(t)}}}function ea(l){let e,t,n;return{c(){e=E("div"),t=E("video"),y(t,"class","example-image"),Tt(t.src,n=l[7].files[0].url)||y(t,"src",n),y(t,"aria-hidden","true"),y(e,"class","example-image-container svelte-9pi8y1")},m(i,o){F(i,e,o),D(e,t)},p(i,o){o&1&&!Tt(t.src,n=i[7].files[0].url)&&y(t,"src",n)},i:Ke,o:Ke,d(i){i&&Z(e)}}}function ta(l){let e,t,n;return t=new vt({props:{class:"example-image",src:l[7].files[0].url,alt:l[7].files[0].orig_name||"Example image"}}),{c(){e=E("div"),Be(t.$$.fragment),y(e,"class","example-image-container svelte-9pi8y1")},m(i,o){F(i,e,o),Ee(t,e,null),n=!0},p(i,o){const s={};o&1&&(s.src=i[7].files[0].url),o&1&&(s.alt=i[7].files[0].orig_name||"Example image"),t.$set(s)},i(i){n||(q(t.$$.fragment,i),n=!0)},o(i){B(t.$$.fragment,i),n=!1},d(i){i&&Z(e),Ne(t)}}}function na(l){let e,t,n,i=At(l[7].files.slice(0,4)),o=[];for(let a=0;a<i.length;a+=1)o[a]=_l(il(l,i,a));const s=a=>B(o[a],1,1,()=>{o[a]=null});let r=l[7].files.length>4&&ul(l);return{c(){e=E("div");for(let a=0;a<o.length;a+=1)o[a].c();t=ct(),r&&r.c(),y(e,"class","example-icons-grid svelte-9pi8y1"),y(e,"role","group"),y(e,"aria-label","Example attachments")},m(a,_){F(a,e,_);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);D(e,t),r&&r.m(e,null),n=!0},p(a,_){if(_&1){i=At(a[7].files.slice(0,4));let u;for(u=0;u<i.length;u+=1){const c=il(a,i,u);o[u]?(o[u].p(c,_),q(o[u],1)):(o[u]=_l(c),o[u].c(),q(o[u],1),o[u].m(e,t))}for(He(),u=i.length;u<o.length;u+=1)s(u);Se()}a[7].files.length>4?r?r.p(a,_):(r=ul(a),r.c(),r.m(e,null)):r&&(r.d(1),r=null)},i(a){if(!n){for(let _=0;_<i.length;_+=1)q(o[_]);n=!0}},o(a){o=o.filter(Boolean);for(let _=0;_<o.length;_+=1)B(o[_]);n=!1},d(a){a&&Z(e),Ul(o,a),r&&r.d()}}}function la(l){let e,t,n,i,o,s;const r=[aa,sa],a=[];function _(u,c){return c&1&&(t=null),t==null&&(t=!!u[10].mime_type?.includes("audio")),t?0:1}return n=_(l,-1),i=a[n]=r[n](l),{c(){e=E("div"),i.c(),y(e,"class","example-icon svelte-9pi8y1"),y(e,"aria-label",o=`File: ${l[10].orig_name}`)},m(u,c){F(u,e,c),a[n].m(e,null),s=!0},p(u,c){let b=n;n=_(u,c),n!==b&&(He(),B(a[b],1,1,()=>{a[b]=null}),Se(),i=a[n],i||(i=a[n]=r[n](u),i.c()),q(i,1),i.m(e,null)),(!s||c&1&&o!==(o=`File: ${u[10].orig_name}`))&&y(e,"aria-label",o)},i(u){s||(q(i),s=!0)},o(u){B(i),s=!1},d(u){u&&Z(e),a[n].d()}}}function ia(l){let e,t,n,i,o=l[9]===3&&l[7].files.length>4&&al(l);return{c(){e=E("div"),t=E("video"),i=ct(),o&&o.c(),y(t,"class","example-image"),Tt(t.src,n=l[10].url)||y(t,"src",n),y(t,"aria-hidden","true"),y(e,"class","example-image-container svelte-9pi8y1")},m(s,r){F(s,e,r),D(e,t),D(e,i),o&&o.m(e,null)},p(s,r){r&1&&!Tt(t.src,n=s[10].url)&&y(t,"src",n),s[9]===3&&s[7].files.length>4?o?o.p(s,r):(o=al(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i:Ke,o:Ke,d(s){s&&Z(e),o&&o.d()}}}function oa(l){let e,t,n,i;t=new vt({props:{class:"example-image",src:l[10].url,alt:l[10].orig_name||`Example image ${l[9]+1}`}});let o=l[9]===3&&l[7].files.length>4&&rl(l);return{c(){e=E("div"),Be(t.$$.fragment),n=ct(),o&&o.c(),y(e,"class","example-image-container svelte-9pi8y1")},m(s,r){F(s,e,r),Ee(t,e,null),D(e,n),o&&o.m(e,null),i=!0},p(s,r){const a={};r&1&&(a.src=s[10].url),r&1&&(a.alt=s[10].orig_name||`Example image ${s[9]+1}`),t.$set(a),s[9]===3&&s[7].files.length>4?o?o.p(s,r):(o=rl(s),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(s){i||(q(t.$$.fragment,s),i=!0)},o(s){B(t.$$.fragment,s),i=!1},d(s){s&&Z(e),Ne(t),o&&o.d()}}}function sa(l){let e,t;return e=new dn({}),{c(){Be(e.$$.fragment)},m(n,i){Ee(e,n,i),t=!0},i(n){t||(q(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){Ne(e,n)}}}function aa(l){let e,t;return e=new Ml({}),{c(){Be(e.$$.fragment)},m(n,i){Ee(e,n,i),t=!0},i(n){t||(q(e.$$.fragment,n),t=!0)},o(n){B(e.$$.fragment,n),t=!1},d(n){Ne(e,n)}}}function al(l){let e,t,n=l[7].files.length-4+"",i,o;return{c(){e=E("div"),t=xe("+"),i=xe(n),y(e,"class","image-overlay svelte-9pi8y1"),y(e,"role","status"),y(e,"aria-label",o=`${l[7].files.length-4} more files`)},m(s,r){F(s,e,r),D(e,t),D(e,i)},p(s,r){r&1&&n!==(n=s[7].files.length-4+"")&&Ft(i,n),r&1&&o!==(o=`${s[7].files.length-4} more files`)&&y(e,"aria-label",o)},d(s){s&&Z(e)}}}function rl(l){let e,t,n=l[7].files.length-4+"",i,o;return{c(){e=E("div"),t=xe("+"),i=xe(n),y(e,"class","image-overlay svelte-9pi8y1"),y(e,"role","status"),y(e,"aria-label",o=`${l[7].files.length-4} more files`)},m(s,r){F(s,e,r),D(e,t),D(e,i)},p(s,r){r&1&&n!==(n=s[7].files.length-4+"")&&Ft(i,n),r&1&&o!==(o=`${s[7].files.length-4} more files`)&&y(e,"aria-label",o)},d(s){s&&Z(e)}}}function _l(l){let e,t,n,i,o,s;const r=[oa,ia,la],a=[];function _(u,c){return c&1&&(e=null),c&1&&(t=null),e==null&&(e=!!u[10].mime_type?.includes("image")),e?0:(t==null&&(t=!!u[10].mime_type?.includes("video")),t?1:2)}return n=_(l,-1),i=a[n]=r[n](l),{c(){i.c(),o=Pl()},m(u,c){a[n].m(u,c),F(u,o,c),s=!0},p(u,c){let b=n;n=_(u,c),n===b?a[n].p(u,c):(He(),B(a[b],1,1,()=>{a[b]=null}),Se(),i=a[n],i?i.p(u,c):(i=a[n]=r[n](u),i.c()),q(i,1),i.m(o.parentNode,o))},i(u){s||(q(i),s=!0)},o(u){B(i),s=!1},d(u){u&&Z(o),a[n].d(u)}}}function ul(l){let e,t,n,i=l[7].files.length-4+"",o,s;return{c(){e=E("div"),t=E("div"),n=xe("+"),o=xe(i),y(t,"class","file-overlay svelte-9pi8y1"),y(t,"role","status"),y(t,"aria-label",s=`${l[7].files.length-4} more files`),y(e,"class","example-icon svelte-9pi8y1")},m(r,a){F(r,e,a),D(e,t),D(t,n),D(t,o)},p(r,a){a&1&&i!==(i=r[7].files.length-4+"")&&Ft(o,i),a&1&&s!==(s=`${r[7].files.length-4} more files`)&&y(t,"aria-label",s)},d(r){r&&Z(e)}}}function fl(l){let e,t,n,i,o,s,r,a=(l[7].display_text||l[7].text)+"",_,u,c,b,h,d;const p=[Qs,Ks,Ys],k=[];function m(v,$){return v[7]?.icon?.url?0:v[7]?.icon?.mime_type==="text"?1:v[7].files!==void 0&&v[7].files.length>0?2:-1}~(n=m(l))&&(i=k[n]=p[n](l));function w(){return l[5](l[9],l[7])}return{c(){e=E("button"),t=E("div"),i&&i.c(),o=ct(),s=E("div"),r=E("span"),_=xe(a),u=ct(),y(r,"class","example-text svelte-9pi8y1"),y(s,"class","example-text-content svelte-9pi8y1"),y(t,"class","example-content svelte-9pi8y1"),y(e,"class","example svelte-9pi8y1"),y(e,"aria-label",c=`Select example ${l[9]+1}: ${l[7].display_text||l[7].text}`)},m(v,$){F(v,e,$),D(e,t),~n&&k[n].m(t,null),D(t,o),D(t,s),D(s,r),D(r,_),D(e,u),b=!0,h||(d=Js(e,"click",w),h=!0)},p(v,$){l=v;let Y=n;n=m(l),n===Y?~n&&k[n].p(l,$):(i&&(He(),B(k[Y],1,1,()=>{k[Y]=null}),Se()),~n?(i=k[n],i?i.p(l,$):(i=k[n]=p[n](l),i.c()),q(i,1),i.m(t,o)):i=null),(!b||$&1)&&a!==(a=(l[7].display_text||l[7].text)+"")&&Ft(_,a),(!b||$&1&&c!==(c=`Select example ${l[9]+1}: ${l[7].display_text||l[7].text}`))&&y(e,"aria-label",c)},i(v){b||(q(i),b=!0)},o(v){B(i),b=!1},d(v){v&&Z(e),~n&&k[n].d(),h=!1,d()}}}function ra(l){let e,t,n,i=l[1]!==null&&ol(l),o=l[0]!==null&&sl(l);return{c(){e=E("div"),i&&i.c(),t=ct(),o&&o.c(),y(e,"class","placeholder-content svelte-9pi8y1"),y(e,"role","complementary")},m(s,r){F(s,e,r),i&&i.m(e,null),D(e,t),o&&o.m(e,null),n=!0},p(s,[r]){s[1]!==null?i?(i.p(s,r),r&2&&q(i,1)):(i=ol(s),i.c(),q(i,1),i.m(e,t)):i&&(He(),B(i,1,1,()=>{i=null}),Se()),s[0]!==null?o?(o.p(s,r),r&1&&q(o,1)):(o=sl(s),o.c(),q(o,1),o.m(e,null)):o&&(He(),B(o,1,1,()=>{o=null}),Se())},i(s){n||(q(i),q(o),n=!0)},o(s){B(i),B(o),n=!1},d(s){s&&Z(e),i&&i.d(),o&&o.d()}}}function _a(l,e,t){let{examples:n=null}=e,{placeholder:i=null}=e,{latex_delimiters:o}=e,{root:s}=e;const r=Ws();function a(u,c){const b=typeof c=="string"?{text:c}:c;r("example_select",{index:u,value:{text:b.text,files:b.files}})}const _=(u,c)=>a(u,typeof c=="string"?{text:c}:c);return l.$$set=u=>{"examples"in u&&t(0,n=u.examples),"placeholder"in u&&t(1,i=u.placeholder),"latex_delimiters"in u&&t(2,o=u.latex_delimiters),"root"in u&&t(3,s=u.root)},[n,i,o,s,a,_]}class ua extends Rs{constructor(e){super(),Os(this,e,_a,ra,Gs,{examples:0,placeholder:1,latex_delimiters:2,root:3})}get examples(){return this.$$.ctx[0]}set examples(e){this.$$set({examples:e}),Lt()}get placeholder(){return this.$$.ctx[1]}set placeholder(e){this.$$set({placeholder:e}),Lt()}get latex_delimiters(){return this.$$.ctx[2]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),Lt()}get root(){return this.$$.ctx[3]}set root(e){this.$$set({root:e}),Lt()}}const{SvelteComponent:fa,create_component:ca,destroy_component:ma,flush:ha,init:da,mount_component:ga,safe_not_equal:ba,transition_in:pa,transition_out:wa}=window.__gradio__svelte__internal,{onDestroy:va}=window.__gradio__svelte__internal;function ka(l){let e,t;return e=new ve({props:{Icon:l[0]?Et:It,label:l[0]?"Copied conversation":"Copy conversation"}}),e.$on("click",l[1]),{c(){ca(e.$$.fragment)},m(n,i){ga(e,n,i),t=!0},p(n,[i]){const o={};i&1&&(o.Icon=n[0]?Et:It),i&1&&(o.label=n[0]?"Copied conversation":"Copy conversation"),e.$set(o)},i(n){t||(pa(e.$$.fragment,n),t=!0)},o(n){wa(e.$$.fragment,n),t=!1},d(n){ma(e,n)}}}function $a(l,e,t){let n=!1,{value:i}=e,o;function s(){t(0,n=!0),o&&clearTimeout(o),o=setTimeout(()=>{t(0,n=!1)},1e3)}const r=()=>{if(i){const _=i.map(u=>u.type==="text"?`${u.role}: ${u.content}`:`${u.role}: ${u.content.value.url}`).join(`

`);navigator.clipboard.writeText(_).catch(u=>{console.error("Failed to copy conversation: ",u)})}};async function a(){"clipboard"in navigator&&(r(),s())}return va(()=>{o&&clearTimeout(o)}),l.$$set=_=>{"value"in _&&t(2,i=_.value)},[n,a,i]}class ya extends fa{constructor(e){super(),da(this,e,$a,ka,ba,{value:2})}get value(){return this.$$.ctx[2]}set value(e){this.$$set({value:e}),ha()}}const{SvelteComponent:Ca,action_destroyer:za,append:hn,attr:we,binding_callbacks:Sa,check_outros:Qe,create_component:Ie,destroy_component:Ae,destroy_each:Rl,detach:le,element:Ct,empty:Ol,ensure_array_like:Dt,flush:S,group_outros:Xe,init:Ha,insert:ie,listen:qa,mount_component:Te,noop:Zt,null_to_empty:cl,safe_not_equal:Ma,set_data:Va,space:mt,text:La,transition_in:V,transition_out:I}=window.__gradio__svelte__internal,{createEventDispatcher:ja,tick:Ba,onMount:un}=window.__gradio__svelte__internal;function ml(l,e,t){const n=l.slice();return n[55]=e[t],n[57]=t,n}function hl(l,e,t){const n=l.slice();n[58]=e[t],n[63]=t;const i=n[58][0].role==="user"?"user":"bot";n[59]=i;const o=n[13][n[59]==="user"?0:1];n[60]=o;const s=n[13][n[59]==="user"?0:1];return n[61]=s,n}function dl(l){let e,t;return e=new ql({props:{$$slots:{default:[Na]},$$scope:{ctx:l}}}),{c(){Ie(e.$$.fragment)},m(n,i){Te(e,n,i),t=!0},p(n,i){const o={};i[0]&1537|i[2]&4&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){I(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function gl(l){let e,t;return e=new ve({props:{Icon:Xl}}),e.$on("click",l[41]),{c(){Ie(e.$$.fragment)},m(n,i){Te(e,n,i),t=!0},p:Zt,i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){I(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function bl(l){let e,t;return e=new ya({props:{value:l[0]}}),{c(){Ie(e.$$.fragment)},m(n,i){Te(e,n,i),t=!0},p(n,i){const o={};i[0]&1&&(o.value=n[0]),e.$set(o)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){I(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function Na(l){let e,t,n,i,o,s=l[9]&&gl(l);t=new ve({props:{Icon:xl,label:"Clear"}}),t.$on("click",l[42]);let r=l[10]&&bl(l);return{c(){s&&s.c(),e=mt(),Ie(t.$$.fragment),n=mt(),r&&r.c(),i=Ol()},m(a,_){s&&s.m(a,_),ie(a,e,_),Te(t,a,_),ie(a,n,_),r&&r.m(a,_),ie(a,i,_),o=!0},p(a,_){a[9]?s?(s.p(a,_),_[0]&512&&V(s,1)):(s=gl(a),s.c(),V(s,1),s.m(e.parentNode,e)):s&&(Xe(),I(s,1,1,()=>{s=null}),Qe()),a[10]?r?(r.p(a,_),_[0]&1024&&V(r,1)):(r=bl(a),r.c(),V(r,1),r.m(i.parentNode,i)):r&&(Xe(),I(r,1,1,()=>{r=null}),Qe())},i(a){o||(V(s),V(t.$$.fragment,a),V(r),o=!0)},o(a){I(s),I(t.$$.fragment,a),I(r),o=!1},d(a){a&&(le(e),le(n),le(i)),s&&s.d(a),Ae(t,a),r&&r.d(a)}}}function Ea(l){let e,t;return e=new ua({props:{examples:l[23],placeholder:l[20],latex_delimiters:l[4],root:l[27]}}),e.$on("example_select",l[46]),{c(){Ie(e.$$.fragment)},m(n,i){Te(e,n,i),t=!0},p(n,i){const o={};i[0]&8388608&&(o.examples=n[23]),i[0]&1048576&&(o.placeholder=n[20]),i[0]&16&&(o.latex_delimiters=n[4]),i[0]&134217728&&(o.root=n[27]),e.$set(o)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){I(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function Ia(l){let e,t,n,i,o,s,r,a=Dt(l[32]),_=[];for(let d=0;d<a.length;d+=1)_[d]=pl(hl(l,a,d));const u=d=>I(_[d],1,1,()=>{_[d]=null}),c=[Ta,Aa],b=[];function h(d,p){return d[5]?0:d[33]?1:-1}return~(n=h(l))&&(i=b[n]=c[n](l)),{c(){e=Ct("div");for(let d=0;d<_.length;d+=1)_[d].c();t=mt(),i&&i.c(),we(e,"class","message-wrap svelte-18p1ref")},m(d,p){ie(d,e,p);for(let k=0;k<_.length;k+=1)_[k]&&_[k].m(e,null);hn(e,t),~n&&b[n].m(e,null),o=!0,s||(r=za(Yl.call(null,e)),s=!0)},p(d,p){if(p[0]&1064303071|p[1]&90){a=Dt(d[32]);let m;for(m=0;m<a.length;m+=1){const w=hl(d,a,m);_[m]?(_[m].p(w,p),V(_[m],1)):(_[m]=pl(w),_[m].c(),V(_[m],1),_[m].m(e,t))}for(Xe(),m=a.length;m<_.length;m+=1)u(m);Qe()}let k=n;n=h(d),n===k?~n&&b[n].p(d,p):(i&&(Xe(),I(b[k],1,1,()=>{b[k]=null}),Qe()),~n?(i=b[n],i?i.p(d,p):(i=b[n]=c[n](d),i.c()),V(i,1),i.m(e,null)):i=null)},i(d){if(!o){for(let p=0;p<a.length;p+=1)V(_[p]);V(i),o=!0}},o(d){_=_.filter(Boolean);for(let p=0;p<_.length;p+=1)I(_[p]);I(i),o=!1},d(d){d&&le(e),Rl(_,d),~n&&b[n].d(),s=!1,r()}}}function pl(l){let e,t;function n(...i){return l[43](l[63],l[58],...i)}return e=new Ls({props:{messages:l[58],display_consecutive_in_same_bubble:l[3],opposite_avatar_img:l[61],avatar_img:l[60],role:l[59],layout:l[19],dispatch:l[35],i18n:l[18],_fetch:l[1],line_breaks:l[16],theme_mode:l[17],target:l[29],root:l[27],upload:l[21],selectable:l[7],sanitize_html:l[14],render_markdown:l[15],rtl:l[11],i:l[63],value:l[0],latex_delimiters:l[4],_components:l[28],generating:l[6],msg_format:l[22],show_like:l[59]==="user"?l[8]&&l[26]:l[8],show_retry:l[24]&&st(l[58],l[0]),show_undo:l[25]&&st(l[58],l[0]),show_copy_button:l[12],handle_action:n,scroll:l[34]?scroll:Za,allow_file_downloads:l[2]}}),e.$on("copy",l[44]),{c(){Ie(e.$$.fragment)},m(i,o){Te(e,i,o),t=!0},p(i,o){l=i;const s={};o[1]&2&&(s.messages=l[58]),o[0]&8&&(s.display_consecutive_in_same_bubble=l[3]),o[0]&8192|o[1]&2&&(s.opposite_avatar_img=l[61]),o[0]&8192|o[1]&2&&(s.avatar_img=l[60]),o[1]&2&&(s.role=l[59]),o[0]&524288&&(s.layout=l[19]),o[0]&262144&&(s.i18n=l[18]),o[0]&2&&(s._fetch=l[1]),o[0]&65536&&(s.line_breaks=l[16]),o[0]&131072&&(s.theme_mode=l[17]),o[0]&536870912&&(s.target=l[29]),o[0]&134217728&&(s.root=l[27]),o[0]&2097152&&(s.upload=l[21]),o[0]&128&&(s.selectable=l[7]),o[0]&16384&&(s.sanitize_html=l[14]),o[0]&32768&&(s.render_markdown=l[15]),o[0]&2048&&(s.rtl=l[11]),o[0]&1&&(s.value=l[0]),o[0]&16&&(s.latex_delimiters=l[4]),o[0]&268435456&&(s._components=l[28]),o[0]&64&&(s.generating=l[6]),o[0]&4194304&&(s.msg_format=l[22]),o[0]&67109120|o[1]&2&&(s.show_like=l[59]==="user"?l[8]&&l[26]:l[8]),o[0]&16777217|o[1]&2&&(s.show_retry=l[24]&&st(l[58],l[0])),o[0]&33554433|o[1]&2&&(s.show_undo=l[25]&&st(l[58],l[0])),o[0]&4096&&(s.show_copy_button=l[12]),o[1]&2&&(s.handle_action=n),o[0]&4&&(s.allow_file_downloads=l[2]),e.$set(s)},i(i){t||(V(e.$$.fragment,i),t=!0)},o(i){I(e.$$.fragment,i),t=!1},d(i){Ae(e,i)}}}function Aa(l){let e,t=Dt(l[33]),n=[];for(let i=0;i<t.length;i+=1)n[i]=wl(ml(l,t,i));return{c(){e=Ct("div");for(let i=0;i<n.length;i+=1)n[i].c();we(e,"class","options svelte-18p1ref")},m(i,o){ie(i,e,o);for(let s=0;s<n.length;s+=1)n[s]&&n[s].m(e,null)},p(i,o){if(o[1]&20){t=Dt(i[33]);let s;for(s=0;s<t.length;s+=1){const r=ml(i,t,s);n[s]?n[s].p(r,o):(n[s]=wl(r),n[s].c(),n[s].m(e,null))}for(;s<n.length;s+=1)n[s].d(1);n.length=t.length}},i:Zt,o:Zt,d(i){i&&le(e),Rl(n,i)}}}function Ta(l){let e,t;return e=new Ps({props:{layout:l[19],avatar_images:l[13]}}),{c(){Ie(e.$$.fragment)},m(n,i){Te(e,n,i),t=!0},p(n,i){const o={};i[0]&524288&&(o.layout=n[19]),i[0]&8192&&(o.avatar_images=n[13]),e.$set(o)},i(n){t||(V(e.$$.fragment,n),t=!0)},o(n){I(e.$$.fragment,n),t=!1},d(n){Ae(e,n)}}}function wl(l){let e,t=(l[55].label||l[55].value)+"",n,i,o,s;function r(){return l[45](l[57],l[55])}return{c(){e=Ct("button"),n=La(t),i=mt(),we(e,"class","option svelte-18p1ref")},m(a,_){ie(a,e,_),hn(e,n),hn(e,i),o||(s=qa(e,"click",r),o=!0)},p(a,_){l=a,_[1]&4&&t!==(t=(l[55].label||l[55].value)+"")&&Va(n,t)},d(a){a&&le(e),o=!1,s()}}}function vl(l){let e,t,n;return t=new ve({props:{Icon:Ci,label:"Scroll down",size:"large"}}),t.$on("click",l[36]),{c(){e=Ct("div"),Ie(t.$$.fragment),we(e,"class","scroll-down-button-container svelte-18p1ref")},m(i,o){ie(i,e,o),Te(t,e,null),n=!0},p:Zt,i(i){n||(V(t.$$.fragment,i),n=!0)},o(i){I(t.$$.fragment,i),n=!1},d(i){i&&le(e),Ae(t)}}}function Da(l){let e,t,n,i,o,s,r,a,_=l[0]!==null&&l[0].length>0&&dl(l);const u=[Ia,Ea],c=[];function b(d,p){return d[0]!==null&&d[0].length>0&&d[32]!==null?0:1}n=b(l),i=c[n]=u[n](l);let h=l[31]&&vl(l);return{c(){_&&_.c(),e=mt(),t=Ct("div"),i.c(),s=mt(),h&&h.c(),r=Ol(),we(t,"class",o=cl(l[19]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-18p1ref"),we(t,"role","log"),we(t,"aria-label","chatbot conversation"),we(t,"aria-live","polite")},m(d,p){_&&_.m(d,p),ie(d,e,p),ie(d,t,p),c[n].m(t,null),l[47](t),ie(d,s,p),h&&h.m(d,p),ie(d,r,p),a=!0},p(d,p){d[0]!==null&&d[0].length>0?_?(_.p(d,p),p[0]&1&&V(_,1)):(_=dl(d),_.c(),V(_,1),_.m(e.parentNode,e)):_&&(Xe(),I(_,1,1,()=>{_=null}),Qe());let k=n;n=b(d),n===k?c[n].p(d,p):(Xe(),I(c[k],1,1,()=>{c[k]=null}),Qe(),i=c[n],i?i.p(d,p):(i=c[n]=u[n](d),i.c()),V(i,1),i.m(t,null)),(!a||p[0]&524288&&o!==(o=cl(d[19]==="bubble"?"bubble-wrap":"panel-wrap")+" svelte-18p1ref"))&&we(t,"class",o),d[31]?h?(h.p(d,p),p[1]&1&&V(h,1)):(h=vl(d),h.c(),V(h,1),h.m(r.parentNode,r)):h&&(Xe(),I(h,1,1,()=>{h=null}),Qe())},i(d){a||(V(_),V(i),V(h),a=!0)},o(d){I(_),I(i),I(h),a=!1},d(d){d&&(le(e),le(t),le(s),le(r)),_&&_.d(d),c[n].d(),l[47](null),h&&h.d(d)}}}const Za=()=>{};function Fa(l,e,t){let n,i,{value:o=[]}=e,s=null,{_fetch:r}=e,{load_component:a}=e,{allow_file_downloads:_}=e,{display_consecutive_in_same_bubble:u}=e,c={};const b=typeof window<"u";async function h(){t(28,c=await Vi(Li(o),c,a))}let{latex_delimiters:d}=e,{pending_message:p=!1}=e,{generating:k=!1}=e,{selectable:m=!1}=e,{likeable:w=!1}=e,{show_share_button:v=!1}=e,{show_copy_all_button:$=!1}=e,{rtl:Y=!1}=e,{show_copy_button:ke=!1}=e,{avatar_images:oe=[null,null]}=e,{sanitize_html:_e=!0}=e,{render_markdown:ue=!0}=e,{line_breaks:fe=!0}=e,{autoscroll:se=!0}=e,{theme_mode:N}=e,{i18n:ce}=e,{layout:me="bubble"}=e,{placeholder:he=null}=e,{upload:de}=e,{msg_format:ge="tuples"}=e,{examples:be=null}=e,{_retryable:$e=!1}=e,{_undoable:De=!1}=e,{like_user_message:Ze=!1}=e,{root:Fe}=e,Ue=null;un(()=>{t(29,Ue=document.querySelector("div.gradio-container"))});let g,ee=!1;const J=ja();function zt(){return g&&g.offsetHeight+g.scrollTop>g.scrollHeight-100}function St(){g&&(g.scrollTo(0,g.scrollHeight),t(31,ee=!1))}async function Ht(){se&&(zt()?(await Ba(),St()):t(31,ee=!0))}un(()=>{Ht()}),un(()=>{function f(){zt()&&t(31,ee=!1)}return g?.addEventListener("scroll",f),()=>{g?.removeEventListener("scroll",f)}});function qt(f,ye,Pe){if(Pe==="undo"||Pe==="retry"){const pe=o;let lt=pe.length-1;for(;pe[lt].role==="assistant";)lt--;J(Pe,{index:pe[lt].index,value:pe[lt].content});return}if(ge==="tuples")J("like",{index:ye.index,value:ye.content,liked:Pe==="like"});else{if(!n)return;const pe=n[f],[lt,Jl]=[pe[0],pe[pe.length-1]];J("like",{index:[lt.index,Jl.index],value:pe.map(Gl=>Gl.content),liked:Pe==="like"})}}function Ot(){if(!o||!n||n.length===0)return;const f=n[n.length-1];if(f[0].role==="assistant")return f[f.length-1].options}const Jt=async()=>{try{const f=await zi(o);J("share",{description:f})}catch(f){console.error(f);let ye=f instanceof Wl?f.message:"Share failed.";J("error",ye)}},Gt=()=>J("clear"),Wt=(f,ye,Pe)=>qt(f,ye[0],Pe),Yt=f=>J("copy",f.detail),Kt=(f,ye)=>J("option_select",{index:f,value:ye.value}),Qt=f=>J("example_select",f.detail);function Xt(f){Sa[f?"unshift":"push"](()=>{g=f,t(30,g)})}return l.$$set=f=>{"value"in f&&t(0,o=f.value),"_fetch"in f&&t(1,r=f._fetch),"load_component"in f&&t(38,a=f.load_component),"allow_file_downloads"in f&&t(2,_=f.allow_file_downloads),"display_consecutive_in_same_bubble"in f&&t(3,u=f.display_consecutive_in_same_bubble),"latex_delimiters"in f&&t(4,d=f.latex_delimiters),"pending_message"in f&&t(5,p=f.pending_message),"generating"in f&&t(6,k=f.generating),"selectable"in f&&t(7,m=f.selectable),"likeable"in f&&t(8,w=f.likeable),"show_share_button"in f&&t(9,v=f.show_share_button),"show_copy_all_button"in f&&t(10,$=f.show_copy_all_button),"rtl"in f&&t(11,Y=f.rtl),"show_copy_button"in f&&t(12,ke=f.show_copy_button),"avatar_images"in f&&t(13,oe=f.avatar_images),"sanitize_html"in f&&t(14,_e=f.sanitize_html),"render_markdown"in f&&t(15,ue=f.render_markdown),"line_breaks"in f&&t(16,fe=f.line_breaks),"autoscroll"in f&&t(39,se=f.autoscroll),"theme_mode"in f&&t(17,N=f.theme_mode),"i18n"in f&&t(18,ce=f.i18n),"layout"in f&&t(19,me=f.layout),"placeholder"in f&&t(20,he=f.placeholder),"upload"in f&&t(21,de=f.upload),"msg_format"in f&&t(22,ge=f.msg_format),"examples"in f&&t(23,be=f.examples),"_retryable"in f&&t(24,$e=f._retryable),"_undoable"in f&&t(25,De=f._undoable),"like_user_message"in f&&t(26,Ze=f.like_user_message),"root"in f&&t(27,Fe=f.root)},l.$$.update=()=>{l.$$.dirty[0]&1&&h(),l.$$.dirty[0]&268435489&&(o||p||c)&&Ht(),l.$$.dirty[0]&1|l.$$.dirty[1]&512&&(Ql(o,s)||(t(40,s=o),J("change"))),l.$$.dirty[0]&4194305&&t(32,n=o&&Mi(o)),l.$$.dirty[0]&1&&t(33,i=o&&Ot())},[o,r,_,u,d,p,k,m,w,v,$,Y,ke,oe,_e,ue,fe,N,ce,me,he,de,ge,be,$e,De,Ze,Fe,c,Ue,g,ee,n,i,b,J,St,qt,a,se,s,Jt,Gt,Wt,Yt,Kt,Qt,Xt]}class Ua extends Ca{constructor(e){super(),Ha(this,e,Fa,Da,Ma,{value:0,_fetch:1,load_component:38,allow_file_downloads:2,display_consecutive_in_same_bubble:3,latex_delimiters:4,pending_message:5,generating:6,selectable:7,likeable:8,show_share_button:9,show_copy_all_button:10,rtl:11,show_copy_button:12,avatar_images:13,sanitize_html:14,render_markdown:15,line_breaks:16,autoscroll:39,theme_mode:17,i18n:18,layout:19,placeholder:20,upload:21,msg_format:22,examples:23,_retryable:24,_undoable:25,like_user_message:26,root:27},null,[-1,-1,-1])}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),S()}get _fetch(){return this.$$.ctx[1]}set _fetch(e){this.$$set({_fetch:e}),S()}get load_component(){return this.$$.ctx[38]}set load_component(e){this.$$set({load_component:e}),S()}get allow_file_downloads(){return this.$$.ctx[2]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),S()}get display_consecutive_in_same_bubble(){return this.$$.ctx[3]}set display_consecutive_in_same_bubble(e){this.$$set({display_consecutive_in_same_bubble:e}),S()}get latex_delimiters(){return this.$$.ctx[4]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),S()}get pending_message(){return this.$$.ctx[5]}set pending_message(e){this.$$set({pending_message:e}),S()}get generating(){return this.$$.ctx[6]}set generating(e){this.$$set({generating:e}),S()}get selectable(){return this.$$.ctx[7]}set selectable(e){this.$$set({selectable:e}),S()}get likeable(){return this.$$.ctx[8]}set likeable(e){this.$$set({likeable:e}),S()}get show_share_button(){return this.$$.ctx[9]}set show_share_button(e){this.$$set({show_share_button:e}),S()}get show_copy_all_button(){return this.$$.ctx[10]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),S()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),S()}get show_copy_button(){return this.$$.ctx[12]}set show_copy_button(e){this.$$set({show_copy_button:e}),S()}get avatar_images(){return this.$$.ctx[13]}set avatar_images(e){this.$$set({avatar_images:e}),S()}get sanitize_html(){return this.$$.ctx[14]}set sanitize_html(e){this.$$set({sanitize_html:e}),S()}get render_markdown(){return this.$$.ctx[15]}set render_markdown(e){this.$$set({render_markdown:e}),S()}get line_breaks(){return this.$$.ctx[16]}set line_breaks(e){this.$$set({line_breaks:e}),S()}get autoscroll(){return this.$$.ctx[39]}set autoscroll(e){this.$$set({autoscroll:e}),S()}get theme_mode(){return this.$$.ctx[17]}set theme_mode(e){this.$$set({theme_mode:e}),S()}get i18n(){return this.$$.ctx[18]}set i18n(e){this.$$set({i18n:e}),S()}get layout(){return this.$$.ctx[19]}set layout(e){this.$$set({layout:e}),S()}get placeholder(){return this.$$.ctx[20]}set placeholder(e){this.$$set({placeholder:e}),S()}get upload(){return this.$$.ctx[21]}set upload(e){this.$$set({upload:e}),S()}get msg_format(){return this.$$.ctx[22]}set msg_format(e){this.$$set({msg_format:e}),S()}get examples(){return this.$$.ctx[23]}set examples(e){this.$$set({examples:e}),S()}get _retryable(){return this.$$.ctx[24]}set _retryable(e){this.$$set({_retryable:e}),S()}get _undoable(){return this.$$.ctx[25]}set _undoable(e){this.$$set({_undoable:e}),S()}get like_user_message(){return this.$$.ctx[26]}set like_user_message(e){this.$$set({like_user_message:e}),S()}get root(){return this.$$.ctx[27]}set root(e){this.$$set({root:e}),S()}}const Pa=Ua,{SvelteComponent:Ra,append:Oa,assign:Ja,attr:Ga,check_outros:kl,create_component:Ut,destroy_component:Pt,detach:$l,element:Wa,flush:C,get_spread_object:Ya,get_spread_update:Ka,group_outros:yl,init:Qa,insert:Cl,mount_component:Rt,safe_not_equal:Xa,space:zl,transition_in:re,transition_out:ze}=window.__gradio__svelte__internal;function Sl(l){let e,t;const n=[{autoscroll:l[24].autoscroll},{i18n:l[24].i18n},l[27],{show_progress:l[27].show_progress==="hidden"?"hidden":"minimal"}];let i={};for(let o=0;o<n.length;o+=1)i=Ja(i,n[o]);return e=new ni({props:i}),e.$on("clear_status",l[38]),{c(){Ut(e.$$.fragment)},m(o,s){Rt(e,o,s),t=!0},p(o,s){const r=s[0]&150994944?Ka(n,[s[0]&16777216&&{autoscroll:o[24].autoscroll},s[0]&16777216&&{i18n:o[24].i18n},s[0]&134217728&&Ya(o[27]),s[0]&134217728&&{show_progress:o[27].show_progress==="hidden"?"hidden":"minimal"}]):{};e.$set(r)},i(o){t||(re(e.$$.fragment,o),t=!0)},o(o){ze(e.$$.fragment,o),t=!1},d(o){Pt(e,o)}}}function Hl(l){let e,t;return e=new ti({props:{show_label:l[7],Icon:_i,float:!0,label:l[6]||"Chatbot"}}),{c(){Ut(e.$$.fragment)},m(n,i){Rt(e,n,i),t=!0},p(n,i){const o={};i[0]&128&&(o.show_label=n[7]),i[0]&64&&(o.label=n[6]||"Chatbot"),e.$set(o)},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Pt(e,n)}}}function xa(l){let e,t,n,i,o,s=l[27]&&Sl(l),r=l[7]&&Hl(l);return i=new Pa({props:{i18n:l[24].i18n,selectable:l[8],likeable:l[9],show_share_button:l[10],show_copy_all_button:l[13],value:l[36],latex_delimiters:l[23],display_consecutive_in_same_bubble:l[22],render_markdown:l[17],theme_mode:l[34],pending_message:l[27]?.status==="pending",generating:l[27]?.status==="generating",rtl:l[11],show_copy_button:l[12],like_user_message:l[26],avatar_images:l[25],sanitize_html:l[14],line_breaks:l[18],autoscroll:l[19],layout:l[15],placeholder:l[32],examples:l[33],_retryable:l[20],_undoable:l[21],upload:l[39],_fetch:l[40],load_component:l[24].load_component,msg_format:l[16],root:l[24].root,allow_file_downloads:l[35]}}),i.$on("change",l[41]),i.$on("select",l[42]),i.$on("like",l[43]),i.$on("share",l[44]),i.$on("error",l[45]),i.$on("example_select",l[46]),i.$on("option_select",l[47]),i.$on("retry",l[48]),i.$on("undo",l[49]),i.$on("clear",l[50]),i.$on("copy",l[51]),{c(){s&&s.c(),e=zl(),t=Wa("div"),r&&r.c(),n=zl(),Ut(i.$$.fragment),Ga(t,"class","wrapper svelte-g3p8na")},m(a,_){s&&s.m(a,_),Cl(a,e,_),Cl(a,t,_),r&&r.m(t,null),Oa(t,n),Rt(i,t,null),o=!0},p(a,_){a[27]?s?(s.p(a,_),_[0]&134217728&&re(s,1)):(s=Sl(a),s.c(),re(s,1),s.m(e.parentNode,e)):s&&(yl(),ze(s,1,1,()=>{s=null}),kl()),a[7]?r?(r.p(a,_),_[0]&128&&re(r,1)):(r=Hl(a),r.c(),re(r,1),r.m(t,n)):r&&(yl(),ze(r,1,1,()=>{r=null}),kl());const u={};_[0]&16777216&&(u.i18n=a[24].i18n),_[0]&256&&(u.selectable=a[8]),_[0]&512&&(u.likeable=a[9]),_[0]&1024&&(u.show_share_button=a[10]),_[0]&8192&&(u.show_copy_all_button=a[13]),_[1]&32&&(u.value=a[36]),_[0]&8388608&&(u.latex_delimiters=a[23]),_[0]&4194304&&(u.display_consecutive_in_same_bubble=a[22]),_[0]&131072&&(u.render_markdown=a[17]),_[1]&8&&(u.theme_mode=a[34]),_[0]&134217728&&(u.pending_message=a[27]?.status==="pending"),_[0]&134217728&&(u.generating=a[27]?.status==="generating"),_[0]&2048&&(u.rtl=a[11]),_[0]&4096&&(u.show_copy_button=a[12]),_[0]&67108864&&(u.like_user_message=a[26]),_[0]&33554432&&(u.avatar_images=a[25]),_[0]&16384&&(u.sanitize_html=a[14]),_[0]&262144&&(u.line_breaks=a[18]),_[0]&524288&&(u.autoscroll=a[19]),_[0]&32768&&(u.layout=a[15]),_[1]&2&&(u.placeholder=a[32]),_[1]&4&&(u.examples=a[33]),_[0]&1048576&&(u._retryable=a[20]),_[0]&2097152&&(u._undoable=a[21]),_[0]&16777216&&(u.upload=a[39]),_[0]&16777216&&(u._fetch=a[40]),_[0]&16777216&&(u.load_component=a[24].load_component),_[0]&65536&&(u.msg_format=a[16]),_[0]&16777216&&(u.root=a[24].root),_[1]&16&&(u.allow_file_downloads=a[35]),i.$set(u)},i(a){o||(re(s),re(r),re(i.$$.fragment,a),o=!0)},o(a){ze(s),ze(r),ze(i.$$.fragment,a),o=!1},d(a){a&&($l(e),$l(t)),s&&s.d(a),r&&r.d(),Pt(i)}}}function er(l){let e,t;return e=new ei({props:{elem_id:l[1],elem_classes:l[2],visible:l[3],padding:!1,scale:l[4],min_width:l[5],height:l[28],resizeable:l[29],min_height:l[30],max_height:l[31],allow_overflow:!0,flex:!0,overflow_behavior:"auto",$$slots:{default:[xa]},$$scope:{ctx:l}}}),{c(){Ut(e.$$.fragment)},m(n,i){Rt(e,n,i),t=!0},p(n,i){const o={};i[0]&2&&(o.elem_id=n[1]),i[0]&4&&(o.elem_classes=n[2]),i[0]&8&&(o.visible=n[3]),i[0]&16&&(o.scale=n[4]),i[0]&32&&(o.min_width=n[5]),i[0]&268435456&&(o.height=n[28]),i[0]&536870912&&(o.resizeable=n[29]),i[0]&1073741824&&(o.min_height=n[30]),i[1]&1&&(o.max_height=n[31]),i[0]&268435393|i[1]&2097214&&(o.$$scope={dirty:i,ctx:n}),e.$set(o)},i(n){t||(re(e.$$.fragment,n),t=!0)},o(n){ze(e.$$.fragment,n),t=!1},d(n){Pt(e,n)}}}function tr(l,e,t){let{elem_id:n=""}=e,{elem_classes:i=[]}=e,{visible:o=!0}=e,{value:s=[]}=e,{scale:r=null}=e,{min_width:a=void 0}=e,{label:_}=e,{show_label:u=!0}=e,{root:c}=e,{_selectable:b=!1}=e,{likeable:h=!1}=e,{show_share_button:d=!1}=e,{rtl:p=!1}=e,{show_copy_button:k=!0}=e,{show_copy_all_button:m=!1}=e,{sanitize_html:w=!0}=e,{layout:v="bubble"}=e,{type:$="tuples"}=e,{render_markdown:Y=!0}=e,{line_breaks:ke=!0}=e,{autoscroll:oe=!0}=e,{_retryable:_e=!1}=e,{_undoable:ue=!1}=e,{group_consecutive_messages:fe=!0}=e,{latex_delimiters:se}=e,{gradio:N}=e,ce=[],{avatar_images:me=[null,null]}=e,{like_user_message:he=!1}=e,{loading_status:de=void 0}=e,{height:ge}=e,{resizeable:be}=e,{min_height:$e}=e,{max_height:De}=e,{placeholder:Ze=null}=e,{examples:Fe=null}=e,{theme_mode:Ue}=e,{allow_file_downloads:g=!0}=e;const ee=()=>N.dispatch("clear_status",de),J=(...f)=>N.client.upload(...f),zt=(...f)=>N.client.fetch(...f),St=()=>N.dispatch("change",s),Ht=f=>N.dispatch("select",f.detail),qt=f=>N.dispatch("like",f.detail),Ot=f=>N.dispatch("share",f.detail),Jt=f=>N.dispatch("error",f.detail),Gt=f=>N.dispatch("example_select",f.detail),Wt=f=>N.dispatch("option_select",f.detail),Yt=f=>N.dispatch("retry",f.detail),Kt=f=>N.dispatch("undo",f.detail),Qt=()=>{t(0,s=[]),N.dispatch("clear")},Xt=f=>N.dispatch("copy",f.detail);return l.$$set=f=>{"elem_id"in f&&t(1,n=f.elem_id),"elem_classes"in f&&t(2,i=f.elem_classes),"visible"in f&&t(3,o=f.visible),"value"in f&&t(0,s=f.value),"scale"in f&&t(4,r=f.scale),"min_width"in f&&t(5,a=f.min_width),"label"in f&&t(6,_=f.label),"show_label"in f&&t(7,u=f.show_label),"root"in f&&t(37,c=f.root),"_selectable"in f&&t(8,b=f._selectable),"likeable"in f&&t(9,h=f.likeable),"show_share_button"in f&&t(10,d=f.show_share_button),"rtl"in f&&t(11,p=f.rtl),"show_copy_button"in f&&t(12,k=f.show_copy_button),"show_copy_all_button"in f&&t(13,m=f.show_copy_all_button),"sanitize_html"in f&&t(14,w=f.sanitize_html),"layout"in f&&t(15,v=f.layout),"type"in f&&t(16,$=f.type),"render_markdown"in f&&t(17,Y=f.render_markdown),"line_breaks"in f&&t(18,ke=f.line_breaks),"autoscroll"in f&&t(19,oe=f.autoscroll),"_retryable"in f&&t(20,_e=f._retryable),"_undoable"in f&&t(21,ue=f._undoable),"group_consecutive_messages"in f&&t(22,fe=f.group_consecutive_messages),"latex_delimiters"in f&&t(23,se=f.latex_delimiters),"gradio"in f&&t(24,N=f.gradio),"avatar_images"in f&&t(25,me=f.avatar_images),"like_user_message"in f&&t(26,he=f.like_user_message),"loading_status"in f&&t(27,de=f.loading_status),"height"in f&&t(28,ge=f.height),"resizeable"in f&&t(29,be=f.resizeable),"min_height"in f&&t(30,$e=f.min_height),"max_height"in f&&t(31,De=f.max_height),"placeholder"in f&&t(32,Ze=f.placeholder),"examples"in f&&t(33,Fe=f.examples),"theme_mode"in f&&t(34,Ue=f.theme_mode),"allow_file_downloads"in f&&t(35,g=f.allow_file_downloads)},l.$$.update=()=>{l.$$.dirty[0]&65537|l.$$.dirty[1]&64&&t(36,ce=$==="tuples"?qi(s,c):Hi(s,c))},[s,n,i,o,r,a,_,u,b,h,d,p,k,m,w,v,$,Y,ke,oe,_e,ue,fe,se,N,me,he,de,ge,be,$e,De,Ze,Fe,Ue,g,ce,c,ee,J,zt,St,Ht,qt,Ot,Jt,Gt,Wt,Yt,Kt,Qt,Xt]}class Mr extends Ra{constructor(e){super(),Qa(this,e,tr,er,Xa,{elem_id:1,elem_classes:2,visible:3,value:0,scale:4,min_width:5,label:6,show_label:7,root:37,_selectable:8,likeable:9,show_share_button:10,rtl:11,show_copy_button:12,show_copy_all_button:13,sanitize_html:14,layout:15,type:16,render_markdown:17,line_breaks:18,autoscroll:19,_retryable:20,_undoable:21,group_consecutive_messages:22,latex_delimiters:23,gradio:24,avatar_images:25,like_user_message:26,loading_status:27,height:28,resizeable:29,min_height:30,max_height:31,placeholder:32,examples:33,theme_mode:34,allow_file_downloads:35},null,[-1,-1])}get elem_id(){return this.$$.ctx[1]}set elem_id(e){this.$$set({elem_id:e}),C()}get elem_classes(){return this.$$.ctx[2]}set elem_classes(e){this.$$set({elem_classes:e}),C()}get visible(){return this.$$.ctx[3]}set visible(e){this.$$set({visible:e}),C()}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),C()}get scale(){return this.$$.ctx[4]}set scale(e){this.$$set({scale:e}),C()}get min_width(){return this.$$.ctx[5]}set min_width(e){this.$$set({min_width:e}),C()}get label(){return this.$$.ctx[6]}set label(e){this.$$set({label:e}),C()}get show_label(){return this.$$.ctx[7]}set show_label(e){this.$$set({show_label:e}),C()}get root(){return this.$$.ctx[37]}set root(e){this.$$set({root:e}),C()}get _selectable(){return this.$$.ctx[8]}set _selectable(e){this.$$set({_selectable:e}),C()}get likeable(){return this.$$.ctx[9]}set likeable(e){this.$$set({likeable:e}),C()}get show_share_button(){return this.$$.ctx[10]}set show_share_button(e){this.$$set({show_share_button:e}),C()}get rtl(){return this.$$.ctx[11]}set rtl(e){this.$$set({rtl:e}),C()}get show_copy_button(){return this.$$.ctx[12]}set show_copy_button(e){this.$$set({show_copy_button:e}),C()}get show_copy_all_button(){return this.$$.ctx[13]}set show_copy_all_button(e){this.$$set({show_copy_all_button:e}),C()}get sanitize_html(){return this.$$.ctx[14]}set sanitize_html(e){this.$$set({sanitize_html:e}),C()}get layout(){return this.$$.ctx[15]}set layout(e){this.$$set({layout:e}),C()}get type(){return this.$$.ctx[16]}set type(e){this.$$set({type:e}),C()}get render_markdown(){return this.$$.ctx[17]}set render_markdown(e){this.$$set({render_markdown:e}),C()}get line_breaks(){return this.$$.ctx[18]}set line_breaks(e){this.$$set({line_breaks:e}),C()}get autoscroll(){return this.$$.ctx[19]}set autoscroll(e){this.$$set({autoscroll:e}),C()}get _retryable(){return this.$$.ctx[20]}set _retryable(e){this.$$set({_retryable:e}),C()}get _undoable(){return this.$$.ctx[21]}set _undoable(e){this.$$set({_undoable:e}),C()}get group_consecutive_messages(){return this.$$.ctx[22]}set group_consecutive_messages(e){this.$$set({group_consecutive_messages:e}),C()}get latex_delimiters(){return this.$$.ctx[23]}set latex_delimiters(e){this.$$set({latex_delimiters:e}),C()}get gradio(){return this.$$.ctx[24]}set gradio(e){this.$$set({gradio:e}),C()}get avatar_images(){return this.$$.ctx[25]}set avatar_images(e){this.$$set({avatar_images:e}),C()}get like_user_message(){return this.$$.ctx[26]}set like_user_message(e){this.$$set({like_user_message:e}),C()}get loading_status(){return this.$$.ctx[27]}set loading_status(e){this.$$set({loading_status:e}),C()}get height(){return this.$$.ctx[28]}set height(e){this.$$set({height:e}),C()}get resizeable(){return this.$$.ctx[29]}set resizeable(e){this.$$set({resizeable:e}),C()}get min_height(){return this.$$.ctx[30]}set min_height(e){this.$$set({min_height:e}),C()}get max_height(){return this.$$.ctx[31]}set max_height(e){this.$$set({max_height:e}),C()}get placeholder(){return this.$$.ctx[32]}set placeholder(e){this.$$set({placeholder:e}),C()}get examples(){return this.$$.ctx[33]}set examples(e){this.$$set({examples:e}),C()}get theme_mode(){return this.$$.ctx[34]}set theme_mode(e){this.$$set({theme_mode:e}),C()}get allow_file_downloads(){return this.$$.ctx[35]}set allow_file_downloads(e){this.$$set({allow_file_downloads:e}),C()}}export{Pa as BaseChatBot,Mr as default};
//# sourceMappingURL=Index-CB5fNOZY.js.map
