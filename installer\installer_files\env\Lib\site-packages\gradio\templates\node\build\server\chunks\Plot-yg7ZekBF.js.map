{"version": 3, "file": "Plot-yg7ZekBF.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Plot.js"], "sourcesContent": ["import { create_ssr_component, validate_component, missing_component } from \"svelte/internal\";\nimport { d as Empty, P as Plot$1 } from \"./client.js\";\nconst Plot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let _value;\n  let { colors = [] } = $$props;\n  let { show_label } = $$props;\n  let { theme_mode } = $$props;\n  let { caption } = $$props;\n  let { bokeh_version } = $$props;\n  let { show_actions_button } = $$props;\n  let { gradio } = $$props;\n  let { x_lim = null } = $$props;\n  let { _selectable } = $$props;\n  let PlotComponent = null;\n  let _type = value?.type;\n  const plotTypeMapping = {\n    plotly: () => import(\"./PlotlyPlot.js\"),\n    bokeh: () => import(\"./BokehPlot.js\"),\n    altair: () => import(\"./AltairPlot.js\"),\n    matplotlib: () => import(\"./MatplotlibPlot.js\")\n  };\n  let loadedPlotTypeMapping = {};\n  const is_browser = typeof window !== \"undefined\";\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.colors === void 0 && $$bindings.colors && colors !== void 0)\n    $$bindings.colors(colors);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.caption === void 0 && $$bindings.caption && caption !== void 0)\n    $$bindings.caption(caption);\n  if ($$props.bokeh_version === void 0 && $$bindings.bokeh_version && bokeh_version !== void 0)\n    $$bindings.bokeh_version(bokeh_version);\n  if ($$props.show_actions_button === void 0 && $$bindings.show_actions_button && show_actions_button !== void 0)\n    $$bindings.show_actions_button(show_actions_button);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.x_lim === void 0 && $$bindings.x_lim && x_lim !== void 0)\n    $$bindings.x_lim(x_lim);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  {\n    if (value !== _value) {\n      let type = value?.type;\n      if (type !== _type) {\n        PlotComponent = null;\n      }\n      if (type && type in plotTypeMapping && is_browser) {\n        if (loadedPlotTypeMapping[type]) {\n          PlotComponent = loadedPlotTypeMapping[type];\n        } else {\n          plotTypeMapping[type]().then((module) => {\n            PlotComponent = module.default;\n            loadedPlotTypeMapping[type] = PlotComponent;\n          });\n        }\n      }\n      _value = value;\n      _type = type;\n    }\n  }\n  return `${value && PlotComponent ? `${validate_component(PlotComponent || missing_component, \"svelte:component\").$$render(\n    $$result,\n    {\n      value,\n      colors,\n      theme_mode,\n      show_label,\n      caption,\n      bokeh_version,\n      show_actions_button,\n      gradio,\n      _selectable,\n      x_lim\n    },\n    {},\n    {}\n  )}` : `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n    default: () => {\n      return `${validate_component(Plot$1, \"PlotIcon\").$$render($$result, {}, {}, {})}`;\n    }\n  })}`}`;\n});\nexport {\n  Plot as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEK,MAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC5E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,EAAE,MAAM,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,mBAAmB,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC;AAC1B,EAAE,MAAM,eAAe,GAAG;AAC1B,IAAI,MAAM,EAAE,MAAM,OAAO,0BAAiB,CAAC;AAC3C,IAAI,KAAK,EAAE,MAAM,OAAO,yBAAgB,CAAC;AACzC,IAAI,MAAM,EAAE,MAAM,OAAO,0BAAiB,CAAC;AAC3C,IAAI,UAAU,EAAE,MAAM,OAAO,8BAAqB,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,IAAI,qBAAqB,GAAG,EAAE,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE;AACF,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,MAAM,IAAI,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;AAC7B,MAAM,IAAI,IAAI,KAAK,KAAK,EAAE;AAC1B,QAAQ,aAAa,GAAG,IAAI,CAAC;AAC7B,OAAO;AACP,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,eAAe,IAAI,UAAU,EAAE;AACzD,QAAQ,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;AACzC,UAAU,aAAa,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACtD,SAAS,MAAM;AACf,UAAU,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACnD,YAAY,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC;AAC3C,YAAY,qBAAqB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;AACxD,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,MAAM,MAAM,GAAG,KAAK,CAAC;AACrB,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC,aAAa,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC3H,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,MAAM;AACZ,MAAM,WAAW;AACjB,MAAM,KAAK;AACX,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC5G,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACT,CAAC;;;;"}