# Windows-specific configuration for <PERSON><PERSON> <PERSON>
# This configuration file contains Windows-optimized settings

# Output settings
output_image_format: "png"
output_video_format: "mp4"
output_video_codec: "libx264"
video_quality: 18

# Performance settings
max_threads: 4  # Conservative setting for Windows
memory_limit: 8  # GB - Conservative for Windows systems

# Path settings - use forward slashes, they work on Windows too
output_template: "{file}_{index}"

# Server settings
server_name: "127.0.0.1"
server_port: 7860
server_share: false

# Windows-specific optimizations
use_gpu: true
execution_providers: ["CUDAExecutionProvider", "DmlExecutionProvider", "CPUExecutionProvider"]

# Logging
log_level: "error"  # Reduce console spam on Windows

# UI settings
ui_theme: "default"
preview_size: 512

# Processing settings
face_swap_mode: "selected"
blend_ratio: 0.65
distance_threshold: 0.65
subsample_size: 128

# Enhancement settings
enhancer_model: "GFPGAN"

# Video processing
keep_frames: false
skip_audio: false
wait_after_extraction: false
