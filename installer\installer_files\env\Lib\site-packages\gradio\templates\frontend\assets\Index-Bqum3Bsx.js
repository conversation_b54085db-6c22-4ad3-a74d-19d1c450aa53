import{P as q}from"./prism-python-B8dcvKZU.js";import"./index-BkoKOheB.js";import"./svelte/svelte.js";(function(s){s.languages.typescript=s.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),s.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete s.languages.typescript.parameter,delete s.languages.typescript["literal-property"];var e=s.languages.extend("typescript",{});delete e["class-name"],s.languages.typescript["class-name"].inside=e,s.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e}}}}),s.languages.ts=s.languages.typescript})(Prism);const{HtmlTag:D,SvelteComponent:J,append:c,attr:g,binding_callbacks:K,destroy_block:Q,detach:y,element:k,empty:N,ensure_array_like:S,flush:z,init:U,insert:v,listen:W,noop:L,safe_not_equal:X,set_data:R,set_style:Y,space:w,text:A,toggle_class:H,update_keyed_each:P}=window.__gradio__svelte__internal;function M(s,e,l){const t=s.slice();return t[11]=e[l].type,t[12]=e[l].description,t[13]=e[l].default,t[14]=e[l].name,t}function T(s){let e,l,t,n,i,a,f,u,_;return{c(){e=k("div"),l=k("span"),t=A(s[1]),n=w(),i=k("button"),a=A("▼"),g(l,"class","title svelte-1k7zb06"),g(i,"class","toggle-all svelte-1k7zb06"),g(i,"title",f=s[4]?"Close All":"Open All"),g(e,"class","header svelte-1k7zb06")},m(h,b){v(h,e,b),c(e,l),c(l,t),c(e,n),c(e,i),c(i,a),u||(_=W(i,"click",s[5]),u=!0)},p(h,b){b&2&&R(t,h[1]),b&16&&f!==(f=h[4]?"Close All":"Open All")&&g(i,"title",f)},d(h){h&&y(e),u=!1,_()}}}function Z(s){let e=[],l=new Map,t,n=S(s[3]);const i=a=>a[14];for(let a=0;a<n.length;a+=1){let f=M(s,n,a),u=i(f);l.set(u,e[a]=I(u,f))}return{c(){for(let a=0;a<e.length;a+=1)e[a].c();t=N()},m(a,f){for(let u=0;u<e.length;u+=1)e[u]&&e[u].m(a,f);v(a,t,f)},p(a,f){f&9&&(n=S(a[3]),e=P(e,f,i,1,a,n,l,t.parentNode,Q,I,t,M))},d(a){a&&y(t);for(let f=0;f<e.length;f+=1)e[f].d(a)}}}function E(s){let e,l,t=s[11]+"",n;return{c(){e=A(": "),l=new D(!1),n=N(),l.a=n},m(i,a){v(i,e,a),l.m(t,i,a),v(i,n,a)},p(i,a){a&8&&t!==(t=i[11]+"")&&l.p(t)},d(i){i&&(y(e),y(n),l.d())}}}function O(s){let e,l,t,n,i,a,f=s[13]+"";return{c(){e=k("div"),l=k("span"),l.textContent="default",t=w(),n=k("code"),i=A("= "),a=new D(!1),g(l,"class","svelte-1k7zb06"),Y(l,"padding-right","4px"),a.a=null,g(n,"class","svelte-1k7zb06"),g(e,"class","default svelte-1k7zb06"),H(e,"last",!s[12])},m(u,_){v(u,e,_),c(e,l),c(e,t),c(e,n),c(n,i),a.m(f,n)},p(u,_){_&8&&f!==(f=u[13]+"")&&a.p(f),_&8&&H(e,"last",!u[12])},d(u){u&&y(e)}}}function j(s){let e,l,t=B(s[12])+"";return{c(){e=k("div"),l=k("p"),g(e,"class","description svelte-1k7zb06")},m(n,i){v(n,e,i),c(e,l),l.innerHTML=t},p(n,i){i&8&&t!==(t=B(n[12])+"")&&(l.innerHTML=t)},d(n){n&&y(e)}}}function I(s,e){let l,t,n,i,a=e[14]+"",f,u,_,h,b,o=e[11]&&E(e),d=e[13]&&O(e),r=e[12]&&j(e);return{key:s,first:null,c(){l=k("details"),t=k("summary"),n=k("pre"),i=k("code"),f=A(a),o&&o.c(),_=w(),d&&d.c(),h=w(),r&&r.c(),b=w(),g(i,"class","svelte-1k7zb06"),g(n,"class",u="language-"+e[0]+" svelte-1k7zb06"),g(t,"class","type svelte-1k7zb06"),g(l,"class","param md svelte-1k7zb06"),this.first=l},m(p,m){v(p,l,m),c(l,t),c(t,n),c(n,i),c(i,f),o&&o.m(i,null),c(l,_),d&&d.m(l,null),c(l,h),r&&r.m(l,null),c(l,b)},p(p,m){e=p,m&8&&a!==(a=e[14]+"")&&R(f,a),e[11]?o?o.p(e,m):(o=E(e),o.c(),o.m(i,null)):o&&(o.d(1),o=null),m&1&&u!==(u="language-"+e[0]+" svelte-1k7zb06")&&g(n,"class",u),e[13]?d?d.p(e,m):(d=O(e),d.c(),d.m(l,h)):d&&(d.d(1),d=null),e[12]?r?r.p(e,m):(r=j(e),r.c(),r.m(l,b)):r&&(r.d(1),r=null)},d(p){p&&y(l),o&&o.d(),d&&d.d(),r&&r.d()}}}function x(s){let e,l,t=s[1]!==null&&T(s),n=s[3]&&Z(s);return{c(){e=k("div"),t&&t.c(),l=w(),n&&n.c(),g(e,"class","wrap svelte-1k7zb06")},m(i,a){v(i,e,a),t&&t.m(e,null),c(e,l),n&&n.m(e,null),s[8](e)},p(i,[a]){i[1]!==null?t?t.p(i,a):(t=T(i),t.c(),t.m(e,l)):t&&(t.d(1),t=null),i[3]?n?n.p(i,a):(n=Z(i),n.c(),n.m(e,null)):n&&(n.d(1),n=null)},i:L,o:L,d(i){i&&y(e),t&&t.d(),n&&n.d(),s[8](null)}}}function B(s){return s.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank">$1</a>')}function ee(s,e,l){let{docs:t}=e,{lang:n="python"}=e,{linkify:i=[]}=e,{header:a}=e,f,u,_=!1;function h(r,p){let m=q.highlight(r,q.languages[p],p);for(const F of i)m=m.replace(new RegExp(F,"g"),`<a href="#h-${F.toLocaleLowerCase()}">${F}</a>`);return m}function b(r,p){return r?Object.entries(r).map(([m,{type:F,description:V,default:C}])=>{let G=F?h(F,p):null;return{name:m,type:G,description:V,default:C?h(C,p):null}}):[]}function o(){l(4,_=!_),f.querySelectorAll(".param").forEach(p=>{p instanceof HTMLDetailsElement&&(p.open=_)})}function d(r){K[r?"unshift":"push"](()=>{f=r,l(2,f)})}return s.$$set=r=>{"docs"in r&&l(6,t=r.docs),"lang"in r&&l(0,n=r.lang),"linkify"in r&&l(7,i=r.linkify),"header"in r&&l(1,a=r.header)},s.$$.update=()=>{s.$$.dirty&65&&l(3,u=b(t,n))},[n,a,f,u,_,o,t,i,d]}class te extends J{constructor(e){super(),U(this,e,ee,x,X,{docs:6,lang:0,linkify:7,header:1})}get docs(){return this.$$.ctx[6]}set docs(e){this.$$set({docs:e}),z()}get lang(){return this.$$.ctx[0]}set lang(e){this.$$set({lang:e}),z()}get linkify(){return this.$$.ctx[7]}set linkify(e){this.$$set({linkify:e}),z()}get header(){return this.$$.ctx[1]}set header(e){this.$$set({header:e}),z()}}const{SvelteComponent:le,create_component:ne,destroy_component:ie,flush:$,init:ae,mount_component:se,safe_not_equal:re,transition_in:fe,transition_out:ue}=window.__gradio__svelte__internal;function ce(s){let e,l;return e=new te({props:{docs:s[0],linkify:s[1],header:s[2]}}),{c(){ne(e.$$.fragment)},m(t,n){se(e,t,n),l=!0},p(t,[n]){const i={};n&1&&(i.docs=t[0]),n&2&&(i.linkify=t[1]),n&4&&(i.header=t[2]),e.$set(i)},i(t){l||(fe(e.$$.fragment,t),l=!0)},o(t){ue(e.$$.fragment,t),l=!1},d(t){ie(e,t)}}}function oe(s,e,l){let{value:t}=e,{linkify:n=[]}=e,{header:i=null}=e;return s.$$set=a=>{"value"in a&&l(0,t=a.value),"linkify"in a&&l(1,n=a.linkify),"header"in a&&l(2,i=a.header)},[t,n,i]}class pe extends le{constructor(e){super(),ae(this,e,oe,ce,re,{value:0,linkify:1,header:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),$()}get linkify(){return this.$$.ctx[1]}set linkify(e){this.$$set({linkify:e}),$()}get header(){return this.$$.ctx[2]}set header(e){this.$$set({header:e}),$()}}export{pe as default};
//# sourceMappingURL=Index-Bqum3Bsx.js.map
