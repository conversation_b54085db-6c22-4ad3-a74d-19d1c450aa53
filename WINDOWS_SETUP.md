# Windows Setup Guide for <PERSON><PERSON>

This guide helps you set up and run <PERSON><PERSON> Floyd on Windows systems.

## Quick Start

### Option 1: Using the Windows Installer (Recommended)
1. Run `installer/windows_run.bat` as Administrator
2. The installer will automatically:
   - Download and install Miniconda
   - Create a Python environment
   - Install FFmpeg
   - Install all dependencies
   - Launch the application

### Option 2: Manual Setup
1. Ensure Python 3.9+ is installed
2. Run `run_windows.bat` for a simple startup
3. Or use: `python run.py`

## System Requirements

- Windows 10/11 (64-bit)
- Python 3.9 or higher
- 8GB+ RAM (16GB recommended)
- NVIDIA GPU with CUDA support (optional but recommended)
- 10GB+ free disk space

## Common Issues and Solutions

### 1. FFmpeg Not Found
**Error**: "ffmpeg is not installed"
**Solution**: 
- Run the Windows installer which automatically installs FFmpeg
- Or manually download FFmpeg and add it to your PATH

### 2. CUDA/GPU Issues
**Error**: CUDA-related errors
**Solutions**:
- Install NVIDIA drivers
- Install CUDA Toolkit 11.8 or 12.4
- Use CPU mode if GPU issues persist

### 3. Path with Spaces
**Error**: Installation fails in paths with spaces
**Solution**: 
- Move the project to a path without spaces (e.g., `C:\roop-floyd`)
- The installer checks for this automatically

### 4. Permission Issues
**Error**: Access denied errors
**Solutions**:
- Run Command Prompt as Administrator
- Check antivirus software isn't blocking files
- Ensure you have write permissions to the installation directory

### 5. Virtual Camera Issues
**Error**: Camera not detected
**Solutions**:
- Install OBS Virtual Camera or similar software
- Ensure camera permissions are granted
- Check Windows Camera privacy settings

## Configuration

Use `config_windows.yaml` for Windows-optimized settings:
- Conservative memory limits
- Optimized thread counts
- Windows-specific paths

## Performance Tips

1. **GPU Acceleration**: Ensure CUDA is properly installed
2. **Memory**: Close other applications to free up RAM
3. **Storage**: Use SSD for better performance
4. **Antivirus**: Add project folder to antivirus exclusions

## Troubleshooting Commands

```batch
# Check Python version
python --version

# Check if CUDA is available
python -c "import torch; print(torch.cuda.is_available())"

# Check installed packages
pip list

# Reinstall requirements
pip install -r requirements.txt --force-reinstall
```

## Getting Help

If you encounter issues:
1. Check this guide first
2. Look at the error messages carefully
3. Try running with `--help` for command options
4. Check the GitHub issues page

## File Structure

```
roop-floyd/
├── installer/
│   └── windows_run.bat     # Main Windows installer
├── run_windows.bat         # Simple startup script
├── config_windows.yaml     # Windows-specific config
├── WINDOWS_SETUP.md       # This guide
└── ...
```
