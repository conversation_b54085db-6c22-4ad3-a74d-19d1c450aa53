const{SvelteComponent:r,append:d,attr:g,detach:_,element:h,flush:u,init:v,insert:o,noop:f,safe_not_equal:y,set_data:m,text:b,toggle_class:i}=window.__gradio__svelte__internal;function q(s){let e,a;return{c(){e=h("div"),a=b(s[0]),g(e,"class","svelte-1ayixqk"),i(e,"table",s[1]==="table"),i(e,"gallery",s[1]==="gallery"),i(e,"selected",s[2])},m(t,l){o(t,e,l),d(e,a)},p(t,[l]){l&1&&m(a,t[0]),l&2&&i(e,"table",t[1]==="table"),l&2&&i(e,"gallery",t[1]==="gallery"),l&4&&i(e,"selected",t[2])},i:f,o:f,d(t){t&&_(e)}}}function w(s,e,a){let{value:t}=e,{type:l}=e,{selected:c=!1}=e;return s.$$set=n=>{"value"in n&&a(0,t=n.value),"type"in n&&a(1,l=n.type),"selected"in n&&a(2,c=n.selected)},[t,l,c]}class x extends r{constructor(e){super(),v(this,e,w,q,y,{value:0,type:1,selected:2})}get value(){return this.$$.ctx[0]}set value(e){this.$$set({value:e}),u()}get type(){return this.$$.ctx[1]}set type(e){this.$$set({type:e}),u()}get selected(){return this.$$.ctx[2]}set selected(e){this.$$set({selected:e}),u()}}export{x as default};
//# sourceMappingURL=Example-DxdiEFS_.js.map
