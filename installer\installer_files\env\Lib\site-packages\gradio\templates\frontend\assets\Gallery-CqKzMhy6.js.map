{"version": 3, "file": "Gallery-CqKzMhy6.js", "sources": ["../../../../node_modules/.pnpm/dequal@2.0.2/node_modules/dequal/dist/index.mjs", "../../../../js/gallery/shared/utils.ts", "../../../../js/gallery/shared/Gallery.svelte"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import { uploadToHuggingFace } from \"@gradio/utils\";\nimport type { FileData } from \"@gradio/client\";\n\nexport async function format_gallery_for_sharing(\n\tvalue: [FileData, string | null][] | null\n): Promise<string> {\n\tif (!value) return \"\";\n\tlet urls = await Promise.all(\n\t\tvalue.map(async ([image, _]) => {\n\t\t\tif (image === null || !image.url) return \"\";\n\t\t\treturn await uploadToHuggingFace(image.url, \"url\");\n\t\t})\n\t);\n\n\treturn `<div style=\"display: flex; flex-wrap: wrap; gap: 16px\">${urls\n\t\t.map((url) => `<img src=\"${url}\" style=\"height: 400px\" />`)\n\t\t.join(\"\")}</div>`;\n}\n", "<script lang=\"ts\">\n\timport {\n\t\tBlockLabel,\n\t\tEmpty,\n\t\tShareButton,\n\t\tIconButton,\n\t\tIconButtonWrapper,\n\t\tFullscreenButton\n\t} from \"@gradio/atoms\";\n\timport { ModifyUpload } from \"@gradio/upload\";\n\timport type { SelectData } from \"@gradio/utils\";\n\timport { Image } from \"@gradio/image/shared\";\n\timport { Video } from \"@gradio/video/shared\";\n\timport { dequal } from \"dequal\";\n\timport { createEventDispatcher, onMount } from \"svelte\";\n\timport { tick } from \"svelte\";\n\timport type { GalleryImage, GalleryVideo } from \"../types\";\n\n\timport {\n\t\tDownload,\n\t\tImage as ImageIcon,\n\t\tMaximize,\n\t\tMinimize,\n\t\tClear,\n\t\tPlay\n\t} from \"@gradio/icons\";\n\timport { FileData } from \"@gradio/client\";\n\timport { format_gallery_for_sharing } from \"./utils\";\n\timport type { I18nFormatter } from \"@gradio/utils\";\n\n\ttype GalleryData = GalleryImage | GalleryVideo;\n\n\texport let show_label = true;\n\texport let label: string;\n\texport let value: GalleryData[] | null = null;\n\texport let columns: number | number[] | undefined = [2];\n\texport let rows: number | number[] | undefined = undefined;\n\texport let height: number | \"auto\" = \"auto\";\n\texport let preview: boolean;\n\texport let allow_preview = true;\n\texport let object_fit: \"contain\" | \"cover\" | \"fill\" | \"none\" | \"scale-down\" =\n\t\t\"cover\";\n\texport let show_share_button = false;\n\texport let show_download_button = false;\n\texport let i18n: I18nFormatter;\n\texport let selected_index: number | null = null;\n\texport let interactive: boolean;\n\texport let _fetch: typeof fetch;\n\texport let mode: \"normal\" | \"minimal\" = \"normal\";\n\texport let show_fullscreen_button = true;\n\texport let display_icon_button_wrapper_top_corner = false;\n\n\tlet is_full_screen = false;\n\tlet image_container: HTMLElement;\n\n\tconst dispatch = createEventDispatcher<{\n\t\tchange: undefined;\n\t\tselect: SelectData;\n\t}>();\n\n\t// tracks whether the value of the gallery was reset\n\tlet was_reset = true;\n\n\t$: was_reset = value == null || value.length === 0 ? true : was_reset;\n\n\tlet resolved_value: GalleryData[] | null = null;\n\n\t$: resolved_value =\n\t\tvalue == null\n\t\t\t? null\n\t\t\t: (value.map((data) => {\n\t\t\t\t\tif (\"video\" in data) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tvideo: data.video as FileData,\n\t\t\t\t\t\t\tcaption: data.caption\n\t\t\t\t\t\t};\n\t\t\t\t\t} else if (\"image\" in data) {\n\t\t\t\t\t\treturn { image: data.image as FileData, caption: data.caption };\n\t\t\t\t\t}\n\t\t\t\t\treturn {};\n\t\t\t\t}) as GalleryData[]);\n\n\tlet prev_value: GalleryData[] | null = value;\n\tif (selected_index == null && preview && value?.length) {\n\t\tselected_index = 0;\n\t}\n\tlet old_selected_index: number | null = selected_index;\n\n\t$: if (!dequal(prev_value, value)) {\n\t\t// When value is falsy (clear button or first load),\n\t\t// preview determines the selected image\n\t\tif (was_reset) {\n\t\t\tselected_index = preview && value?.length ? 0 : null;\n\t\t\twas_reset = false;\n\t\t\t// Otherwise we keep the selected_index the same if the\n\t\t\t// gallery has at least as many elements as it did before\n\t\t} else {\n\t\t\tselected_index =\n\t\t\t\tselected_index != null && value != null && selected_index < value.length\n\t\t\t\t\t? selected_index\n\t\t\t\t\t: null;\n\t\t}\n\t\tdispatch(\"change\");\n\t\tprev_value = value;\n\t}\n\n\t$: previous =\n\t\t((selected_index ?? 0) + (resolved_value?.length ?? 0) - 1) %\n\t\t(resolved_value?.length ?? 0);\n\t$: next = ((selected_index ?? 0) + 1) % (resolved_value?.length ?? 0);\n\n\tfunction handle_preview_click(event: MouseEvent): void {\n\t\tconst element = event.target as HTMLElement;\n\t\tconst x = event.offsetX;\n\t\tconst width = element.offsetWidth;\n\t\tconst centerX = width / 2;\n\n\t\tif (x < centerX) {\n\t\t\tselected_index = previous;\n\t\t} else {\n\t\t\tselected_index = next;\n\t\t}\n\t}\n\n\tfunction on_keydown(e: KeyboardEvent): void {\n\t\tswitch (e.code) {\n\t\t\tcase \"Escape\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = null;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowLeft\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = previous;\n\t\t\t\tbreak;\n\t\t\tcase \"ArrowRight\":\n\t\t\t\te.preventDefault();\n\t\t\t\tselected_index = next;\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\t$: {\n\t\tif (selected_index !== old_selected_index) {\n\t\t\told_selected_index = selected_index;\n\t\t\tif (selected_index !== null) {\n\t\t\t\tdispatch(\"select\", {\n\t\t\t\t\tindex: selected_index,\n\t\t\t\t\tvalue: resolved_value?.[selected_index]\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n\t$: if (allow_preview) {\n\t\tscroll_to_img(selected_index);\n\t}\n\n\tlet el: HTMLButtonElement[] = [];\n\tlet container_element: HTMLDivElement;\n\n\tasync function scroll_to_img(index: number | null): Promise<void> {\n\t\tif (typeof index !== \"number\") return;\n\t\tawait tick();\n\n\t\tif (el[index] === undefined) return;\n\n\t\tel[index]?.focus();\n\n\t\tconst { left: container_left, width: container_width } =\n\t\t\tcontainer_element.getBoundingClientRect();\n\t\tconst { left, width } = el[index].getBoundingClientRect();\n\n\t\tconst relative_left = left - container_left;\n\n\t\tconst pos =\n\t\t\trelative_left +\n\t\t\twidth / 2 -\n\t\t\tcontainer_width / 2 +\n\t\t\tcontainer_element.scrollLeft;\n\n\t\tif (container_element && typeof container_element.scrollTo === \"function\") {\n\t\t\tcontainer_element.scrollTo({\n\t\t\t\tleft: pos < 0 ? 0 : pos,\n\t\t\t\tbehavior: \"smooth\"\n\t\t\t});\n\t\t}\n\t}\n\n\tlet window_height = 0;\n\n\t// Unlike `gr.Image()`, images specified via remote URLs are not cached in the server\n\t// and their remote URLs are directly passed to the client as `value[].image.url`.\n\t// The `download` attribute of the <a> tag doesn't work for remote URLs (https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#download),\n\t// so we need to download the image via JS as below.\n\tasync function download(file_url: string, name: string): Promise<void> {\n\t\tlet response;\n\t\ttry {\n\t\t\tresponse = await _fetch(file_url);\n\t\t} catch (error) {\n\t\t\tif (error instanceof TypeError) {\n\t\t\t\t// If CORS is not allowed (https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch#checking_that_the_fetch_was_successful),\n\t\t\t\t// open the link in a new tab instead, mimicing the behavior of the `download` attribute for remote URLs,\n\t\t\t\t// which is not ideal, but a reasonable fallback.\n\t\t\t\twindow.open(file_url, \"_blank\", \"noreferrer\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t\tconst blob = await response.blob();\n\t\tconst url = URL.createObjectURL(blob);\n\t\tconst link = document.createElement(\"a\");\n\t\tlink.href = url;\n\t\tlink.download = name;\n\t\tlink.click();\n\t\tURL.revokeObjectURL(url);\n\t}\n\n\t$: selected_media =\n\t\tselected_index != null && resolved_value != null\n\t\t\t? resolved_value[selected_index]\n\t\t\t: null;\n\n\tonMount(() => {\n\t\tdocument.addEventListener(\"fullscreenchange\", () => {\n\t\t\tis_full_screen = !!document.fullscreenElement;\n\t\t});\n\t});\n</script>\n\n<svelte:window bind:innerHeight={window_height} />\n\n{#if show_label}\n\t<BlockLabel {show_label} Icon={ImageIcon} label={label || \"Gallery\"} />\n{/if}\n{#if value == null || resolved_value == null || resolved_value.length === 0}\n\t<Empty unpadded_box={true} size=\"large\"><ImageIcon /></Empty>\n{:else}\n\t<div class=\"gallery-container\" bind:this={image_container}>\n\t\t{#if selected_media && allow_preview}\n\t\t\t<button\n\t\t\t\ton:keydown={on_keydown}\n\t\t\t\tclass=\"preview\"\n\t\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\t>\n\t\t\t\t<IconButtonWrapper\n\t\t\t\t\tdisplay_top_corner={display_icon_button_wrapper_top_corner}\n\t\t\t\t>\n\t\t\t\t\t{#if show_download_button}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Download}\n\t\t\t\t\t\t\tlabel={i18n(\"common.download\")}\n\t\t\t\t\t\t\ton:click={() => {\n\t\t\t\t\t\t\t\tconst image =\n\t\t\t\t\t\t\t\t\t\"image\" in selected_media\n\t\t\t\t\t\t\t\t\t\t? selected_media?.image\n\t\t\t\t\t\t\t\t\t\t: selected_media?.video;\n\t\t\t\t\t\t\t\tif (image == null) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconst { url, orig_name } = image;\n\t\t\t\t\t\t\t\tif (url) {\n\t\t\t\t\t\t\t\t\tdownload(url, orig_name ?? \"image\");\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_fullscreen_button}\n\t\t\t\t\t\t<FullscreenButton container={image_container} />\n\t\t\t\t\t{/if}\n\n\t\t\t\t\t{#if show_share_button}\n\t\t\t\t\t\t<div class=\"icon-button\">\n\t\t\t\t\t\t\t<ShareButton\n\t\t\t\t\t\t\t\t{i18n}\n\t\t\t\t\t\t\t\ton:share\n\t\t\t\t\t\t\t\ton:error\n\t\t\t\t\t\t\t\tvalue={resolved_value}\n\t\t\t\t\t\t\t\tformatter={format_gallery_for_sharing}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t{/if}\n\t\t\t\t\t{#if !is_full_screen}\n\t\t\t\t\t\t<IconButton\n\t\t\t\t\t\t\tIcon={Clear}\n\t\t\t\t\t\t\tlabel=\"Close\"\n\t\t\t\t\t\t\ton:click={() => (selected_index = null)}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</IconButtonWrapper>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"media-button\"\n\t\t\t\t\ton:click={\"image\" in selected_media\n\t\t\t\t\t\t? (event) => handle_preview_click(event)\n\t\t\t\t\t\t: null}\n\t\t\t\t\tstyle=\"height: calc(100% - {selected_media.caption\n\t\t\t\t\t\t? '80px'\n\t\t\t\t\t\t: '60px'})\"\n\t\t\t\t\taria-label=\"detailed view of selected image\"\n\t\t\t\t>\n\t\t\t\t\t{#if \"image\" in selected_media}\n\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\tdata-testid=\"detailed-image\"\n\t\t\t\t\t\t\tsrc={selected_media.image.url}\n\t\t\t\t\t\t\talt={selected_media.caption || \"\"}\n\t\t\t\t\t\t\ttitle={selected_media.caption || null}\n\t\t\t\t\t\t\tclass={selected_media.caption && \"with-caption\"}\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t{:else}\n\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\tsrc={selected_media.video.url}\n\t\t\t\t\t\t\tdata-testid={\"detailed-video\"}\n\t\t\t\t\t\t\talt={selected_media.caption || \"\"}\n\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\tmuted={false}\n\t\t\t\t\t\t\tcontrols={true}\n\t\t\t\t\t\t/>\n\t\t\t\t\t{/if}\n\t\t\t\t</button>\n\t\t\t\t{#if selected_media?.caption}\n\t\t\t\t\t<caption class=\"caption\">\n\t\t\t\t\t\t{selected_media.caption}\n\t\t\t\t\t</caption>\n\t\t\t\t{/if}\n\t\t\t\t<div\n\t\t\t\t\tbind:this={container_element}\n\t\t\t\t\tclass=\"thumbnails scroll-hide\"\n\t\t\t\t\tdata-testid=\"container_el\"\n\t\t\t\t>\n\t\t\t\t\t{#each resolved_value as media, i}\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tbind:this={el[i]}\n\t\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-small\"\n\t\t\t\t\t\t\tclass:selected={selected_index === i && mode !== \"minimal\"}\n\t\t\t\t\t\t\taria-label={\"Thumbnail \" +\n\t\t\t\t\t\t\t\t(i + 1) +\n\t\t\t\t\t\t\t\t\" of \" +\n\t\t\t\t\t\t\t\tresolved_value.length}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{#if \"image\" in media}\n\t\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\t\tsrc={media.image.url}\n\t\t\t\t\t\t\t\t\ttitle={media.caption || null}\n\t\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t\t<Play />\n\t\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\t\tsrc={media.video.url}\n\t\t\t\t\t\t\t\t\ttitle={media.caption || null}\n\t\t\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t{/each}\n\t\t\t\t</div>\n\t\t\t</button>\n\t\t{/if}\n\n\t\t<div\n\t\t\tclass=\"grid-wrap\"\n\t\t\tclass:minimal={mode === \"minimal\"}\n\t\t\tclass:fixed-height={mode !== \"minimal\" && (!height || height == \"auto\")}\n\t\t\tclass:hidden={is_full_screen}\n\t\t>\n\t\t\t{#if interactive && selected_index === null}\n\t\t\t\t<ModifyUpload {i18n} on:clear={() => (value = [])} />\n\t\t\t{/if}\n\t\t\t<div\n\t\t\t\tclass=\"grid-container\"\n\t\t\t\tstyle=\"--grid-cols:{columns}; --grid-rows:{rows}; --object-fit: {object_fit}; height: {height};\"\n\t\t\t\tclass:pt-6={show_label}\n\t\t\t>\n\t\t\t\t{#each resolved_value as entry, i}\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumbnail-item thumbnail-lg\"\n\t\t\t\t\t\tclass:selected={selected_index === i}\n\t\t\t\t\t\ton:click={() => (selected_index = i)}\n\t\t\t\t\t\taria-label={\"Thumbnail \" + (i + 1) + \" of \" + resolved_value.length}\n\t\t\t\t\t>\n\t\t\t\t\t\t{#if \"image\" in entry}\n\t\t\t\t\t\t\t<Image\n\t\t\t\t\t\t\t\talt={entry.caption || \"\"}\n\t\t\t\t\t\t\t\tsrc={typeof entry.image === \"string\"\n\t\t\t\t\t\t\t\t\t? entry.image\n\t\t\t\t\t\t\t\t\t: entry.image.url}\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{:else}\n\t\t\t\t\t\t\t<Play />\n\t\t\t\t\t\t\t<Video\n\t\t\t\t\t\t\t\tsrc={entry.video.url}\n\t\t\t\t\t\t\t\ttitle={entry.caption || null}\n\t\t\t\t\t\t\t\tis_stream={false}\n\t\t\t\t\t\t\t\tdata-testid={\"thumbnail \" + (i + 1)}\n\t\t\t\t\t\t\t\talt=\"\"\n\t\t\t\t\t\t\t\tloading=\"lazy\"\n\t\t\t\t\t\t\t\tloop={false}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t\t{#if entry.caption}\n\t\t\t\t\t\t\t<div class=\"caption-label\">\n\t\t\t\t\t\t\t\t{entry.caption}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t{/if}\n\t\t\t\t\t</button>\n\t\t\t\t{/each}\n\t\t\t</div>\n\t\t</div>\n\t</div>\n{/if}\n\n<style lang=\"postcss\">\n\t.image-container {\n\t\theight: 100%;\n\t\tposition: relative;\n\t}\n\t.image-container :global(img),\n\tbutton {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t\tdisplay: block;\n\t\tborder-radius: var(--radius-lg);\n\t}\n\n\t.preview {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tflex-direction: column;\n\t\tz-index: var(--layer-2);\n\t\tborder-radius: calc(var(--block-radius) - var(--block-border-width));\n\t\t-webkit-backdrop-filter: blur(8px);\n\t\tbackdrop-filter: blur(8px);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal {\n\t\twidth: fit-content;\n\t\theight: fit-content;\n\t}\n\n\t.preview::before {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\tz-index: var(--layer-below);\n\t\tbackground: var(--background-fill-primary);\n\t\topacity: 0.9;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\n\t.fixed-height {\n\t\tmin-height: var(--size-80);\n\t\tmax-height: 55vh;\n\t}\n\n\t@media (--screen-xl) {\n\t\t.fixed-height {\n\t\t\tmin-height: 450px;\n\t\t}\n\t}\n\n\t.media-button {\n\t\theight: calc(100% - 60px);\n\t\twidth: 100%;\n\t\tdisplay: flex;\n\t}\n\t.media-button :global(img),\n\t.media-button :global(video) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\tobject-fit: contain;\n\t}\n\t.thumbnails :global(img) {\n\t\tobject-fit: cover;\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t}\n\t.thumbnails :global(svg) {\n\t\tposition: absolute;\n\t\ttop: var(--size-2);\n\t\tleft: var(--size-2);\n\t\twidth: 50%;\n\t\theight: 50%;\n\t\topacity: 50%;\n\t}\n\t.preview :global(img.with-caption) {\n\t\theight: var(--size-full);\n\t}\n\n\t.preview.minimal :global(img.with-caption) {\n\t\theight: auto;\n\t}\n\n\t.selectable {\n\t\tcursor: crosshair;\n\t}\n\n\t.caption {\n\t\tpadding: var(--size-2) var(--size-3);\n\t\toverflow: hidden;\n\t\tcolor: var(--block-label-text-color);\n\t\tfont-weight: var(--weight-semibold);\n\t\ttext-align: center;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t\talign-self: center;\n\t}\n\n\t.thumbnails {\n\t\tdisplay: flex;\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tgap: var(--spacing-lg);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-14);\n\t\toverflow-x: scroll;\n\t}\n\n\t.thumbnail-item {\n\t\t--ring-color: transparent;\n\t\tposition: relative;\n\t\tbox-shadow:\n\t\t\tinset 0 0 0 1px var(--ring-color),\n\t\t\tvar(--shadow-drop);\n\t\tborder: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--button-small-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\taspect-ratio: var(--ratio-square);\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: clip;\n\t}\n\n\t.thumbnail-item:hover {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t\tfilter: brightness(1.1);\n\t}\n\n\t.thumbnail-item.selected {\n\t\t--ring-color: var(--color-accent);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-item :global(svg) {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\twidth: 50%;\n\t\theight: 50%;\n\t\topacity: 50%;\n\t\ttransform: translate(-50%, -50%);\n\t}\n\n\t.thumbnail-item :global(video) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: cover;\n\t}\n\n\t.thumbnail-small {\n\t\tflex: none;\n\t\ttransform: scale(0.9);\n\t\ttransition: 0.075s;\n\t\twidth: var(--size-9);\n\t\theight: var(--size-9);\n\t}\n\t.thumbnail-small.selected {\n\t\t--ring-color: var(--color-accent);\n\t\ttransform: scale(1);\n\t\tborder-color: var(--color-accent);\n\t}\n\n\t.thumbnail-small > img {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.grid-wrap {\n\t\tposition: relative;\n\t\tpadding: var(--size-2);\n\t\theight: var(--size-full);\n\t\toverflow-y: scroll;\n\t}\n\n\t.grid-container {\n\t\tdisplay: grid;\n\t\tposition: relative;\n\t\tgrid-template-rows: repeat(var(--grid-rows), minmax(100px, 1fr));\n\t\tgrid-template-columns: repeat(var(--grid-cols), minmax(100px, 1fr));\n\t\tgrid-auto-rows: minmax(100px, 1fr);\n\t\tgap: var(--spacing-lg);\n\t}\n\n\t.thumbnail-lg > :global(img) {\n\t\twidth: var(--size-full);\n\t\theight: var(--size-full);\n\t\toverflow: hidden;\n\t\tobject-fit: var(--object-fit);\n\t}\n\n\t.thumbnail-lg:hover .caption-label {\n\t\topacity: 0.5;\n\t}\n\n\t.caption-label {\n\t\tposition: absolute;\n\t\tright: var(--block-label-margin);\n\t\tbottom: var(--block-label-margin);\n\t\tz-index: var(--layer-1);\n\t\tborder-top: 1px solid var(--border-color-primary);\n\t\tborder-left: 1px solid var(--border-color-primary);\n\t\tborder-radius: var(--block-label-radius);\n\t\tbackground: var(--background-fill-secondary);\n\t\tpadding: var(--block-label-padding);\n\t\tmax-width: 80%;\n\t\toverflow: hidden;\n\t\tfont-size: var(--block-label-text-size);\n\t\ttext-align: left;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.grid-wrap.minimal {\n\t\tpadding: 0;\n\t}\n</style>\n"], "names": ["has", "find", "iter", "tar", "key", "dequal", "foo", "bar", "ctor", "len", "tmp", "format_gallery_for_sharing", "value", "image", "_", "uploadToHuggingFace", "url", "createEventDispatcher", "onMount", "ImageIcon", "ctx", "dirty", "blocklabel_changes", "if_block0", "create_if_block_4", "create_if_block_3", "i", "toggle_class", "div1", "insert", "target", "div2", "anchor", "append", "div0", "each_blocks", "if_block1", "create_if_block_6", "set_style", "button0", "button1", "div", "is_function", "Download", "iconbutton_changes", "Clear", "create_if_block_11", "create_if_block_10", "create_if_block_9", "create_if_block_8", "t_value", "caption", "set_data", "button", "create_if_block_1", "create_if_block_12", "show_label", "$$props", "label", "columns", "rows", "height", "preview", "allow_preview", "object_fit", "show_share_button", "show_download_button", "i18n", "selected_index", "interactive", "_fetch", "mode", "show_fullscreen_button", "display_icon_button_wrapper_top_corner", "is_full_screen", "image_container", "dispatch", "was_reset", "resolved_value", "prev_value", "old_selected_index", "handle_preview_click", "event", "element", "x", "centerX", "previous", "next", "on_keydown", "e", "el", "container_element", "scroll_to_img", "index", "tick", "container_left", "container_width", "left", "width", "pos", "window_height", "download", "file_url", "name", "response", "error", "blob", "link", "$$invalidate", "selected_media", "orig_name", "click_handler_1", "$$value", "click_handler_3", "click_handler_4", "data"], "mappings": "gqCAAA,IAAIA,GAAM,OAAO,UAAU,eAE3B,SAASC,GAAKC,EAAMC,EAAKC,EAAK,CAC7B,IAAKA,KAAOF,EAAK,OAChB,GAAIG,EAAOD,EAAKD,CAAG,EAAG,OAAOC,CAE/B,CAEO,SAASC,EAAOC,EAAKC,EAAK,CAChC,IAAIC,EAAMC,EAAKC,EACf,GAAIJ,IAAQC,EAAK,MAAO,GAExB,GAAID,GAAOC,IAAQC,EAAKF,EAAI,eAAiBC,EAAI,YAAa,CAC7D,GAAIC,IAAS,KAAM,OAAOF,EAAI,YAAcC,EAAI,UAChD,GAAIC,IAAS,OAAQ,OAAOF,EAAI,aAAeC,EAAI,WAEnD,GAAIC,IAAS,MAAO,CACnB,IAAKC,EAAIH,EAAI,UAAYC,EAAI,OAC5B,KAAOE,KAASJ,EAAOC,EAAIG,CAAG,EAAGF,EAAIE,CAAG,CAAC,GAAE,CAE5C,OAAOA,IAAQ,EACf,CAED,GAAID,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EACFC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACH,EAAI,IAAIG,CAAG,EAAG,MAAO,GAE3B,MAAO,EACP,CAED,GAAIF,IAAS,IAAK,CACjB,GAAIF,EAAI,OAASC,EAAI,KACpB,MAAO,GAER,IAAKE,KAAOH,EAMX,GALAI,EAAMD,EAAI,CAAC,EACPC,GAAO,OAAOA,GAAQ,WACzBA,EAAMT,GAAKM,EAAKG,CAAG,EACf,CAACA,IAEF,CAACL,EAAOI,EAAI,CAAC,EAAGF,EAAI,IAAIG,CAAG,CAAC,EAC/B,MAAO,GAGT,MAAO,EACP,CAED,GAAIF,IAAS,YACZF,EAAM,IAAI,WAAWA,CAAG,EACxBC,EAAM,IAAI,WAAWA,CAAG,UACdC,IAAS,SAAU,CAC7B,IAAKC,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAI,QAAQG,CAAG,IAAMF,EAAI,QAAQE,CAAG,GAAE,CAEvD,OAAOA,IAAQ,EACf,CAED,GAAI,YAAY,OAAOH,CAAG,EAAG,CAC5B,IAAKG,EAAIH,EAAI,cAAgBC,EAAI,WAChC,KAAOE,KAASH,EAAIG,CAAG,IAAMF,EAAIE,CAAG,GAAE,CAEvC,OAAOA,IAAQ,EACf,CAED,GAAI,CAACD,GAAQ,OAAOF,GAAQ,SAAU,CACrCG,EAAM,EACN,IAAKD,KAAQF,EAEZ,GADIN,GAAI,KAAKM,EAAKE,CAAI,GAAK,EAAEC,GAAO,CAACT,GAAI,KAAKO,EAAKC,CAAI,GACnD,EAAEA,KAAQD,IAAQ,CAACF,EAAOC,EAAIE,CAAI,EAAGD,EAAIC,CAAI,CAAC,EAAG,MAAO,GAE7D,OAAO,OAAO,KAAKD,CAAG,EAAE,SAAWE,CACnC,CACD,CAED,OAAOH,IAAQA,GAAOC,IAAQA,CAC/B,CChFA,eAAsBI,GACrBC,EACkB,CAClB,OAAKA,EAQE,2DAPI,MAAM,QAAQ,IACxBA,EAAM,IAAI,MAAO,CAACC,EAAOC,CAAC,IACrBD,IAAU,MAAQ,CAACA,EAAM,IAAY,GAClC,MAAME,GAAoBF,EAAM,GAAU,CACjD,CAAA,GAIA,IAAKG,GAAQ,aAAaA,CAAG,4BAA4B,EACzD,KAAK,EAAE,CAAC,SAVS,EAWpB,meCHU,CAAA,sBAAAC,GAAA,QAAAC,EAAsC,EAAA,OAAA,mCAC1B,EAAA,OAAA,yOA4NUC,GAAkB,MAAAC,MAAS,wGAATC,EAAA,CAAA,EAAA,IAAAC,EAAA,MAAAF,MAAS,oIAMpDG,EAAAH,OAAkBA,EAAa,CAAA,GAAAI,GAAAJ,CAAA,IAyI9BA,EAAW,EAAA,GAAIA,EAAc,CAAA,IAAK,MAAIK,GAAAL,CAAA,OAQnCA,EAAc,EAAA,CAAA,uBAAnB,OAAIM,GAAA,yOAHcN,EAAO,CAAA,CAAA,oBAAgBA,EAAI,CAAA,CAAA,qBAAkBA,EAAU,CAAA,CAAA,eAAYA,EAAM,CAAA,CAAA,aACjFA,EAAU,CAAA,CAAA,yCAVRO,EAAAC,EAAA,UAAAR,QAAS,SAAS,EACbO,EAAAC,EAAA,eAAAR,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,eACxDA,EAAc,EAAA,CAAA,2CAxI9BS,EAsLKC,EAAAC,EAAAC,CAAA,wBAlDJC,EAiDKF,EAAAH,CAAA,wBAxCJK,EAuCKL,EAAAM,CAAA,4EAnLDd,OAAkBA,EAAa,CAAA,uGAyI9BA,EAAW,EAAA,GAAIA,EAAc,CAAA,IAAK,wHAQ/BA,EAAc,EAAA,CAAA,oBAAnB,OAAIM,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,4CAHcN,EAAO,CAAA,CAAA,mCAAgBA,EAAI,CAAA,CAAA,qCAAkBA,EAAU,CAAA,CAAA,8BAAYA,EAAM,CAAA,CAAA,2BACjFA,EAAU,CAAA,CAAA,mBAVRO,EAAAC,EAAA,UAAAR,QAAS,SAAS,mBACbO,EAAAC,EAAA,eAAAR,QAAS,YAAS,CAAMA,EAAU,CAAA,GAAAA,MAAU,OAAM,kCACxDA,EAAc,EAAA,CAAA,yCAUzB,OAAIM,GAAA,+MApJY,4UAUGN,EAAsC,EAAA,iFAuDrD,MAAA,UAAWA,EAAc,EAAA,EAAA,0BAsB1B,IAAAgB,EAAAhB,OAAgB,SAAOiB,GAAAjB,CAAA,OAUpBA,EAAc,EAAA,CAAA,uBAAnB,OAAIM,GAAA,+OArCsBY,EAAAC,EAAA,SAAA,gBAAAnB,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,iLAvDKO,EAAAa,EAAA,UAAApB,QAAS,SAAS,UAHlCS,EA+HQC,EAAAU,EAAAR,CAAA,qBA5EPC,EA+BQO,EAAAD,CAAA,8CAMRN,EAsCKO,EAAAC,CAAA,+FAzEMC,GAAA,UAAWtB,EAAA,EAAA,QAElB,IAAI,IAFG,UAAWA,EAAA,EAAA,QAElB,MAAI,MAAA,KAAA,SAAA,kBAtDIA,EAAU,EAAA,CAAA,kEAKDA,EAAsC,EAAA,8NAkD9BkB,EAAAC,EAAA,SAAA,gBAAAnB,EAAe,EAAA,EAAA,QACxC,OACA,QAAM,GAAA,EAyBLA,OAAgB,iFAUbA,EAAc,EAAA,CAAA,oBAAnB,OAAIM,GAAA,EAAA,2GAAJ,OAAIA,EAAAS,EAAA,OAAAT,GAAA,4BA1FQC,EAAAa,EAAA,UAAApB,QAAS,SAAS,uDA0F9B,OAAIM,GAAA,qOAnFEiB,GACC,MAAAvB,MAAK,iBAAiB,yFAAtBC,EAAA,CAAA,EAAA,OAAAuB,EAAA,MAAAxB,MAAK,iBAAiB,sJAkBDA,EAAe,EAAA,CAAA,6FAAfA,EAAe,EAAA,+JASnCA,EAAc,EAAA,YACVT,oHANbkB,EAQKC,EAAAW,EAAAT,CAAA,qFAHIZ,EAAc,EAAA,uJAOhByB,GAAK,MAAA,OAAA,CAAA,CAAA,uLArCRzB,EAAoB,EAAA,GAAA0B,GAAA1B,CAAA,IAoBpBA,EAAsB,EAAA,GAAA2B,GAAA3B,CAAA,IAItBA,EAAiB,CAAA,GAAA4B,GAAA5B,CAAA,KAWhBA,EAAc,EAAA,GAAA6B,GAAA7B,CAAA,mLAnCfA,EAAoB,EAAA,+GAoBpBA,EAAsB,EAAA,gHAItBA,EAAiB,CAAA,8GAWhBA,EAAc,EAAA,gTA6BbA,EAAc,EAAA,EAAC,MAAM,kBACb,qBACRA,EAAc,EAAA,EAAC,SAAW,uBAEzB,aACK,SACJ,YACG,0FAPLA,EAAc,EAAA,EAAC,MAAM,0BAErBA,EAAc,EAAA,EAAC,SAAW,gLAV1BA,EAAc,EAAA,EAAC,MAAM,QACrBA,EAAc,EAAA,EAAC,SAAW,SACxBA,EAAc,EAAA,EAAC,SAAW,WAC1BA,EAAc,EAAA,EAAC,SAAW,qHAH5BA,EAAc,EAAA,EAAC,MAAM,0BACrBA,EAAc,EAAA,EAAC,SAAW,2BACxBA,EAAc,EAAA,EAAC,SAAW,6BAC1BA,EAAc,EAAA,EAAC,SAAW,+HAkBjC8B,EAAA9B,MAAe,QAAO,oFADxBS,EAESC,EAAAqB,EAAAnB,CAAA,iBADPX,EAAA,CAAA,EAAA,SAAA6B,KAAAA,EAAA9B,MAAe,QAAO,KAAAgC,GAAA,EAAAF,CAAA,sFA8Bf9B,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,eACb,iBACE,cAAgBA,EAAC,EAAA,EAAG,8BAG3B,iIANDA,EAAK,EAAA,EAAC,MAAM,0BACVA,EAAK,EAAA,EAAC,SAAW,wMAVnBA,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,mBACX,cAAgBA,EAAC,EAAA,EAAG,8GAF5BA,EAAK,EAAA,EAAC,MAAM,0BACVA,EAAK,EAAA,EAAC,SAAW,gLAHrB,MAAA,UAAWA,EAAK,EAAA,EAAA,0NALT,cACVA,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,MAAM,EAJNO,EAAA0B,EAAA,WAAAjC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,UAJ3DS,EA8BQC,EAAAuB,EAAArB,CAAA,gOAzBK,cACVZ,EAAI,EAAA,EAAA,GACL,OACAA,EAAc,EAAA,EAAC,4EAJAO,EAAA0B,EAAA,WAAAjC,EAAmB,CAAA,IAAAA,EAAK,EAAA,GAAAA,QAAS,SAAS,sZAgEpDA,EAAK,EAAA,EAAC,MAAM,UACVA,EAAK,EAAA,EAAC,SAAW,eACb,iBACE,cAAgBA,EAAC,EAAA,EAAG,8BAG3B,iIANDA,EAAK,EAAA,EAAC,MAAM,0BACVA,EAAK,EAAA,EAAC,SAAW,wMAVnBA,EAAK,EAAA,EAAC,SAAW,cACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,wGAHVA,EAAK,EAAA,EAAC,SAAW,8BACVA,EAAK,EAAA,EAAC,OAAU,SACzBA,EAAM,EAAA,EAAA,MACNA,EAAK,EAAA,EAAC,MAAM,oHAiBd8B,EAAA9B,MAAM,QAAO,sFADfS,EAEKC,EAAAW,EAAAT,CAAA,iBADHX,EAAA,CAAA,EAAA,OAAA6B,KAAAA,EAAA9B,MAAM,QAAO,KAAAgC,GAAA,EAAAF,CAAA,4FAtBX,MAAA,UAAW9B,EAAK,EAAA,EAAA,0BAoBhB,IAAAgB,EAAAhB,MAAM,SAAOkC,GAAAlC,CAAA,mKAtBN,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,MAAM,EAFnDO,EAAA0B,EAAA,WAAAjC,OAAmBA,EAAC,EAAA,CAAA,UAFrCS,EA+BQC,EAAAuB,EAAArB,CAAA,0NALFZ,MAAM,uFAtBC,cAAgBA,EAAI,EAAA,EAAA,GAAK,OAASA,EAAc,EAAA,EAAC,4CAF7CO,EAAA0B,EAAA,WAAAjC,OAAmBA,EAAC,EAAA,CAAA,0SA3JrCA,EAAU,CAAA,GAAAmC,GAAAnC,CAAA,8CAGVA,EAAK,CAAA,GAAI,MAAQA,EAAc,EAAA,GAAI,MAAQA,EAAc,EAAA,EAAC,SAAW,EAAC,kKAHtEA,EAAU,CAAA,wXA1MH,WAAAoC,EAAa,EAAA,EAAAC,EACb,CAAA,MAAAC,CAAA,EAAAD,GACA,MAAA7C,EAA8B,IAAA,EAAA6C,EAC9B,CAAA,QAAAE,EAAA,CAA0C,CAAC,CAAA,EAAAF,GAC3C,KAAAG,EAAsC,MAAA,EAAAH,GACtC,OAAAI,EAA0B,MAAA,EAAAJ,EAC1B,CAAA,QAAAK,CAAA,EAAAL,GACA,cAAAM,EAAgB,EAAA,EAAAN,GAChB,WAAAO,EACV,OAAA,EAAAP,GACU,kBAAAQ,EAAoB,EAAA,EAAAR,GACpB,qBAAAS,EAAuB,EAAA,EAAAT,EACvB,CAAA,KAAAU,CAAA,EAAAV,GACA,eAAAW,EAAgC,IAAA,EAAAX,EAChC,CAAA,YAAAY,CAAA,EAAAZ,EACA,CAAA,OAAAa,CAAA,EAAAb,GACA,KAAAc,EAA6B,QAAA,EAAAd,GAC7B,uBAAAe,EAAyB,EAAA,EAAAf,GACzB,uCAAAgB,EAAyC,EAAA,EAAAhB,EAEhDiB,EAAiB,GACjBC,QAEEC,GAAW3D,SAMb4D,EAAY,GAIZC,EAAuC,KAiBvCC,GAAmCnE,EACnCwD,GAAkB,MAAQN,GAAWlD,GAAO,SAC/CwD,EAAiB,OAEdY,GAAoCZ,WAyB/Ba,GAAqBC,EAAA,CACvB,MAAAC,EAAUD,EAAM,OAChBE,EAAIF,EAAM,QAEVG,EADQF,EAAQ,YACE,EAEpBC,EAAIC,MACPjB,EAAiBkB,CAAA,MAEjBlB,EAAiBmB,CAAA,WAIVC,GAAWC,EAAA,QACXA,EAAE,KAAA,CACJ,IAAA,SACJA,EAAE,eAAA,MACFrB,EAAiB,IAAA,QAEb,IAAA,YACJqB,EAAE,eAAA,MACFrB,EAAiBkB,CAAA,QAEb,IAAA,aACJG,EAAE,eAAA,MACFrB,EAAiBmB,CAAA,SAuBhB,IAAAG,EAAA,CAAA,EACAC,iBAEWC,GAAcC,EAAA,CAIxB,UAHOA,GAAU,WACf,MAAAC,GAAA,EAEFJ,EAAGG,CAAK,IAAM,QAAA,OAElBH,EAAGG,CAAK,GAAG,cAEH,KAAME,EAAgB,MAAOC,GACpCL,EAAkB,yBACX,KAAAM,EAAM,MAAAC,CAAA,EAAUR,EAAGG,CAAK,EAAE,wBAI5BM,EAFgBF,EAAOF,EAI5BG,EAAQ,EACRF,EAAkB,EAClBL,EAAkB,WAEfA,GAAA,OAA4BA,EAAkB,UAAa,YAC9DA,EAAkB,SAAA,CACjB,KAAMQ,EAAM,EAAI,EAAIA,EACpB,SAAU,eAKTC,GAAgB,EAML,eAAAC,GAASC,EAAkBC,EAAA,CACrC,IAAAC,MAEHA,EAAA,MAAiBlC,EAAOgC,CAAQ,CACxB,OAAAG,EAAA,IACJA,aAAiB,UAAA,CAIpB,OAAO,KAAKH,EAAU,SAAU,YAAY,SAIvC,MAAAG,EAED,MAAAC,EAAA,MAAaF,EAAS,OACtBxF,EAAM,IAAI,gBAAgB0F,CAAI,EAC9BC,GAAO,SAAS,cAAc,GAAG,EACvCA,GAAK,KAAO3F,EACZ2F,GAAK,SAAWJ,EAChBI,GAAK,MAAA,EACL,IAAI,gBAAgB3F,CAAG,EAQxBE,GAAA,IAAA,CACC,SAAS,iBAAiB,mBAAA,IAAA,CACzB0F,EAAA,GAAAlC,EAAA,CAAA,CAAmB,SAAS,iBAAA,gEA4BjB7D,EACL,UAAWgG,EACRA,GAAgB,MAChBA,GAAgB,MAChB,GAAAhG,GAAS,kBAGL,IAAAG,EAAK,UAAA8F,CAAS,EAAKjG,EACvBG,GACHqF,GAASrF,EAAK8F,GAAa,OAAO,qEAyBnB,MAAAC,GAAA,IAAAH,EAAA,EAAAxC,EAAiB,IAAI,KAOpCc,GAAUD,GAAqBC,CAAK,+CAyC3BQ,EAAGhE,CAAC,EAAAsF,YACE,MAAAC,GAAAvF,GAAAkF,EAAA,EAAAxC,EAAiB1C,CAAC,6CAP1BiE,EAAiBqB,6BAgDSpG,EAAK,CAAA,CAAA,EAWxBsG,GAAAxF,GAAAkF,EAAA,EAAAxC,EAAiB1C,CAAC,6CAtJEiD,EAAeqC,4yBAjLzDJ,EAAA,GAAG/B,EAAYjE,GAAS,MAAQA,EAAM,SAAW,EAAI,GAAOiE,CAAA,mBAI5D+B,EAAA,GAAG9B,EACFlE,GAAS,KACN,KACCA,EAAM,IAAKuG,GACR,UAAWA,GAEb,MAAOA,EAAK,MACZ,QAASA,EAAK,SAEL,UAAWA,GACZ,MAAOA,EAAK,MAAmB,QAASA,EAAK,wCAWnD9G,EAAO0E,GAAYnE,CAAK,IAG3BiE,OACHT,EAAiBN,GAAWlD,GAAO,OAAS,EAAI,IAAA,OAChDiE,EAAY,EAAA,GAIZ+B,EAAA,EAAAxC,EACCA,GAAkB,MAAQxD,GAAS,MAAQwD,EAAiBxD,EAAM,OAC/DwD,EACA,IAAA,EAELQ,GAAS,QAAQ,OACjBG,GAAanE,CAAA,0BAGX0E,IACAlB,GAAkB,IAAMU,GAAgB,QAAU,GAAK,IACxDA,GAAgB,QAAU,0BACzBS,IAASnB,GAAkB,GAAK,IAAMU,GAAgB,QAAU,8BAmC9DV,IAAmBY,UACtBA,GAAqBZ,CAAA,EACjBA,IAAmB,MACtBQ,GAAS,SAAA,CACR,MAAOR,EACP,MAAOU,IAAiBV,CAAc,wBAMnCL,GACN6B,GAAcxB,CAAc,uBAgE1BwC,EAAA,GAAAC,EACFzC,GAAkB,MAAQU,GAAkB,KACzCA,EAAeV,CAAc,EAC7B,IAAA", "x_google_ignoreList": [0]}